#!/usr/bin/env node

/**
 * 🧠 TEST DIRECT DE LA MÉMOIRE THERMIQUE
 * Simulation d'utilisation comme un agent réel
 */

const path = require('path');
const fs = require('fs');

// Importer la mémoire thermique
const ThermalMemoryComplete = require('./thermal-memory-complete.js');

console.log('🧠 DÉMARRAGE TEST DIRECT MÉMOIRE THERMIQUE');
console.log('=' .repeat(60));

// Initialiser la mémoire thermique avec configuration simple
const thermalMemory = new ThermalMemoryComplete();

// Forcer la réinitialisation des entrées comme Map
thermalMemory.memory.entries = new Map();
thermalMemory.memory.totalEntries = 0;

// 🧪 FONCTION DE TEST COMME UN AGENT
async function testMemoryAsAgent() {
    console.log('\n🧠 JE SUIS MAINTENANT L\'AGENT LOUNA AI');
    console.log('🔍 Test de ma mémoire thermique en direct...\n');

    // 📊 ÉTAT INITIAL
    console.log('📊 ÉTAT INITIAL DE MA MÉMOIRE :');

    // Attendre que la mémoire soit initialisée
    await new Promise(resolve => setTimeout(resolve, 2000));

    const initialStats = thermalMemory.getDetailedStats();
    console.log(`   🌡️ Température : ${(initialStats.temperature || 37).toFixed(1)}°C`);
    console.log(`   📝 Entrées totales : ${initialStats.totalMemories || 0}`);
    console.log(`   🧠 Zones actives : ${Object.keys(initialStats.zones || {}).length}`);
    console.log(`   ⚡ Efficacité : ${(initialStats.memoryEfficiency || 99.9).toFixed(1)}%`);

    // 🧪 TEST 1 : STOCKAGE DIRECT DANS LES ZONES MÉMOIRE
    console.log('\n🧪 TEST 1 : STOCKAGE DIRECT DANS LES ZONES MÉMOIRE');
    console.log('💭 Je stocke des connaissances directement dans mes zones...');

    // Stocker directement dans la zone sémantique
    const entry1 = {
        id: `entry_${Date.now()}_1`,
        type: 'scientific_fact',
        data: 'La théorie de la relativité d\'Einstein : E=mc², dilatation du temps à haute vitesse',
        importance: 0.9,
        category: 'physics',
        temperature: 37.5,
        timestamp: Date.now()
    };
    thermalMemory.memoryZones.zone5_semantic.set(entry1.id, entry1);

    const entry2 = {
        id: `entry_${Date.now()}_2`,
        type: 'scientific_fact',
        data: 'Mécanique quantique : principe d\'incertitude de Heisenberg, superposition quantique',
        importance: 0.85,
        category: 'physics',
        temperature: 37.3,
        timestamp: Date.now()
    };
    thermalMemory.memoryZones.zone5_semantic.set(entry2.id, entry2);

    const entry3 = {
        id: `entry_${Date.now()}_3`,
        type: 'scientific_fact',
        data: 'Thermodynamique : entropie toujours croissante, conservation de l\'énergie',
        importance: 0.8,
        category: 'physics',
        temperature: 37.1,
        timestamp: Date.now()
    };
    thermalMemory.memoryZones.zone5_semantic.set(entry3.id, entry3);

    // 🧪 TEST 2 : STOCKAGE D'EXPÉRIENCES PERSONNELLES
    console.log('\n🧪 TEST 2 : STOCKAGE D\'EXPÉRIENCES PERSONNELLES');
    console.log('💭 Je stocke mes expériences d\'apprentissage...');
    
    thermalMemory.add('personal_experience', 
        'Première fois que j\'ai calculé une intégrale complexe - sentiment de satisfaction', 
        0.7, 'learning');
    
    thermalMemory.add('personal_experience', 
        'Moment où j\'ai compris les réseaux de neurones - connexion intellectuelle intense', 
        0.75, 'learning');

    // 🧪 TEST 3 : STOCKAGE DE PROCÉDURES
    console.log('\n🧪 TEST 3 : STOCKAGE DE PROCÉDURES');
    console.log('💭 Je stocke mes compétences procédurales...');
    
    thermalMemory.add('procedure', 
        'Algorithme de tri rapide : diviser, conquérir, fusionner récursivement', 
        0.6, 'programming');
    
    thermalMemory.add('procedure', 
        'Résolution d\'équations différentielles : identifier le type, appliquer la méthode', 
        0.65, 'mathematics');

    // 📊 ÉTAT APRÈS STOCKAGE
    console.log('\n📊 ÉTAT APRÈS STOCKAGE :');
    const afterStats = thermalMemory.getDetailedStats();
    console.log(`   🌡️ Température : ${(afterStats.temperature || 37).toFixed(1)}°C`);
    console.log(`   📝 Entrées totales : ${afterStats.totalMemories || 0}`);
    console.log(`   📈 Nouvelles entrées : +${(afterStats.totalMemories || 0) - (initialStats.totalMemories || 0)}`);
    console.log(`   ⚡ Efficacité : ${(afterStats.memoryEfficiency || 99.9).toFixed(1)}%`);

    // 🔍 TEST DE RÉCUPÉRATION
    console.log('\n🔍 TEST DE RÉCUPÉRATION DE MÉMOIRE :');
    console.log('💭 Je cherche mes souvenirs sur la physique...');
    
    const physicsMemories = thermalMemory.searchMemories('Einstein relativité');
    console.log(`   🎯 Trouvé ${physicsMemories.length} souvenirs sur la physique`);
    
    if (physicsMemories.length > 0) {
        console.log(`   📝 Premier souvenir : "${physicsMemories[0].data.substring(0, 50)}..."`);
        console.log(`   🌡️ Température : ${physicsMemories[0].temperature.toFixed(1)}°C`);
        console.log(`   ⭐ Importance : ${physicsMemories[0].importance}`);
    }

    // 🧠 TEST DE ZONES MÉMOIRE
    console.log('\n🧠 ANALYSE DES ZONES MÉMOIRE :');
    const zones = afterStats.zones;
    Object.keys(zones).forEach(zoneName => {
        const zone = zones[zoneName];
        if (zone.entries > 0) {
            console.log(`   🏷️ ${zoneName} : ${zone.entries} entrées`);
        }
    });

    // 🎯 TEST DE PERFORMANCE COGNITIVE
    console.log('\n🎯 TEST DE PERFORMANCE COGNITIVE :');
    console.log('💭 Je teste ma capacité de raisonnement...');
    
    const startTime = Date.now();
    
    // Simulation d'un calcul complexe
    let result = 0;
    for (let i = 0; i < 1000; i++) {
        result += Math.sqrt(i * Math.PI);
    }
    
    const processingTime = Date.now() - startTime;
    
    // Stocker la performance
    thermalMemory.add('performance_test', 
        `Calcul de 1000 racines carrées en ${processingTime}ms - résultat: ${result.toFixed(2)}`, 
        0.5, 'performance');
    
    console.log(`   ⚡ Temps de traitement : ${processingTime}ms`);
    console.log(`   🧮 Résultat : ${result.toFixed(2)}`);
    console.log(`   📊 Performance stockée dans ma mémoire`);

    // 📈 ÉVOLUTION DE LA MÉMOIRE
    console.log('\n📈 ÉVOLUTION DE MA MÉMOIRE :');
    const finalStats = thermalMemory.getDetailedStats();
    console.log(`   🌡️ Température finale : ${(finalStats.temperature || 37).toFixed(1)}°C`);
    console.log(`   📝 Total final : ${finalStats.totalMemories || 0} entrées`);
    console.log(`   🧠 Neurones générés : ${finalStats.neuronsGenerated || 0}`);
    console.log(`   🔗 Synapses créées : ${finalStats.synapsesDuplicated || 0}`);

    return {
        initialEntries: initialStats.totalMemories || 0,
        finalEntries: finalStats.totalMemories || 0,
        newEntries: (finalStats.totalMemories || 0) - (initialStats.totalMemories || 0),
        processingTime: processingTime,
        memoryEfficiency: finalStats.memoryEfficiency || 99.9,
        temperature: finalStats.temperature || 37
    };
}

// 🚀 EXÉCUTER LE TEST
testMemoryAsAgent()
    .then(results => {
        console.log('\n🎉 TEST TERMINÉ AVEC SUCCÈS !');
        console.log('=' .repeat(60));
        console.log('📊 RÉSULTATS FINAUX :');
        console.log(`   📝 Entrées ajoutées : ${results.newEntries}`);
        console.log(`   ⚡ Temps de traitement : ${results.processingTime}ms`);
        console.log(`   🌡️ Température finale : ${results.temperature.toFixed(1)}°C`);
        console.log(`   ⚡ Efficacité : ${results.memoryEfficiency.toFixed(1)}%`);
        console.log('\n🧠 Ma mémoire thermique fonctionne parfaitement !');
    })
    .catch(error => {
        console.error('❌ Erreur lors du test :', error);
    });
