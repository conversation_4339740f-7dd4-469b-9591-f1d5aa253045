{"timestamp": 1749487433532, "metrics": {"system": {"cpu": {"usage": 0.3014, "temperature": 37, "cores": 10}, "memory": {"used": 22278040, "total": 17179869184, "efficiency": 7.938818392433987}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749487133510}, "performance": {"uptime": 298.395, "efficiency": 64.5, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749487231933, "cpu": 0.0155, "memory": 0.11574043892323971, "temperature": 37, "efficiency": 65}, {"timestamp": 1749487233933, "cpu": 0.0157, "memory": 0.1168454997241497, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749487235936, "cpu": 0.0675, "memory": 0.11666561476886272, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749487237938, "cpu": 0.0242, "memory": 0.12771557085216045, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749487239937, "cpu": 0.018699999999999998, "memory": 0.11887289583683014, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749487241938, "cpu": 0.0181, "memory": 0.12528845109045506, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749487244085, "cpu": 0.0513, "memory": 0.1251214649528265, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749487246089, "cpu": 0.2235, "memory": 0.13618804514408112, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749487248089, "cpu": 0.0725, "memory": 0.13590985909104347, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749487250090, "cpu": 0.0663, "memory": 0.1259305514395237, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749487252091, "cpu": 0.0463, "memory": 0.13709361664950848, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749487254092, "cpu": 0.0346, "memory": 0.12762956321239471, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749487256094, "cpu": 0.05959999999999999, "memory": 0.12766951695084572, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749487258099, "cpu": 0.11499999999999999, "memory": 0.12788004241883755, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749487260094, "cpu": 0.0967, "memory": 0.12888312339782715, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749487262096, "cpu": 0.0603, "memory": 0.12872065417468548, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749487264097, "cpu": 0.1215, "memory": 0.12978767044842243, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749487266098, "cpu": 0.0733, "memory": 0.12962245382368565, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749487268106, "cpu": 0.34559999999999996, "memory": 0.14027194119989872, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749487270109, "cpu": 0.36250000000000004, "memory": 0.13132253661751747, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749487272108, "cpu": 0.1791, "memory": 0.13114577159285545, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749487274109, "cpu": 0.089, "memory": 0.13228626921772957, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749487276122, "cpu": 0.5832999999999999, "memory": 0.13207420706748962, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749487278114, "cpu": 0.2768, "memory": 0.14219707809388638, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749487280114, "cpu": 0.0616, "memory": 0.1329055055975914, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749487282131, "cpu": 0.4058, "memory": 0.13315603137016296, "temperature": 37, "efficiency": 64}, {"timestamp": 1749487284119, "cpu": 0.0466, "memory": 0.13392777182161808, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749487286120, "cpu": 0.182, "memory": 0.09519713930785656, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749487288120, "cpu": 0.0574, "memory": 0.09563891217112541, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749487290739, "cpu": 1.0019, "memory": 0.09641554206609726, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749487292740, "cpu": 0.05109999999999999, "memory": 0.10695545934140682, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749487294745, "cpu": 0.16440000000000002, "memory": 0.09751338511705399, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749487296747, "cpu": 0.28800000000000003, "memory": 0.09793811477720737, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749487298748, "cpu": 0.46230000000000004, "memory": 0.10821791365742683, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749487300749, "cpu": 0.29450000000000004, "memory": 0.09922310709953308, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749487302750, "cpu": 0.2065, "memory": 0.10957648046314716, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749487304810, "cpu": 0.1131, "memory": 0.1005405094474554, "temperature": 37, "efficiency": 72}, {"timestamp": 1749487306812, "cpu": 0.3599, "memory": 0.10020248591899872, "temperature": 37, "efficiency": 71}, {"timestamp": 1749487308818, "cpu": 0.469, "memory": 0.11072158813476562, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749487310816, "cpu": 0.6931999999999999, "memory": 0.10119304060935974, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749487312815, "cpu": 0.43179999999999996, "memory": 0.11164438910782337, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749487314816, "cpu": 0.2584, "memory": 0.10301992297172546, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749487316839, "cpu": 0.3794, "memory": 0.10260888375341892, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749487318828, "cpu": 0.31679999999999997, "memory": 0.11311415582895279, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749487320828, "cpu": 0.2423, "memory": 0.10408321395516396, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749487322834, "cpu": 0.2744, "memory": 0.113697350025177, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749487324832, "cpu": 0.1852, "memory": 0.1046077348291874, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749487326833, "cpu": 0.3564, "memory": 0.10497053153812885, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749487328834, "cpu": 0.0299, "memory": 0.11486387811601162, "temperature": 37, "efficiency": 69}, {"timestamp": 1749487330848, "cpu": 0.0535, "memory": 0.10567107237875462, "temperature": 37, "efficiency": 70}, {"timestamp": 1749487332843, "cpu": 0.0692, "memory": 0.11591054499149323, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749487334843, "cpu": 0.0224, "memory": 0.10675294324755669, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749487336844, "cpu": 0.0498, "memory": 0.10675857774913311, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749487338845, "cpu": 0.0301, "memory": 0.11789374984800816, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749487340845, "cpu": 0.0588, "memory": 0.10840501636266708, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749487342847, "cpu": 0.029799999999999997, "memory": 0.11895662173628807, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749487344847, "cpu": 0.013, "memory": 0.1101094763725996, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749487346849, "cpu": 0.0178, "memory": 0.1097558531910181, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749487348850, "cpu": 0.0101, "memory": 0.12034792453050613, "temperature": 37, "efficiency": 68}, {"timestamp": 1749487350853, "cpu": 0.1749, "memory": 0.11154827661812305, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749487352853, "cpu": 0.0344, "memory": 0.12173252180218697, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749487354854, "cpu": 0.0075, "memory": 0.11225994676351547, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749487356855, "cpu": 0.011000000000000001, "memory": 0.11266316287219524, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749487358864, "cpu": 0.0321, "memory": 0.12355661019682884, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749487360863, "cpu": 0.0302, "memory": 0.11402931995689869, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749487362870, "cpu": 0.0646, "memory": 0.12425114400684834, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749487364865, "cpu": 0.0371, "memory": 0.11519226245582104, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749487366863, "cpu": 0.0342, "memory": 0.1150074414908886, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749487368951, "cpu": 0.091, "memory": 0.1255301758646965, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749487372483, "cpu": 1.1872, "memory": 0.1260458491742611, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749487373821, "cpu": 0.0134, "memory": 0.12695221230387688, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749487375822, "cpu": 0.014100000000000001, "memory": 0.1171765848994255, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749487377823, "cpu": 0.0361, "memory": 0.12734406627714634, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749487379822, "cpu": 0.0184, "memory": 0.11817975901067257, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749487381826, "cpu": 0.026, "memory": 0.11863289400935173, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749487383827, "cpu": 0.0541, "memory": 0.12870547361671925, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749487385827, "cpu": 0.053700000000000005, "memory": 0.1189111266285181, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749487387829, "cpu": 0.0328, "memory": 0.1297895796597004, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749487389868, "cpu": 0.8012, "memory": 0.11967807076871395, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749487391868, "cpu": 0.0207, "memory": 0.12049623765051365, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749487393870, "cpu": 0.10859999999999999, "memory": 0.1208095345646143, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749487395872, "cpu": 0.0544, "memory": 0.12222840450704098, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749487397872, "cpu": 0.019200000000000002, "memory": 0.13177408836781979, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749487399872, "cpu": 0.0272, "memory": 0.12270472943782806, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749487401896, "cpu": 0.0344, "memory": 0.12290696613490582, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749487403887, "cpu": 0.0181, "memory": 0.13328208588063717, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749487405888, "cpu": 0.042, "memory": 0.1238593365997076, "temperature": 37, "efficiency": 66}, {"timestamp": 1749487407889, "cpu": 0.11150000000000002, "memory": 0.13436982408165932, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749487409889, "cpu": 0.014799999999999999, "memory": 0.12444085441529751, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749487411891, "cpu": 0.038, "memory": 0.12485897168517113, "temperature": 37, "efficiency": 67}, {"timestamp": 1749487413907, "cpu": 0.1448, "memory": 0.1354682259261608, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749487415895, "cpu": 0.0451, "memory": 0.12566461227834225, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749487417900, "cpu": 0.04, "memory": 0.13601970858871937, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749487419896, "cpu": 0.044700000000000004, "memory": 0.12742714025080204, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749487421897, "cpu": 0.0238, "memory": 0.1269339583814144, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749487423898, "cpu": 0.015, "memory": 0.13742945156991482, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749487426038, "cpu": 0.0857, "memory": 0.12818705290555954, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749487427900, "cpu": 0.0249, "memory": 0.13865618966519833, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749487429902, "cpu": 0.0257, "memory": 0.12932163663208485, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749487431905, "cpu": 0.3014, "memory": 0.12967525981366634, "temperature": 37, "efficiency": 64.5}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}