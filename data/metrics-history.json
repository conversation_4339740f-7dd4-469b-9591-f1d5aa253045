{"timestamp": 1749490673820, "metrics": {"system": {"cpu": {"usage": 0.07339999999999999, "temperature": 37, "cores": 10}, "memory": {"used": 16281080, "total": 17179869184, "efficiency": 24.54682902667045}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749490373828}, "performance": {"uptime": 298.251, "efficiency": 70.2, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749490473984, "cpu": 0.0998, "memory": 0.11108727194368839, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749490475986, "cpu": 0.018799999999999997, "memory": 0.11202190071344376, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749490477985, "cpu": 0.0139, "memory": 0.11265887878835201, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749490479986, "cpu": 0.0115, "memory": 0.11066398583352566, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749490481984, "cpu": 0.014899999999999998, "memory": 0.1114309299737215, "temperature": 37, "efficiency": 65}, {"timestamp": 1749490483983, "cpu": 0.09870000000000001, "memory": 0.11353818699717522, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749490485983, "cpu": 0.012899999999999998, "memory": 0.11465647257864475, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749490487982, "cpu": 0.0127, "memory": 0.11147754266858101, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749490489982, "cpu": 0.009399999999999999, "memory": 0.11313362047076225, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749490491982, "cpu": 0.0164, "memory": 0.11392934247851372, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749490493981, "cpu": 0.0667, "memory": 0.11200010776519775, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749490495981, "cpu": 0.015899999999999997, "memory": 0.11264244094491005, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749490497982, "cpu": 0.0125, "memory": 0.11351210996508598, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749490499983, "cpu": 0.0292, "memory": 0.11546616442501545, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749490501983, "cpu": 0.0091, "memory": 0.11596232652664185, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749490503984, "cpu": 0.050600000000000006, "memory": 0.11412580497562885, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749490505983, "cpu": 0.0176, "memory": 0.1149647869169712, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749490507985, "cpu": 0.007899999999999999, "memory": 0.11550141498446465, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749490509985, "cpu": 0.0146, "memory": 0.1133881974965334, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490511986, "cpu": 0.0106, "memory": 0.1143100205808878, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490513986, "cpu": 0.0424, "memory": 0.11624637991189957, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749490515988, "cpu": 0.031599999999999996, "memory": 0.11710920371115208, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749490517988, "cpu": 0.034999999999999996, "memory": 0.11407146230340004, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749490519988, "cpu": 0.0348, "memory": 0.11932556517422199, "temperature": 37, "efficiency": 64}, {"timestamp": 1749490521988, "cpu": 0.0084, "memory": 0.1200932078063488, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749490523989, "cpu": 0.0944, "memory": 0.11868560686707497, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749490525990, "cpu": 0.019799999999999998, "memory": 0.13108882121741772, "temperature": 37, "efficiency": 65}, {"timestamp": 1749490527990, "cpu": 0.009399999999999999, "memory": 0.12424569576978683, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749490529991, "cpu": 0.0103, "memory": 0.1261647790670395, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749490531992, "cpu": 0.0115, "memory": 0.1266523264348507, "temperature": 37, "efficiency": 64}, {"timestamp": 1749490533993, "cpu": 0.061799999999999994, "memory": 0.1246647909283638, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749490535994, "cpu": 0.0073, "memory": 0.12550079263746738, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749490537995, "cpu": 0.0176, "memory": 0.1260962337255478, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749490539996, "cpu": 0.0118, "memory": 0.12823394499719143, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749490541997, "cpu": 0.0246, "memory": 0.12514390982687473, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749490543999, "cpu": 0.0861, "memory": 0.12694410979747772, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749490546000, "cpu": 0.0126, "memory": 0.12791026383638382, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749490548000, "cpu": 0.0086, "memory": 0.12878808192908764, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749490550016, "cpu": 0.030400000000000003, "memory": 0.12647947296500206, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749490552018, "cpu": 0.012199999999999999, "memory": 0.1272426452487707, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749490554019, "cpu": 0.054299999999999994, "memory": 0.12938566505908966, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749490556020, "cpu": 0.013200000000000002, "memory": 0.12600659392774105, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749490558054, "cpu": 0.11230000000000001, "memory": 0.12682792730629444, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749490560022, "cpu": 0.0168, "memory": 0.12887567281723022, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749490562023, "cpu": 0.0183, "memory": 0.12942953035235405, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749490564023, "cpu": 0.0197, "memory": 0.12758029624819756, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749490566024, "cpu": 0.0225, "memory": 0.1283974852412939, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749490568025, "cpu": 0.0196, "memory": 0.12897769920527935, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749490570027, "cpu": 0.0087, "memory": 0.13078665360808372, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749490572032, "cpu": 0.0312, "memory": 0.1276268158107996, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749490574028, "cpu": 0.0137, "memory": 0.129452021792531, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749490576029, "cpu": 0.0121, "memory": 0.13026511296629906, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749490578030, "cpu": 0.0279, "memory": 0.14101755805313587, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749490580031, "cpu": 0.012799999999999999, "memory": 0.14470466412603855, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749490582033, "cpu": 0.018799999999999997, "memory": 0.1348018180578947, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749490584033, "cpu": 0.0143, "memory": 0.13727303594350815, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749490586034, "cpu": 0.0146, "memory": 0.09492146782577038, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749490588035, "cpu": 0.015200000000000002, "memory": 0.09610620327293873, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749490590036, "cpu": 0.0157, "memory": 0.10941782966256142, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749490592037, "cpu": 0.4379, "memory": 0.1023333054035902, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749490594038, "cpu": 0.0135, "memory": 0.10423320345580578, "temperature": 37, "efficiency": 68}, {"timestamp": 1749490596040, "cpu": 0.010799999999999999, "memory": 0.11658626608550549, "temperature": 37, "efficiency": 68}, {"timestamp": 1749490598041, "cpu": 0.8408000000000001, "memory": 0.10970262810587883, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749490600042, "cpu": 0.6684, "memory": 0.11124275624752045, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749490602043, "cpu": 0.0785, "memory": 0.11297394521534443, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749490604045, "cpu": 0.4262, "memory": 0.11508176103234291, "temperature": 37, "efficiency": 67}, {"timestamp": 1749490606046, "cpu": 0.0144, "memory": 0.12409803457558155, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749490608047, "cpu": 0.0137, "memory": 0.11417046189308167, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749490610047, "cpu": 0.8702, "memory": 0.11615552939474583, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490612048, "cpu": 0.0135, "memory": 0.1179784070700407, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749490614050, "cpu": 0.0193, "memory": 0.12010973878204823, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490616051, "cpu": 0.4463, "memory": 0.1253575086593628, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490618052, "cpu": 0.0168, "memory": 0.12480411678552628, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749490620052, "cpu": 0.4791, "memory": 0.12600594200193882, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490622053, "cpu": 0.36679999999999996, "memory": 0.12610922567546368, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490624055, "cpu": 0.0177, "memory": 0.1280596014112234, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490626056, "cpu": 0.0213, "memory": 0.13976367190480232, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749490628056, "cpu": 0.4123, "memory": 0.13263560831546783, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749490630057, "cpu": 0.7618, "memory": 0.13423459604382515, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490632058, "cpu": 0.0131, "memory": 0.13390793465077877, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749490634059, "cpu": 0.3862, "memory": 0.13589332811534405, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749490636060, "cpu": 0.0406, "memory": 0.14723865315318108, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749490638060, "cpu": 0.0239, "memory": 0.14774557203054428, "temperature": 37, "efficiency": 65}, {"timestamp": 1749490640061, "cpu": 1.2195, "memory": 0.13873758725821972, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749490642063, "cpu": 0.0115, "memory": 0.13878713361918926, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749490644063, "cpu": 0.0144, "memory": 0.14025545679032803, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749490646064, "cpu": 4.0038, "memory": 0.14558960683643818, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749490648067, "cpu": 0.0172, "memory": 0.08837725035846233, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749490650068, "cpu": 0.42180000000000006, "memory": 0.09128605015575886, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749490652069, "cpu": 0.1298, "memory": 0.0915097538381815, "temperature": 37, "efficiency": 71}, {"timestamp": 1749490654070, "cpu": 0.0165, "memory": 0.09339163079857826, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749490656070, "cpu": 0.06369999999999999, "memory": 0.0940198078751564, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749490658071, "cpu": 0.165, "memory": 0.09573702700436115, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749490660072, "cpu": 0.0287, "memory": 0.09387657046318054, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749490662074, "cpu": 0.0988, "memory": 0.09439419955015182, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749490664074, "cpu": 0.14959999999999998, "memory": 0.09310850873589516, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749490666075, "cpu": 0.0631, "memory": 0.09410432539880276, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749490668076, "cpu": 0.051699999999999996, "memory": 0.09578149765729904, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749490670077, "cpu": 0.0891, "memory": 0.09365687146782875, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749490672079, "cpu": 0.07339999999999999, "memory": 0.0947683583945036, "temperature": 37, "efficiency": 70.2}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}