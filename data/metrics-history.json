{"timestamp": 1749490973810, "metrics": {"system": {"cpu": {"usage": 0.0077, "temperature": 37, "cores": 10}, "memory": {"used": 17743560, "total": 17179869184, "efficiency": 25.873993358572903}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749490373828}, "performance": {"uptime": 598.426, "efficiency": 70.6, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749490774159, "cpu": 0.0395, "memory": 0.1166375819593668, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749490776160, "cpu": 0.0327, "memory": 0.11737216264009476, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749490778160, "cpu": 0.0319, "memory": 0.11858958750963211, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490780161, "cpu": 0.0545, "memory": 0.11666277423501015, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749490782162, "cpu": 0.061700000000000005, "memory": 0.1173781231045723, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749490784164, "cpu": 0.08800000000000001, "memory": 0.11985544115304947, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490786164, "cpu": 0.0394, "memory": 0.11676684953272343, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749490788165, "cpu": 0.0476, "memory": 0.11782869696617126, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749490790166, "cpu": 0.0299, "memory": 0.11984244920313358, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490792167, "cpu": 0.049, "memory": 0.12033958919346333, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749490794168, "cpu": 0.0361, "memory": 0.11860118247568607, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490796169, "cpu": 0.0741, "memory": 0.11945697478950024, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749490798171, "cpu": 0.0305, "memory": 0.12005981989204884, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490800171, "cpu": 0.0312, "memory": 0.11814171448349953, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749490802172, "cpu": 0.0634, "memory": 0.11890102177858353, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490804173, "cpu": 0.0482, "memory": 0.12067994102835655, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490806174, "cpu": 0.0437, "memory": 0.12140022590756416, "temperature": 37, "efficiency": 66}, {"timestamp": 1749490808175, "cpu": 0.0499, "memory": 0.11823414824903011, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749490810176, "cpu": 0.0888, "memory": 0.1199173741042614, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749490812178, "cpu": 0.0366, "memory": 0.12065530754625797, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749490814179, "cpu": 0.05959999999999999, "memory": 0.11868230067193508, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749490816179, "cpu": 0.044000000000000004, "memory": 0.11942791752517223, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749490818180, "cpu": 0.0379, "memory": 0.12030932120978832, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749490820183, "cpu": 0.049600000000000005, "memory": 0.12208246625959873, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490822183, "cpu": 0.0904, "memory": 0.12264354154467583, "temperature": 37, "efficiency": 66}, {"timestamp": 1749490824184, "cpu": 0.0349, "memory": 0.12077605351805687, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749490826185, "cpu": 0.0308, "memory": 0.12568123638629913, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749490828186, "cpu": 0.0378, "memory": 0.12620072811841965, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749490830187, "cpu": 0.0349, "memory": 0.12808055616915226, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490832188, "cpu": 0.0336, "memory": 0.12881453149020672, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490834189, "cpu": 0.0437, "memory": 0.12644995003938675, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490836190, "cpu": 0.0333, "memory": 0.1274440437555313, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749490838190, "cpu": 0.4368, "memory": 0.1282920129597187, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490840191, "cpu": 0.030400000000000003, "memory": 0.12997593730688095, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490842192, "cpu": 0.05550000000000001, "memory": 0.13070004060864449, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749490844193, "cpu": 0.0308, "memory": 0.12885513715445995, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490846194, "cpu": 0.0286, "memory": 0.12941607274115086, "temperature": 37, "efficiency": 66}, {"timestamp": 1749490848195, "cpu": 0.0351, "memory": 0.13016322627663612, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490850196, "cpu": 0.0366, "memory": 0.1279046293348074, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749490852197, "cpu": 0.0324, "memory": 0.1284005120396614, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749490854198, "cpu": 0.0369, "memory": 0.13050008565187454, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749490856198, "cpu": 0.0375, "memory": 0.13118553906679153, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749490858199, "cpu": 0.0268, "memory": 0.12791040353477, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749490860198, "cpu": 0.030899999999999997, "memory": 0.1298349816352129, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749490862201, "cpu": 0.0448, "memory": 0.13052118010818958, "temperature": 37, "efficiency": 66}, {"timestamp": 1749490864201, "cpu": 0.0106, "memory": 0.1285587903112173, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749490866202, "cpu": 0.0063, "memory": 0.12927725911140442, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749490868203, "cpu": 0.0060999999999999995, "memory": 0.13009333051741123, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490870204, "cpu": 0.0091, "memory": 0.13166028074920177, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490872205, "cpu": 0.0081, "memory": 0.13238186948001385, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749490874207, "cpu": 0.0481, "memory": 0.1304232981055975, "temperature": 37, "efficiency": 66}, {"timestamp": 1749490876207, "cpu": 0.0125, "memory": 0.13098204508423805, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749490878208, "cpu": 0.0092, "memory": 0.1317303627729416, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749490880209, "cpu": 0.0085, "memory": 0.12958734296262264, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749490882210, "cpu": 0.006200000000000001, "memory": 0.13008187524974346, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749490884211, "cpu": 0.0077, "memory": 0.13245278969407082, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749490886212, "cpu": 0.006200000000000001, "memory": 0.08852421306073666, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749490888213, "cpu": 0.010799999999999999, "memory": 0.08922293782234192, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749490890215, "cpu": 1.0734000000000001, "memory": 0.09192861616611481, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749490892215, "cpu": 0.0075, "memory": 0.0906212255358696, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749490894216, "cpu": 0.006, "memory": 0.09280135855078697, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749490896217, "cpu": 0.0097, "memory": 0.09375815279781818, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749490898218, "cpu": 0.006, "memory": 0.09121457114815712, "temperature": 37, "efficiency": 74}, {"timestamp": 1749490900219, "cpu": 0.0068, "memory": 0.09275912307202816, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749490902219, "cpu": 0.0059, "memory": 0.09356886148452759, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749490904220, "cpu": 0.0063, "memory": 0.09193401783704758, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749490906221, "cpu": 0.0075, "memory": 0.09253141470253468, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749490908222, "cpu": 0.0226, "memory": 0.09326590225100517, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749490910223, "cpu": 0.0065, "memory": 0.09498577564954758, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749490912225, "cpu": 0.009899999999999999, "memory": 0.09183632209897041, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749490914225, "cpu": 0.0063999999999999994, "memory": 0.09383810684084892, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749490916226, "cpu": 0.0071, "memory": 0.09476132690906525, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749490918227, "cpu": 0.0067, "memory": 0.09514186531305313, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749490920227, "cpu": 0.0116, "memory": 0.0933423638343811, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749490922229, "cpu": 0.0336, "memory": 0.09404299780726433, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749490924230, "cpu": 0.0075, "memory": 0.09581656195223331, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749490926231, "cpu": 0.0072, "memory": 0.09640739299356937, "temperature": 37, "efficiency": 71.8}, {"timestamp": 1749490928232, "cpu": 0.0086, "memory": 0.09300266392529011, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749490930232, "cpu": 0.006, "memory": 0.0950823538005352, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749490932234, "cpu": 0.006600000000000001, "memory": 0.09584971703588963, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749490934235, "cpu": 0.0068, "memory": 0.0939754769206047, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749490936236, "cpu": 0.009000000000000001, "memory": 0.09500854648649693, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749490938237, "cpu": 0.0060999999999999995, "memory": 0.0958045944571495, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749490940237, "cpu": 0.0098, "memory": 0.09399708360433578, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749490942239, "cpu": 0.007600000000000001, "memory": 0.09447773918509483, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749490944240, "cpu": 0.0119, "memory": 0.09703831747174263, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749490946242, "cpu": 0.0101, "memory": 0.10553952306509018, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749490948243, "cpu": 0.0104, "memory": 0.09828554466366768, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749490950245, "cpu": 0.014799999999999999, "memory": 0.09988569654524326, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749490952245, "cpu": 0.014799999999999999, "memory": 0.10045738890767097, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749490954246, "cpu": 0.0155, "memory": 0.1020719762891531, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749490956247, "cpu": 0.0084, "memory": 0.09894059039652348, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749490958247, "cpu": 0.0089, "memory": 0.09963987395167351, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749490960248, "cpu": 0.0091, "memory": 0.10118247009813786, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749490962250, "cpu": 0.0177, "memory": 0.10176301002502441, "temperature": 37, "efficiency": 71}, {"timestamp": 1749490964250, "cpu": 0.0088, "memory": 0.09966804645955563, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749490966251, "cpu": 0.0169, "memory": 0.10002683848142624, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749490968252, "cpu": 0.008, "memory": 0.10124850086867809, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749490970253, "cpu": 0.0077, "memory": 0.10292576625943184, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749490972254, "cpu": 0.0077, "memory": 0.10328111238777637, "temperature": 37, "efficiency": 70.6}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}