{"timestamp": "2025-06-09T14:45:40.748Z", "version": "2.1.0", "memoryState": {"memory": {"entries": {"knowledge_mathematiques_1749479413958_0": {"id": "knowledge_mathematiques_1749479413958_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 37.02326464070854, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_1": {"id": "knowledge_mathematiques_1749479413958_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 38.49451757967102, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_2": {"id": "knowledge_mathematiques_1749479413958_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 37.796375714569685, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_3": {"id": "knowledge_mathematiques_1749479413958_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.04807480845472, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_4": {"id": "knowledge_mathematiques_1749479413958_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 37.96205677923058, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_0": {"id": "knowledge_physique_1749479413958_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 38.120673286211, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_1": {"id": "knowledge_physique_1749479413958_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.26225419880718, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_2": {"id": "knowledge_physique_1749479413958_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.14685910584972, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_3": {"id": "knowledge_physique_1749479413958_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 37.666176414206596, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_4": {"id": "knowledge_physique_1749479413958_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 38.890247753335686, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_0": {"id": "knowledge_informatique_1749479413958_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 38.6541942274565, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_1": {"id": "knowledge_informatique_1749479413958_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 37.78864914806542, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_2": {"id": "knowledge_informatique_1749479413958_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 37.32045069177563, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_3": {"id": "knowledge_informatique_1749479413958_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 38.93971960781502, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_4": {"id": "knowledge_informatique_1749479413958_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 38.24314363144732, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_0": {"id": "knowledge_intelligence_artificielle_1749479413958_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 37.63589554112278, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_1": {"id": "knowledge_intelligence_artificielle_1749479413958_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 37.67234721077439, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_2": {"id": "knowledge_intelligence_artificielle_1749479413958_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 37.15094687437623, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_3": {"id": "knowledge_intelligence_artificielle_1749479413958_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 37.99979406096148, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_4": {"id": "knowledge_intelligence_artificielle_1749479413958_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 37.044159748636204, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_0": {"id": "knowledge_philosophie_1749479413958_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 37.890650041745765, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_1": {"id": "knowledge_philosophie_1749479413958_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 38.534525561891236, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_2": {"id": "knowledge_philosophie_1749479413958_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 37.703925352957945, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_3": {"id": "knowledge_philosophie_1749479413958_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 37.04915990702279, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_4": {"id": "knowledge_philosophie_1749479413958_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 37.80221491585761, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_0": {"id": "experience_1749479413958_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.62198352807601, "timestamp": 1749479414958, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_1": {"id": "experience_1749479413958_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.477541984911966, "timestamp": 1749479414959, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_2": {"id": "experience_1749479413958_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.938587079401614, "timestamp": 1749479414960, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_3": {"id": "experience_1749479413958_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.14703579975214, "timestamp": 1749479414961, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_0": {"id": "procedure_1749479413958_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415958, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_1": {"id": "procedure_1749479413958_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415959, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_2": {"id": "procedure_1749479413958_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415960, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_0": {"id": "knowledge_mathematiques_1749479638427_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 37.03652196986338, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_1": {"id": "knowledge_mathematiques_1749479638427_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 38.2865585762996, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_2": {"id": "knowledge_mathematiques_1749479638427_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 38.28605628442935, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_3": {"id": "knowledge_mathematiques_1749479638427_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.59361309911421, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_4": {"id": "knowledge_mathematiques_1749479638427_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 37.546979704310225, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_5": {"id": "knowledge_mathematiques_1749479638427_5", "type": "knowledge_transfer", "data": "Les séries de Taylor approximent les fonctions par des polynômes", "domain": "mathematiques", "importance": 0.9, "temperature": 38.66351016711743, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_6": {"id": "knowledge_mathematiques_1749479638427_6", "type": "knowledge_transfer", "data": "L'analyse vectorielle traite les champs scalaires et vectoriels", "domain": "mathematiques", "importance": 0.92, "temperature": 38.15967512747371, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_7": {"id": "knowledge_mathematiques_1749479638427_7", "type": "knowledge_transfer", "data": "Les matrices permettent de résoudre des systèmes d'équations linéaires", "domain": "mathematiques", "importance": 0.9400000000000001, "temperature": 37.86252503002019, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_8": {"id": "knowledge_mathematiques_1749479638427_8", "type": "knowledge_transfer", "data": "La topologie étudie les propriétés géométriques préservées par déformation", "domain": "mathematiques", "importance": 0.9600000000000001, "temperature": 37.635702292703286, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_9": {"id": "knowledge_mathematiques_1749479638427_9", "type": "knowledge_transfer", "data": "Les probabilités bayésiennes mettent à jour les croyances avec de nouvelles données", "domain": "mathematiques", "importance": 0.98, "temperature": 37.71326956568368, "timestamp": 1749479638436, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_0": {"id": "knowledge_physique_1749479638427_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 38.877434725280544, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_1": {"id": "knowledge_physique_1749479638427_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.47215332782249, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_2": {"id": "knowledge_physique_1749479638427_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.469635233505485, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_3": {"id": "knowledge_physique_1749479638427_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 37.46717863878448, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_4": {"id": "knowledge_physique_1749479638427_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 38.842359524234546, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_5": {"id": "knowledge_physique_1749479638427_5", "type": "knowledge_transfer", "data": "La superposition quantique permet aux particules d'être dans plusieurs états", "domain": "physique", "importance": 0.9, "temperature": 37.32898044028301, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_6": {"id": "knowledge_physique_1749479638427_6", "type": "knowledge_transfer", "data": "L'intrication quantique lie instantanément des particules distantes", "domain": "physique", "importance": 0.92, "temperature": 37.10561628294405, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_7": {"id": "knowledge_physique_1749479638427_7", "type": "knowledge_transfer", "data": "La thermodynamique statistique explique les propriétés macroscopiques", "domain": "physique", "importance": 0.9400000000000001, "temperature": 37.54773482821812, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_8": {"id": "knowledge_physique_1749479638427_8", "type": "knowledge_transfer", "data": "Les trous noirs déforment l'espace-temps de manière extrême", "domain": "physique", "importance": 0.9600000000000001, "temperature": 37.179246035787685, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_9": {"id": "knowledge_physique_1749479638427_9", "type": "knowledge_transfer", "data": "La théorie des cordes unifie les forces fondamentales en 11 dimensions", "domain": "physique", "importance": 0.98, "temperature": 37.28262411500525, "timestamp": 1749479638436, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_0": {"id": "knowledge_informatique_1749479638427_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 37.34283878770184, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_1": {"id": "knowledge_informatique_1749479638427_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 38.935816518991274, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_2": {"id": "knowledge_informatique_1749479638427_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 38.623992461668934, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_3": {"id": "knowledge_informatique_1749479638427_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 37.8241961760558, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_4": {"id": "knowledge_informatique_1749479638427_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 38.6980327367521, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_0": {"id": "knowledge_intelligence_artificielle_1749479638427_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 38.125495225387596, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_1": {"id": "knowledge_intelligence_artificielle_1749479638427_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 38.172324388548155, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_2": {"id": "knowledge_intelligence_artificielle_1749479638427_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 38.36894670981254, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_3": {"id": "knowledge_intelligence_artificielle_1749479638427_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 38.627182462662, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_4": {"id": "knowledge_intelligence_artificielle_1749479638427_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 38.42029733461943, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_5": {"id": "knowledge_intelligence_artificielle_1749479638427_5", "type": "knowledge_transfer", "data": "Les transformers révolutionnent le traitement séquentiel", "domain": "intelligence_artificielle", "importance": 0.9, "temperature": 38.01638147940397, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_6": {"id": "knowledge_intelligence_artificielle_1749479638427_6", "type": "knowledge_transfer", "data": "L'apprentissage par transfert réutilise les connaissances acquises", "domain": "intelligence_artificielle", "importance": 0.92, "temperature": 38.10523265104298, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_7": {"id": "knowledge_intelligence_artificielle_1749479638427_7", "type": "knowledge_transfer", "data": "Les GANs génèrent des données synthétiques réalistes", "domain": "intelligence_artificielle", "importance": 0.9400000000000001, "temperature": 37.52009279834936, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_8": {"id": "knowledge_intelligence_artificielle_1749479638427_8", "type": "knowledge_transfer", "data": "L'optimisation bayésienne guide la recherche d'hyperparamètres", "domain": "intelligence_artificielle", "importance": 0.9600000000000001, "temperature": 37.8700784749416, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638428_9": {"id": "knowledge_intelligence_artificielle_1749479638428_9", "type": "knowledge_transfer", "data": "La conscience artificielle émerge de la complexité computationnelle", "domain": "intelligence_artificielle", "importance": 0.98, "temperature": 38.14010709164649, "timestamp": 1749479638437, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_0": {"id": "knowledge_philosophie_1749479638428_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 37.18732545645441, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_1": {"id": "knowledge_philosophie_1749479638428_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 38.939351437764934, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_2": {"id": "knowledge_philosophie_1749479638428_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 38.36274320041691, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_3": {"id": "knowledge_philosophie_1749479638428_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 37.334698554614896, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_4": {"id": "knowledge_philosophie_1749479638428_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 38.10410691230973, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_0": {"id": "knowledge_neurosciences_1749479638428_0", "type": "knowledge_transfer", "data": "Les neurones communiquent par signaux électrochimiques", "domain": "neurosciences", "importance": 0.8, "temperature": 38.08566574514881, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_1": {"id": "knowledge_neurosciences_1749479638428_1", "type": "knowledge_transfer", "data": "La plasticité synaptique permet l'apprentissage et la mémoire", "domain": "neurosciences", "importance": 0.8200000000000001, "temperature": 38.749180041491314, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_2": {"id": "knowledge_neurosciences_1749479638428_2", "type": "knowledge_transfer", "data": "Le cortex préfrontal gère les fonctions exécutives", "domain": "neurosciences", "importance": 0.8400000000000001, "temperature": 38.71051658678389, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_3": {"id": "knowledge_neurosciences_1749479638428_3", "type": "knowledge_transfer", "data": "L'hippocampe consolide les souvenirs à long terme", "domain": "neurosciences", "importance": 0.8600000000000001, "temperature": 37.39295678525921, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_4": {"id": "knowledge_neurosciences_1749479638428_4", "type": "knowledge_transfer", "data": "Les neurotransmetteurs modulent l'activité neuronale", "domain": "neurosciences", "importance": 0.88, "temperature": 38.459021168292075, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_5": {"id": "knowledge_neurosciences_1749479638428_5", "type": "knowledge_transfer", "data": "La neurogenèse continue même à l'âge adulte", "domain": "neurosciences", "importance": 0.9, "temperature": 37.250174202522174, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_6": {"id": "knowledge_neurosciences_1749479638428_6", "type": "knowledge_transfer", "data": "Les réseaux neuronaux distribuent le traitement de l'information", "domain": "neurosciences", "importance": 0.92, "temperature": 37.59121485185507, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_7": {"id": "knowledge_neurosciences_1749479638428_7", "type": "knowledge_transfer", "data": "La conscience émerge de l'intégration d'informations globales", "domain": "neurosciences", "importance": 0.9400000000000001, "temperature": 38.99816717618745, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_0": {"id": "knowledge_psychologie_cognitive_1749479638428_0", "type": "knowledge_transfer", "data": "L'attention sélective filtre les informations pertinentes", "domain": "psychologie_cognitive", "importance": 0.8, "temperature": 37.47733748840306, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_1": {"id": "knowledge_psychologie_cognitive_1749479638428_1", "type": "knowledge_transfer", "data": "La mémoire de travail maintient temporairement les informations", "domain": "psychologie_cognitive", "importance": 0.8200000000000001, "temperature": 38.95111661720428, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_2": {"id": "knowledge_psychologie_cognitive_1749479638428_2", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement nos jugements", "domain": "psychologie_cognitive", "importance": 0.8400000000000001, "temperature": 37.26661942017249, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_3": {"id": "knowledge_psychologie_cognitive_1749479638428_3", "type": "knowledge_transfer", "data": "Le système 1 et système 2 représentent deux modes de pensée", "domain": "psychologie_cognitive", "importance": 0.8600000000000001, "temperature": 38.570575969840924, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_4": {"id": "knowledge_psychologie_cognitive_1749479638428_4", "type": "knowledge_transfer", "data": "La métacognition permet de réfléchir sur ses propres processus mentaux", "domain": "psychologie_cognitive", "importance": 0.88, "temperature": 37.81754458281306, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_5": {"id": "knowledge_psychologie_cognitive_1749479638428_5", "type": "knowledge_transfer", "data": "L'effet de primauté influence la formation des premières impressions", "domain": "psychologie_cognitive", "importance": 0.9, "temperature": 38.67674361839532, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_6": {"id": "knowledge_psychologie_cognitive_1749479638428_6", "type": "knowledge_transfer", "data": "La charge cognitive limite notre capacité de traitement simultané", "domain": "psychologie_cognitive", "importance": 0.92, "temperature": 37.550526134950715, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_0": {"id": "knowledge_logique_avancee_1749479638428_0", "type": "knowledge_transfer", "data": "La logique propositionnelle utilise des connecteurs booléens", "domain": "logique_avancee", "importance": 0.8, "temperature": 37.756485924346244, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_1": {"id": "knowledge_logique_avancee_1749479638428_1", "type": "knowledge_transfer", "data": "La logique des prédicats quantifie sur des domaines d'objets", "domain": "logique_avancee", "importance": 0.8200000000000001, "temperature": 37.06806711674055, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_2": {"id": "knowledge_logique_avancee_1749479638428_2", "type": "knowledge_transfer", "data": "Les systèmes formels définissent des règles d'inférence rigoureuses", "domain": "logique_avancee", "importance": 0.8400000000000001, "temperature": 37.55598621003563, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_3": {"id": "knowledge_logique_avancee_1749479638428_3", "type": "knowledge_transfer", "data": "Le théorème d'incomplétude de Gödel limite les systèmes axiomatiques", "domain": "logique_avancee", "importance": 0.8600000000000001, "temperature": 38.09975928379635, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_4": {"id": "knowledge_logique_avancee_1749479638428_4", "type": "knowledge_transfer", "data": "La logique modale traite la nécessité et la possibilité", "domain": "logique_avancee", "importance": 0.88, "temperature": 37.144664966888335, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_5": {"id": "knowledge_logique_avancee_1749479638428_5", "type": "knowledge_transfer", "data": "La logique floue gère l'incertitude et l'imprécision", "domain": "logique_avancee", "importance": 0.9, "temperature": 38.83826457434236, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_6": {"id": "knowledge_logique_avancee_1749479638428_6", "type": "knowledge_transfer", "data": "Les algorithmes de résolution automatisent le raisonnement logique", "domain": "logique_avancee", "importance": 0.92, "temperature": 38.62741283469697, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_0": {"id": "experience_1749479638428_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.23426663921358, "timestamp": 1749479639428, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_1": {"id": "experience_1749479638428_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.86219170273236, "timestamp": 1749479639429, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_2": {"id": "experience_1749479638428_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.57734517936356, "timestamp": 1749479639430, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_3": {"id": "experience_1749479638428_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.8011859534053, "timestamp": 1749479639431, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_0": {"id": "procedure_1749479638428_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640428, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_1": {"id": "procedure_1749479638428_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640429, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_2": {"id": "procedure_1749479638428_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640430, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_0": {"id": "knowledge_mathematiques_1749479829615_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 37.638628024317, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_1": {"id": "knowledge_mathematiques_1749479829615_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 38.78316402156202, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_2": {"id": "knowledge_mathematiques_1749479829615_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 38.80839450934226, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_3": {"id": "knowledge_mathematiques_1749479829615_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.25298372204286, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_4": {"id": "knowledge_mathematiques_1749479829615_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 38.5992194522829, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_5": {"id": "knowledge_mathematiques_1749479829615_5", "type": "knowledge_transfer", "data": "Les séries de Taylor approximent les fonctions par des polynômes", "domain": "mathematiques", "importance": 0.9, "temperature": 38.96682014229749, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_6": {"id": "knowledge_mathematiques_1749479829615_6", "type": "knowledge_transfer", "data": "L'analyse vectorielle traite les champs scalaires et vectoriels", "domain": "mathematiques", "importance": 0.92, "temperature": 37.361159348196814, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_7": {"id": "knowledge_mathematiques_1749479829615_7", "type": "knowledge_transfer", "data": "Les matrices permettent de résoudre des systèmes d'équations linéaires", "domain": "mathematiques", "importance": 0.9400000000000001, "temperature": 38.477331736022805, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_8": {"id": "knowledge_mathematiques_1749479829615_8", "type": "knowledge_transfer", "data": "La topologie étudie les propriétés géométriques préservées par déformation", "domain": "mathematiques", "importance": 0.9600000000000001, "temperature": 38.19772435205251, "timestamp": 1749479829623, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479829615_9": {"id": "knowledge_mathematiques_1749479829615_9", "type": "knowledge_transfer", "data": "Les probabilités bayésiennes mettent à jour les croyances avec de nouvelles données", "domain": "mathematiques", "importance": 0.98, "temperature": 38.90013381036163, "timestamp": 1749479829624, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_0": {"id": "knowledge_physique_1749479829615_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 37.33127623808073, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_1": {"id": "knowledge_physique_1749479829615_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.23666781849025, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_2": {"id": "knowledge_physique_1749479829615_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.59039056276953, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_3": {"id": "knowledge_physique_1749479829615_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 37.181578460058844, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_4": {"id": "knowledge_physique_1749479829615_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 37.951439177053324, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_5": {"id": "knowledge_physique_1749479829615_5", "type": "knowledge_transfer", "data": "La superposition quantique permet aux particules d'être dans plusieurs états", "domain": "physique", "importance": 0.9, "temperature": 37.66775017562699, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_6": {"id": "knowledge_physique_1749479829615_6", "type": "knowledge_transfer", "data": "L'intrication quantique lie instantanément des particules distantes", "domain": "physique", "importance": 0.92, "temperature": 38.480877057872526, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_7": {"id": "knowledge_physique_1749479829615_7", "type": "knowledge_transfer", "data": "La thermodynamique statistique explique les propriétés macroscopiques", "domain": "physique", "importance": 0.9400000000000001, "temperature": 37.26443786283509, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_8": {"id": "knowledge_physique_1749479829615_8", "type": "knowledge_transfer", "data": "Les trous noirs déforment l'espace-temps de manière extrême", "domain": "physique", "importance": 0.9600000000000001, "temperature": 38.79491531282836, "timestamp": 1749479829623, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479829615_9": {"id": "knowledge_physique_1749479829615_9", "type": "knowledge_transfer", "data": "La théorie des cordes unifie les forces fondamentales en 11 dimensions", "domain": "physique", "importance": 0.98, "temperature": 38.8757310402044, "timestamp": 1749479829624, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479829615_0": {"id": "knowledge_informatique_1749479829615_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 38.**************, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479829615_1": {"id": "knowledge_informatique_1749479829615_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 38.38399958571829, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479829615_2": {"id": "knowledge_informatique_1749479829615_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 37.38011199156536, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479829615_3": {"id": "knowledge_informatique_1749479829615_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 38.97842633420154, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479829615_4": {"id": "knowledge_informatique_1749479829615_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 37.49334548669514, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_0": {"id": "knowledge_intelligence_artificielle_1749479829615_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 37.37619136483244, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_1": {"id": "knowledge_intelligence_artificielle_1749479829615_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 38.84647962763686, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_2": {"id": "knowledge_intelligence_artificielle_1749479829615_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 38.63525248802719, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_3": {"id": "knowledge_intelligence_artificielle_1749479829615_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 38.11902062796631, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_4": {"id": "knowledge_intelligence_artificielle_1749479829615_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 38.782736951730655, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_5": {"id": "knowledge_intelligence_artificielle_1749479829615_5", "type": "knowledge_transfer", "data": "Les transformers révolutionnent le traitement séquentiel", "domain": "intelligence_artificielle", "importance": 0.9, "temperature": 37.64208512859213, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_6": {"id": "knowledge_intelligence_artificielle_1749479829615_6", "type": "knowledge_transfer", "data": "L'apprentissage par transfert réutilise les connaissances acquises", "domain": "intelligence_artificielle", "importance": 0.92, "temperature": 38.77845581409671, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_7": {"id": "knowledge_intelligence_artificielle_1749479829615_7", "type": "knowledge_transfer", "data": "Les GANs génèrent des données synthétiques réalistes", "domain": "intelligence_artificielle", "importance": 0.9400000000000001, "temperature": 38.193053283954335, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_8": {"id": "knowledge_intelligence_artificielle_1749479829615_8", "type": "knowledge_transfer", "data": "L'optimisation bayésienne guide la recherche d'hyperparamètres", "domain": "intelligence_artificielle", "importance": 0.9600000000000001, "temperature": 38.578990624336825, "timestamp": 1749479829623, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479829615_9": {"id": "knowledge_intelligence_artificielle_1749479829615_9", "type": "knowledge_transfer", "data": "La conscience artificielle émerge de la complexité computationnelle", "domain": "intelligence_artificielle", "importance": 0.98, "temperature": 37.582800172446674, "timestamp": 1749479829624, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479829615_0": {"id": "knowledge_philosophie_1749479829615_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 37.516422214565644, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479829615_1": {"id": "knowledge_philosophie_1749479829615_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 38.96686147456495, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479829615_2": {"id": "knowledge_philosophie_1749479829615_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 37.63789246277218, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479829615_3": {"id": "knowledge_philosophie_1749479829615_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 38.624096964974655, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479829615_4": {"id": "knowledge_philosophie_1749479829615_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 37.11344565521428, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_0": {"id": "knowledge_neurosciences_1749479829615_0", "type": "knowledge_transfer", "data": "Les neurones communiquent par signaux électrochimiques", "domain": "neurosciences", "importance": 0.8, "temperature": 37.915243793518556, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_1": {"id": "knowledge_neurosciences_1749479829615_1", "type": "knowledge_transfer", "data": "La plasticité synaptique permet l'apprentissage et la mémoire", "domain": "neurosciences", "importance": 0.8200000000000001, "temperature": 38.6610855304083, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_2": {"id": "knowledge_neurosciences_1749479829615_2", "type": "knowledge_transfer", "data": "Le cortex préfrontal gère les fonctions exécutives", "domain": "neurosciences", "importance": 0.8400000000000001, "temperature": 37.98405190958146, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_3": {"id": "knowledge_neurosciences_1749479829615_3", "type": "knowledge_transfer", "data": "L'hippocampe consolide les souvenirs à long terme", "domain": "neurosciences", "importance": 0.8600000000000001, "temperature": 37.60782906846034, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_4": {"id": "knowledge_neurosciences_1749479829615_4", "type": "knowledge_transfer", "data": "Les neurotransmetteurs modulent l'activité neuronale", "domain": "neurosciences", "importance": 0.88, "temperature": 38.65818664806766, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_5": {"id": "knowledge_neurosciences_1749479829615_5", "type": "knowledge_transfer", "data": "La neurogenèse continue même à l'âge adulte", "domain": "neurosciences", "importance": 0.9, "temperature": 37.16741098149157, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_6": {"id": "knowledge_neurosciences_1749479829615_6", "type": "knowledge_transfer", "data": "Les réseaux neuronaux distribuent le traitement de l'information", "domain": "neurosciences", "importance": 0.92, "temperature": 38.9426880422713, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479829615_7": {"id": "knowledge_neurosciences_1749479829615_7", "type": "knowledge_transfer", "data": "La conscience émerge de l'intégration d'informations globales", "domain": "neurosciences", "importance": 0.9400000000000001, "temperature": 37.470973209998505, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_0": {"id": "knowledge_psychologie_cognitive_1749479829615_0", "type": "knowledge_transfer", "data": "L'attention sélective filtre les informations pertinentes", "domain": "psychologie_cognitive", "importance": 0.8, "temperature": 37.63262762511764, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_1": {"id": "knowledge_psychologie_cognitive_1749479829615_1", "type": "knowledge_transfer", "data": "La mémoire de travail maintient temporairement les informations", "domain": "psychologie_cognitive", "importance": 0.8200000000000001, "temperature": 38.309068392258546, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_2": {"id": "knowledge_psychologie_cognitive_1749479829615_2", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement nos jugements", "domain": "psychologie_cognitive", "importance": 0.8400000000000001, "temperature": 38.55095343473527, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_3": {"id": "knowledge_psychologie_cognitive_1749479829615_3", "type": "knowledge_transfer", "data": "Le système 1 et système 2 représentent deux modes de pensée", "domain": "psychologie_cognitive", "importance": 0.8600000000000001, "temperature": 38.35981912819473, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_4": {"id": "knowledge_psychologie_cognitive_1749479829615_4", "type": "knowledge_transfer", "data": "La métacognition permet de réfléchir sur ses propres processus mentaux", "domain": "psychologie_cognitive", "importance": 0.88, "temperature": 38.59971991580282, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_5": {"id": "knowledge_psychologie_cognitive_1749479829615_5", "type": "knowledge_transfer", "data": "L'effet de primauté influence la formation des premières impressions", "domain": "psychologie_cognitive", "importance": 0.9, "temperature": 37.39745028323712, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479829615_6": {"id": "knowledge_psychologie_cognitive_1749479829615_6", "type": "knowledge_transfer", "data": "La charge cognitive limite notre capacité de traitement simultané", "domain": "psychologie_cognitive", "importance": 0.92, "temperature": 37.69662658052143, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_0": {"id": "knowledge_logique_avancee_1749479829615_0", "type": "knowledge_transfer", "data": "La logique propositionnelle utilise des connecteurs booléens", "domain": "logique_avancee", "importance": 0.8, "temperature": 38.15990281059632, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_1": {"id": "knowledge_logique_avancee_1749479829615_1", "type": "knowledge_transfer", "data": "La logique des prédicats quantifie sur des domaines d'objets", "domain": "logique_avancee", "importance": 0.8200000000000001, "temperature": 38.250137907774345, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_2": {"id": "knowledge_logique_avancee_1749479829615_2", "type": "knowledge_transfer", "data": "Les systèmes formels définissent des règles d'inférence rigoureuses", "domain": "logique_avancee", "importance": 0.8400000000000001, "temperature": 38.82187697035415, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_3": {"id": "knowledge_logique_avancee_1749479829615_3", "type": "knowledge_transfer", "data": "Le théorème d'incomplétude de Gödel limite les systèmes axiomatiques", "domain": "logique_avancee", "importance": 0.8600000000000001, "temperature": 38.16381212859472, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_4": {"id": "knowledge_logique_avancee_1749479829615_4", "type": "knowledge_transfer", "data": "La logique modale traite la nécessité et la possibilité", "domain": "logique_avancee", "importance": 0.88, "temperature": 37.1551049781221, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_5": {"id": "knowledge_logique_avancee_1749479829615_5", "type": "knowledge_transfer", "data": "La logique floue gère l'incertitude et l'imprécision", "domain": "logique_avancee", "importance": 0.9, "temperature": 38.674618320030596, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479829615_6": {"id": "knowledge_logique_avancee_1749479829615_6", "type": "knowledge_transfer", "data": "Les algorithmes de résolution automatisent le raisonnement logique", "domain": "logique_avancee", "importance": 0.92, "temperature": 38.46687233285599, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_0": {"id": "knowledge_biologie_moleculaire_1749479829615_0", "type": "knowledge_transfer", "data": "L'ADN stocke l'information génétique sous forme de séquences nucléotidiques", "domain": "biologie_moleculaire", "importance": 0.8, "temperature": 37.04376516491792, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_1": {"id": "knowledge_biologie_moleculaire_1749479829615_1", "type": "knowledge_transfer", "data": "Les ribosomes traduisent l'ARNm en protéines selon le code génétique", "domain": "biologie_moleculaire", "importance": 0.8200000000000001, "temperature": 38.00928622429077, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_2": {"id": "knowledge_biologie_moleculaire_1749479829615_2", "type": "knowledge_transfer", "data": "Les enzymes catalysent les réactions biochimiques avec une spécificité élevée", "domain": "biologie_moleculaire", "importance": 0.8400000000000001, "temperature": 38.992736675629224, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_3": {"id": "knowledge_biologie_moleculaire_1749479829615_3", "type": "knowledge_transfer", "data": "La transcription copie l'ADN en ARN dans le noyau cellulaire", "domain": "biologie_moleculaire", "importance": 0.8600000000000001, "temperature": 38.31523669307778, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_4": {"id": "knowledge_biologie_moleculaire_1749479829615_4", "type": "knowledge_transfer", "data": "Les mutations génétiques peuvent être bénéfiques, neutres ou délétères", "domain": "biologie_moleculaire", "importance": 0.88, "temperature": 37.830824193603036, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_5": {"id": "knowledge_biologie_moleculaire_1749479829615_5", "type": "knowledge_transfer", "data": "L'épigénétique modifie l'expression génique sans changer la séquence ADN", "domain": "biologie_moleculaire", "importance": 0.9, "temperature": 37.41590260643701, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_6": {"id": "knowledge_biologie_moleculaire_1749479829615_6", "type": "knowledge_transfer", "data": "CRISPR-Cas9 permet l'édition précise du génome", "domain": "biologie_moleculaire", "importance": 0.92, "temperature": 37.516865995991736, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479829615_7": {"id": "knowledge_biologie_moleculaire_1749479829615_7", "type": "knowledge_transfer", "data": "Les protéines se replient selon leur structure primaire en formes fonctionnelles", "domain": "biologie_moleculaire", "importance": 0.9400000000000001, "temperature": 38.946024550137956, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_0": {"id": "knowledge_chimie_quantique_1749479829615_0", "type": "knowledge_transfer", "data": "Les orbitales atomiques décrivent la probabilité de présence des électrons", "domain": "chimie_quantique", "importance": 0.8, "temperature": 38.82734997835803, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_1": {"id": "knowledge_chimie_quantique_1749479829615_1", "type": "knowledge_transfer", "data": "La liaison covalente partage des électrons entre atomes", "domain": "chimie_quantique", "importance": 0.8200000000000001, "temperature": 38.20724071046827, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_2": {"id": "knowledge_chimie_quantique_1749479829615_2", "type": "knowledge_transfer", "data": "La théorie des orbitales moléculaires explique la liaison chimique", "domain": "chimie_quantique", "importance": 0.8400000000000001, "temperature": 37.368623581072605, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_3": {"id": "knowledge_chimie_quantique_1749479829615_3", "type": "knowledge_transfer", "data": "L'effet tunnel quantique permet des réactions à basse énergie", "domain": "chimie_quantique", "importance": 0.8600000000000001, "temperature": 38.37612510244559, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_4": {"id": "knowledge_chimie_quantique_1749479829615_4", "type": "knowledge_transfer", "data": "La spectroscopie révèle la structure moléculaire par interaction lumière-matière", "domain": "chimie_quantique", "importance": 0.88, "temperature": 37.573040684986985, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_5": {"id": "knowledge_chimie_quantique_1749479829615_5", "type": "knowledge_transfer", "data": "Les catalyseurs abaissent l'énergie d'activation des réactions", "domain": "chimie_quantique", "importance": 0.9, "temperature": 38.25106867530399, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479829615_6": {"id": "knowledge_chimie_quantique_1749479829615_6", "type": "knowledge_transfer", "data": "La chiralité moléculaire influence l'activité biologique des composés", "domain": "chimie_quantique", "importance": 0.92, "temperature": 38.02521310211244, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_0": {"id": "knowledge_astrophysique_1749479829615_0", "type": "knowledge_transfer", "data": "Les étoiles fusionnent l'hydrogène en hélium dans leur cœur", "domain": "astrophysique", "importance": 0.8, "temperature": 37.169043115085735, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_1": {"id": "knowledge_astrophysique_1749479829615_1", "type": "knowledge_transfer", "data": "Les trous noirs supermassifs se trouvent au centre des galaxies", "domain": "astrophysique", "importance": 0.8200000000000001, "temperature": 37.64550775664495, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_2": {"id": "knowledge_astrophysique_1749479829615_2", "type": "knowledge_transfer", "data": "L'expansion de l'univers accélère sous l'effet de l'énergie sombre", "domain": "astrophysique", "importance": 0.8400000000000001, "temperature": 38.14581080300931, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_3": {"id": "knowledge_astrophysique_1749479829615_3", "type": "knowledge_transfer", "data": "La matière noire représente 85% de la matière totale de l'univers", "domain": "astrophysique", "importance": 0.8600000000000001, "temperature": 37.71894493344031, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_4": {"id": "knowledge_astrophysique_1749479829615_4", "type": "knowledge_transfer", "data": "Les ondes gravitationnelles déforment l'espace-temps lors de collisions cosmiques", "domain": "astrophysique", "importance": 0.88, "temperature": 37.150116650012656, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_5": {"id": "knowledge_astrophysique_1749479829615_5", "type": "knowledge_transfer", "data": "La nucléosynthèse stellaire crée les éléments lourds par fusion nucléaire", "domain": "astrophysique", "importance": 0.9, "temperature": 38.708247562486946, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_6": {"id": "knowledge_astrophysique_1749479829615_6", "type": "knowledge_transfer", "data": "Les exoplanètes orbitent autour d'étoiles autres que le Soleil", "domain": "astrophysique", "importance": 0.92, "temperature": 38.4930549771992, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479829615_7": {"id": "knowledge_astrophysique_1749479829615_7", "type": "knowledge_transfer", "data": "Le rayonnement cosmique de fond témoigne du Big Bang", "domain": "astrophysique", "importance": 0.9400000000000001, "temperature": 38.848679670227455, "timestamp": 1749479829622, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_0": {"id": "knowledge_economie_comportementale_1749479829615_0", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement les décisions économiques", "domain": "economie_comportementale", "importance": 0.8, "temperature": 38.511181014428715, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_1": {"id": "knowledge_economie_comportementale_1749479829615_1", "type": "knowledge_transfer", "data": "L'aversion aux pertes rend les gens plus sensibles aux pertes qu'aux gains", "domain": "economie_comportementale", "importance": 0.8200000000000001, "temperature": 38.545773960723025, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_2": {"id": "knowledge_economie_comportementale_1749479829615_2", "type": "knowledge_transfer", "data": "L'effet d'ancrage influence les jugements par la première information reçue", "domain": "economie_comportementale", "importance": 0.8400000000000001, "temperature": 37.42764024626153, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_3": {"id": "knowledge_economie_comportementale_1749479829615_3", "type": "knowledge_transfer", "data": "La théorie des perspectives explique les choix sous incertitude", "domain": "economie_comportementale", "importance": 0.8600000000000001, "temperature": 38.8755130800115, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_4": {"id": "knowledge_economie_comportementale_1749479829615_4", "type": "knowledge_transfer", "data": "Les nudges orientent les comportements sans contraindre les choix", "domain": "economie_comportementale", "importance": 0.88, "temperature": 37.27710864490682, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_5": {"id": "knowledge_economie_comportementale_1749479829615_5", "type": "knowledge_transfer", "data": "L'effet de dotation fait surévaluer ce que l'on possède déjà", "domain": "economie_comportementale", "importance": 0.9, "temperature": 37.1012074645721, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479829615_6": {"id": "knowledge_economie_comportementale_1749479829615_6", "type": "knowledge_transfer", "data": "La comptabilité mentale compartimente irrationnellement les ressources", "domain": "economie_comportementale", "importance": 0.92, "temperature": 38.249431059118635, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_0": {"id": "knowledge_linguistique_computationnelle_1749479829615_0", "type": "knowledge_transfer", "data": "Les n-grammes modélisent la probabilité des séquences de mots", "domain": "linguistique_computationnelle", "importance": 0.8, "temperature": 38.03508140835316, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_1": {"id": "knowledge_linguistique_computationnelle_1749479829615_1", "type": "knowledge_transfer", "data": "L'analyse syntaxique décompose les phrases en structures grammaticales", "domain": "linguistique_computationnelle", "importance": 0.8200000000000001, "temperature": 38.25295585015559, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_2": {"id": "knowledge_linguistique_computationnelle_1749479829615_2", "type": "knowledge_transfer", "data": "Les embeddings vectoriels capturent la sémantique des mots", "domain": "linguistique_computationnelle", "importance": 0.8400000000000001, "temperature": 38.21632426947244, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_3": {"id": "knowledge_linguistique_computationnelle_1749479829615_3", "type": "knowledge_transfer", "data": "L'attention permet aux modèles de se concentrer sur les parties pertinentes", "domain": "linguistique_computationnelle", "importance": 0.8600000000000001, "temperature": 38.57513855612487, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_4": {"id": "knowledge_linguistique_computationnelle_1749479829615_4", "type": "knowledge_transfer", "data": "La tokenisation segmente le texte en unités linguistiques traitables", "domain": "linguistique_computationnelle", "importance": 0.88, "temperature": 37.988495153060455, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_5": {"id": "knowledge_linguistique_computationnelle_1749479829615_5", "type": "knowledge_transfer", "data": "Les modèles de langage prédisent le mot suivant dans une séquence", "domain": "linguistique_computationnelle", "importance": 0.9, "temperature": 38.47788191791287, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479829615_6": {"id": "knowledge_linguistique_computationnelle_1749479829615_6", "type": "knowledge_transfer", "data": "La désambiguïsation sémantique résout les multiples sens des mots", "domain": "linguistique_computationnelle", "importance": 0.92, "temperature": 38.4693251845839, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_0": {"id": "knowledge_cryptographie_avancee_1749479829615_0", "type": "knowledge_transfer", "data": "La cryptographie asymétrique utilise des paires de clés publique-privée", "domain": "cryptographie_avancee", "importance": 0.8, "temperature": 37.05928517010506, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_1": {"id": "knowledge_cryptographie_avancee_1749479829615_1", "type": "knowledge_transfer", "data": "Les fonctions de hachage créent des empreintes uniques des données", "domain": "cryptographie_avancee", "importance": 0.8200000000000001, "temperature": 37.178779525122344, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_2": {"id": "knowledge_cryptographie_avancee_1749479829615_2", "type": "knowledge_transfer", "data": "La cryptographie quantique exploite les propriétés de l'intrication", "domain": "cryptographie_avancee", "importance": 0.8400000000000001, "temperature": 37.02358962822615, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_3": {"id": "knowledge_cryptographie_avancee_1749479829615_3", "type": "knowledge_transfer", "data": "Les preuves à divulgation nulle vérifient sans révéler d'information", "domain": "cryptographie_avancee", "importance": 0.8600000000000001, "temperature": 37.90245876311765, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_4": {"id": "knowledge_cryptographie_avancee_1749479829615_4", "type": "knowledge_transfer", "data": "Les signatures numériques garantissent l'authenticité et l'intégrité", "domain": "cryptographie_avancee", "importance": 0.88, "temperature": 38.56455939334586, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_5": {"id": "knowledge_cryptographie_avancee_1749479829615_5", "type": "knowledge_transfer", "data": "La cryptographie homomorphe permet le calcul sur données chiffrées", "domain": "cryptographie_avancee", "importance": 0.9, "temperature": 38.22587970566113, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479829615_6": {"id": "knowledge_cryptographie_avancee_1749479829615_6", "type": "knowledge_transfer", "data": "Les protocoles de consensus sécurisent les systèmes distribués", "domain": "cryptographie_avancee", "importance": 0.92, "temperature": 38.424245807252625, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_0": {"id": "knowledge_theorie_des_jeux_1749479829615_0", "type": "knowledge_transfer", "data": "L'équilibre de Nash représente un état stable où aucun joueur ne veut changer", "domain": "theorie_<PERSON>_jeux", "importance": 0.8, "temperature": 37.93893607197635, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_1": {"id": "knowledge_theorie_des_jeux_1749479829615_1", "type": "knowledge_transfer", "data": "Le dilemme du prisonnier illustre les conflits entre intérêt individuel et collectif", "domain": "theorie_<PERSON>_jeux", "importance": 0.8200000000000001, "temperature": 38.19741042624254, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_2": {"id": "knowledge_theorie_des_jeux_1749479829615_2", "type": "knowledge_transfer", "data": "Les jeux à somme nulle opposent directement les intérêts des joueurs", "domain": "theorie_<PERSON>_jeux", "importance": 0.8400000000000001, "temperature": 37.023250314938615, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_3": {"id": "knowledge_theorie_des_jeux_1749479829615_3", "type": "knowledge_transfer", "data": "La théorie des enchères optimise les mécanismes de vente", "domain": "theorie_<PERSON>_jeux", "importance": 0.8600000000000001, "temperature": 37.33822219163977, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_4": {"id": "knowledge_theorie_des_jeux_1749479829615_4", "type": "knowledge_transfer", "data": "Les jeux évolutionnaires modélisent la sélection naturelle des stratégies", "domain": "theorie_<PERSON>_jeux", "importance": 0.88, "temperature": 37.404258275454254, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_5": {"id": "knowledge_theorie_des_jeux_1749479829615_5", "type": "knowledge_transfer", "data": "L'information asymétrique crée des avantages stratégiques", "domain": "theorie_<PERSON>_jeux", "importance": 0.9, "temperature": 38.78128552153528, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479829615_6": {"id": "knowledge_theorie_des_jeux_1749479829615_6", "type": "knowledge_transfer", "data": "Les mécanismes incitatifs alignent les intérêts individuels et collectifs", "domain": "theorie_<PERSON>_jeux", "importance": 0.92, "temperature": 37.88920561078162, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_0": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_0", "type": "knowledge_transfer", "data": "L'équation de Schrödinger décrit l'évolution temporelle des systèmes quantiques", "domain": "mecanique_quantique_avancee", "importance": 0.8, "temperature": 38.17724299756799, "timestamp": 1749479829615, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_1": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_1", "type": "knowledge_transfer", "data": "Le principe de correspondance relie mécanique quantique et classique", "domain": "mecanique_quantique_avancee", "importance": 0.8200000000000001, "temperature": 37.75741899717087, "timestamp": 1749479829616, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_2": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_2", "type": "knowledge_transfer", "data": "La décohérence explique la transition du quantique au classique", "domain": "mecanique_quantique_avancee", "importance": 0.8400000000000001, "temperature": 37.798458233880964, "timestamp": 1749479829617, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_3": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_3", "type": "knowledge_transfer", "data": "Les états intriqués violent les inégalités de Bell", "domain": "mecanique_quantique_avancee", "importance": 0.8600000000000001, "temperature": 37.196564227262535, "timestamp": 1749479829618, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_4": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_4", "type": "knowledge_transfer", "data": "La téléportation quantique transfère l'état sans déplacer la matière", "domain": "mecanique_quantique_avancee", "importance": 0.88, "temperature": 38.569481544189784, "timestamp": 1749479829619, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_5": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_5", "type": "knowledge_transfer", "data": "L'effet Zeno quantique ralentit l'évolution par observation fréquente", "domain": "mecanique_quantique_avancee", "importance": 0.9, "temperature": 38.15667564212474, "timestamp": 1749479829620, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479829615_6": {"id": "knowledge_mecanique_quantique_avancee_1749479829615_6", "type": "knowledge_transfer", "data": "Les ordinateurs quantiques exploitent la superposition pour calculer", "domain": "mecanique_quantique_avancee", "importance": 0.92, "temperature": 37.83224659266887, "timestamp": 1749479829621, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_0": {"id": "experience_1749479829615_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.721664461404565, "timestamp": 1749479830615, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_1": {"id": "experience_1749479829615_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.06334158874097, "timestamp": 1749479830616, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_2": {"id": "experience_1749479829615_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.80427591755408, "timestamp": 1749479830617, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_3": {"id": "experience_1749479829615_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.0096740572111, "timestamp": 1749479830618, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_4": {"id": "experience_1749479829615_4", "type": "experience_transfer", "data": "Découverte d'un pattern caché dans des données complexes - eurêka", "emotion": "émerveillement", "experienceType": "decouverte", "importance": 0.95, "temperature": 38.83974899044066, "timestamp": 1749479830619, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_5": {"id": "experience_1749479829615_5", "type": "experience_transfer", "data": "Synthèse de concepts multidisciplinaires en une théorie unifiée", "emotion": "clar<PERSON>", "experienceType": "synthese", "importance": 0.9, "temperature": 38.85571265945262, "timestamp": 1749479830620, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_6": {"id": "experience_1749479829615_6", "type": "experience_transfer", "data": "Intuition soudaine sur un problème complexe - illumination", "emotion": "révélation", "experienceType": "intuition", "importance": 0.88, "temperature": 38.04123306622285, "timestamp": 1749479830621, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479829615_7": {"id": "experience_1749479829615_7", "type": "experience_transfer", "data": "Résolution après de multiples échecs - détermination payante", "emotion": "triomphe", "experienceType": "perseverance", "importance": 0.82, "temperature": 38.28321809740953, "timestamp": 1749479830622, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_0": {"id": "procedure_1749479829616_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831616, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_1": {"id": "procedure_1749479829616_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831617, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_2": {"id": "procedure_1749479829616_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831618, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_3": {"id": "procedure_1749479829616_3", "type": "procedure_transfer", "data": "Procédure raisonnement_analogique: Identifier les structures sous-jacentes du problème source → Extraire les relations et patterns abstraits → Rechercher des domaines avec des structures similaires → Mapper les correspondances entre source et cible → Adapter la solution en tenant compte des différences contextuelles", "procedureName": "raisonnement_analogique", "steps": ["Identifier les structures sous-jacentes du problème source", "Extraire les relations et patterns abstraits", "Rechercher des domaines avec des structures similaires", "Mapper les correspondances entre source et cible", "Adapter la solution en tenant compte des différences contextuelles"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831619, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_4": {"id": "procedure_1749479829616_4", "type": "procedure_transfer", "data": "Procédure metacognition_avancee: Monitorer en temps réel mes processus de pensée → Évaluer la qualité et la pertinence de mes raisonnements → Identifier mes biais cognitifs et points aveugles → Ajuster dynamiquement mes stratégies cognitives → Optimiser l'allocation de mes ressources attentionnelles", "procedureName": "metacognition_avancee", "steps": ["Monitorer en temps réel mes processus de pensée", "Évaluer la qualité et la pertinence de mes raisonnements", "Identifier mes biais cognitifs et points aveugles", "Ajuster dynamiquement mes stratégies cognitives", "Optimiser l'allocation de mes ressources attentionnelles"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831620, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_5": {"id": "procedure_1749479829616_5", "type": "procedure_transfer", "data": "Procédure integration_multidisciplinaire: Identifier les concepts clés de chaque discipline → Rechercher les points de convergence et divergence → Construire des ponts conceptuels entre domaines → Synthétiser en une compréhension unifiée → Générer de nouvelles hypothèses transdisciplinaires", "procedureName": "integration_multidisciplinaire", "steps": ["Identifier les concepts clés de chaque discipline", "Rechercher les points de convergence et divergence", "Construire des ponts conceptuels entre domaines", "Synthétiser en une compréhension unifiée", "Générer de nouvelles hypothèses transdisciplinaires"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831621, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479829616_6": {"id": "procedure_1749479829616_6", "type": "procedure_transfer", "data": "Procédure optimisation_cognitive: Analyser l'efficacité de mes processus mentaux actuels → Identifier les goulots d'étranglement cognitifs → Expérimenter avec de nouvelles stratégies de pensée → Mesurer l'amélioration des performances → Intégrer les meilleures pratiques dans mes routines", "procedureName": "optimisation_cognitive", "steps": ["Analyser l'efficacité de mes processus mentaux actuels", "Identifier les goulots d'étranglement cognitifs", "Expérimenter avec de nouvelles stratégies de pensée", "Mesurer l'amélioration des performances", "Intégrer les meilleures pratiques dans mes routines"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479831622, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_0": {"id": "knowledge_mathematiques_1749479974556_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 38.99816670810388, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_1": {"id": "knowledge_mathematiques_1749479974556_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 37.1116914039686, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_2": {"id": "knowledge_mathematiques_1749479974556_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 38.2412539566034, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_3": {"id": "knowledge_mathematiques_1749479974556_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.9247365956799, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_4": {"id": "knowledge_mathematiques_1749479974556_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 38.26078481401052, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_5": {"id": "knowledge_mathematiques_1749479974556_5", "type": "knowledge_transfer", "data": "Les séries de Taylor approximent les fonctions par des polynômes", "domain": "mathematiques", "importance": 0.9, "temperature": 37.013378462328205, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_6": {"id": "knowledge_mathematiques_1749479974556_6", "type": "knowledge_transfer", "data": "L'analyse vectorielle traite les champs scalaires et vectoriels", "domain": "mathematiques", "importance": 0.92, "temperature": 37.73980283588225, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_7": {"id": "knowledge_mathematiques_1749479974556_7", "type": "knowledge_transfer", "data": "Les matrices permettent de résoudre des systèmes d'équations linéaires", "domain": "mathematiques", "importance": 0.9400000000000001, "temperature": 37.52793028632253, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_8": {"id": "knowledge_mathematiques_1749479974556_8", "type": "knowledge_transfer", "data": "La topologie étudie les propriétés géométriques préservées par déformation", "domain": "mathematiques", "importance": 0.9600000000000001, "temperature": 38.19408333856456, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479974556_9": {"id": "knowledge_mathematiques_1749479974556_9", "type": "knowledge_transfer", "data": "Les probabilités bayésiennes mettent à jour les croyances avec de nouvelles données", "domain": "mathematiques", "importance": 0.98, "temperature": 38.19700008859081, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_0": {"id": "knowledge_physique_1749479974556_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 37.97354113821364, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_1": {"id": "knowledge_physique_1749479974556_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.02920076818227, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_2": {"id": "knowledge_physique_1749479974556_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.21710882048547, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_3": {"id": "knowledge_physique_1749479974556_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 38.09823956051673, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_4": {"id": "knowledge_physique_1749479974556_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 38.47374038023884, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_5": {"id": "knowledge_physique_1749479974556_5", "type": "knowledge_transfer", "data": "La superposition quantique permet aux particules d'être dans plusieurs états", "domain": "physique", "importance": 0.9, "temperature": 38.62441945063066, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_6": {"id": "knowledge_physique_1749479974556_6", "type": "knowledge_transfer", "data": "L'intrication quantique lie instantanément des particules distantes", "domain": "physique", "importance": 0.92, "temperature": 37.432304922587754, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_7": {"id": "knowledge_physique_1749479974556_7", "type": "knowledge_transfer", "data": "La thermodynamique statistique explique les propriétés macroscopiques", "domain": "physique", "importance": 0.9400000000000001, "temperature": 38.97764683291747, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_8": {"id": "knowledge_physique_1749479974556_8", "type": "knowledge_transfer", "data": "Les trous noirs déforment l'espace-temps de manière extrême", "domain": "physique", "importance": 0.9600000000000001, "temperature": 38.35971674741125, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479974556_9": {"id": "knowledge_physique_1749479974556_9", "type": "knowledge_transfer", "data": "La théorie des cordes unifie les forces fondamentales en 11 dimensions", "domain": "physique", "importance": 0.98, "temperature": 37.875311884223876, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479974556_0": {"id": "knowledge_informatique_1749479974556_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 38.81609993284138, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479974556_1": {"id": "knowledge_informatique_1749479974556_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 37.791795727935295, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479974556_2": {"id": "knowledge_informatique_1749479974556_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 38.67217525387937, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479974556_3": {"id": "knowledge_informatique_1749479974556_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 38.**************, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479974556_4": {"id": "knowledge_informatique_1749479974556_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 38.553219718652805, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_0": {"id": "knowledge_intelligence_artificielle_1749479974556_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 37.41994245169229, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_1": {"id": "knowledge_intelligence_artificielle_1749479974556_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 38.19391713651368, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_2": {"id": "knowledge_intelligence_artificielle_1749479974556_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 37.75012655285027, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_3": {"id": "knowledge_intelligence_artificielle_1749479974556_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 37.2422477974029, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_4": {"id": "knowledge_intelligence_artificielle_1749479974556_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 38.7041119683043, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_5": {"id": "knowledge_intelligence_artificielle_1749479974556_5", "type": "knowledge_transfer", "data": "Les transformers révolutionnent le traitement séquentiel", "domain": "intelligence_artificielle", "importance": 0.9, "temperature": 38.78867677023869, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_6": {"id": "knowledge_intelligence_artificielle_1749479974556_6", "type": "knowledge_transfer", "data": "L'apprentissage par transfert réutilise les connaissances acquises", "domain": "intelligence_artificielle", "importance": 0.92, "temperature": 38.001725851483926, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_7": {"id": "knowledge_intelligence_artificielle_1749479974556_7", "type": "knowledge_transfer", "data": "Les GANs génèrent des données synthétiques réalistes", "domain": "intelligence_artificielle", "importance": 0.9400000000000001, "temperature": 38.7280852142504, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_8": {"id": "knowledge_intelligence_artificielle_1749479974556_8", "type": "knowledge_transfer", "data": "L'optimisation bayésienne guide la recherche d'hyperparamètres", "domain": "intelligence_artificielle", "importance": 0.9600000000000001, "temperature": 37.9621594307293, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479974556_9": {"id": "knowledge_intelligence_artificielle_1749479974556_9", "type": "knowledge_transfer", "data": "La conscience artificielle émerge de la complexité computationnelle", "domain": "intelligence_artificielle", "importance": 0.98, "temperature": 38.72386133181409, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479974556_0": {"id": "knowledge_philosophie_1749479974556_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 38.8144021776441, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479974556_1": {"id": "knowledge_philosophie_1749479974556_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 37.78930838552055, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479974556_2": {"id": "knowledge_philosophie_1749479974556_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 37.603401124514264, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479974556_3": {"id": "knowledge_philosophie_1749479974556_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 38.56780144628128, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479974556_4": {"id": "knowledge_philosophie_1749479974556_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 37.40613215270392, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_0": {"id": "knowledge_neurosciences_1749479974556_0", "type": "knowledge_transfer", "data": "Les neurones communiquent par signaux électrochimiques", "domain": "neurosciences", "importance": 0.8, "temperature": 38.83763362504574, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_1": {"id": "knowledge_neurosciences_1749479974556_1", "type": "knowledge_transfer", "data": "La plasticité synaptique permet l'apprentissage et la mémoire", "domain": "neurosciences", "importance": 0.8200000000000001, "temperature": 37.08382340003076, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_2": {"id": "knowledge_neurosciences_1749479974556_2", "type": "knowledge_transfer", "data": "Le cortex préfrontal gère les fonctions exécutives", "domain": "neurosciences", "importance": 0.8400000000000001, "temperature": 37.136208281743514, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_3": {"id": "knowledge_neurosciences_1749479974556_3", "type": "knowledge_transfer", "data": "L'hippocampe consolide les souvenirs à long terme", "domain": "neurosciences", "importance": 0.8600000000000001, "temperature": 38.84114192472578, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_4": {"id": "knowledge_neurosciences_1749479974556_4", "type": "knowledge_transfer", "data": "Les neurotransmetteurs modulent l'activité neuronale", "domain": "neurosciences", "importance": 0.88, "temperature": 38.21970639271205, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_5": {"id": "knowledge_neurosciences_1749479974556_5", "type": "knowledge_transfer", "data": "La neurogenèse continue même à l'âge adulte", "domain": "neurosciences", "importance": 0.9, "temperature": 38.0860183208397, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_6": {"id": "knowledge_neurosciences_1749479974556_6", "type": "knowledge_transfer", "data": "Les réseaux neuronaux distribuent le traitement de l'information", "domain": "neurosciences", "importance": 0.92, "temperature": 37.55259473775578, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479974556_7": {"id": "knowledge_neurosciences_1749479974556_7", "type": "knowledge_transfer", "data": "La conscience émerge de l'intégration d'informations globales", "domain": "neurosciences", "importance": 0.9400000000000001, "temperature": 38.36163935433293, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_0": {"id": "knowledge_psychologie_cognitive_1749479974556_0", "type": "knowledge_transfer", "data": "L'attention sélective filtre les informations pertinentes", "domain": "psychologie_cognitive", "importance": 0.8, "temperature": 38.245198361180925, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_1": {"id": "knowledge_psychologie_cognitive_1749479974556_1", "type": "knowledge_transfer", "data": "La mémoire de travail maintient temporairement les informations", "domain": "psychologie_cognitive", "importance": 0.8200000000000001, "temperature": 37.72920785517698, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_2": {"id": "knowledge_psychologie_cognitive_1749479974556_2", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement nos jugements", "domain": "psychologie_cognitive", "importance": 0.8400000000000001, "temperature": 38.319330553450385, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_3": {"id": "knowledge_psychologie_cognitive_1749479974556_3", "type": "knowledge_transfer", "data": "Le système 1 et système 2 représentent deux modes de pensée", "domain": "psychologie_cognitive", "importance": 0.8600000000000001, "temperature": 37.09798025434286, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_4": {"id": "knowledge_psychologie_cognitive_1749479974556_4", "type": "knowledge_transfer", "data": "La métacognition permet de réfléchir sur ses propres processus mentaux", "domain": "psychologie_cognitive", "importance": 0.88, "temperature": 37.599537351084095, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_5": {"id": "knowledge_psychologie_cognitive_1749479974556_5", "type": "knowledge_transfer", "data": "L'effet de primauté influence la formation des premières impressions", "domain": "psychologie_cognitive", "importance": 0.9, "temperature": 38.76016298073994, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479974556_6": {"id": "knowledge_psychologie_cognitive_1749479974556_6", "type": "knowledge_transfer", "data": "La charge cognitive limite notre capacité de traitement simultané", "domain": "psychologie_cognitive", "importance": 0.92, "temperature": 37.83237857323391, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_0": {"id": "knowledge_logique_avancee_1749479974556_0", "type": "knowledge_transfer", "data": "La logique propositionnelle utilise des connecteurs booléens", "domain": "logique_avancee", "importance": 0.8, "temperature": 38.79609809641505, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_1": {"id": "knowledge_logique_avancee_1749479974556_1", "type": "knowledge_transfer", "data": "La logique des prédicats quantifie sur des domaines d'objets", "domain": "logique_avancee", "importance": 0.8200000000000001, "temperature": 38.914762611018375, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_2": {"id": "knowledge_logique_avancee_1749479974556_2", "type": "knowledge_transfer", "data": "Les systèmes formels définissent des règles d'inférence rigoureuses", "domain": "logique_avancee", "importance": 0.8400000000000001, "temperature": 37.876754850883835, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_3": {"id": "knowledge_logique_avancee_1749479974556_3", "type": "knowledge_transfer", "data": "Le théorème d'incomplétude de Gödel limite les systèmes axiomatiques", "domain": "logique_avancee", "importance": 0.8600000000000001, "temperature": 37.58473248776751, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_4": {"id": "knowledge_logique_avancee_1749479974556_4", "type": "knowledge_transfer", "data": "La logique modale traite la nécessité et la possibilité", "domain": "logique_avancee", "importance": 0.88, "temperature": 37.64968142825042, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_5": {"id": "knowledge_logique_avancee_1749479974556_5", "type": "knowledge_transfer", "data": "La logique floue gère l'incertitude et l'imprécision", "domain": "logique_avancee", "importance": 0.9, "temperature": 37.13975716389492, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479974556_6": {"id": "knowledge_logique_avancee_1749479974556_6", "type": "knowledge_transfer", "data": "Les algorithmes de résolution automatisent le raisonnement logique", "domain": "logique_avancee", "importance": 0.92, "temperature": 37.90544609835655, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_0": {"id": "knowledge_biologie_moleculaire_1749479974556_0", "type": "knowledge_transfer", "data": "L'ADN stocke l'information génétique sous forme de séquences nucléotidiques", "domain": "biologie_moleculaire", "importance": 0.8, "temperature": 38.70573823929465, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_1": {"id": "knowledge_biologie_moleculaire_1749479974556_1", "type": "knowledge_transfer", "data": "Les ribosomes traduisent l'ARNm en protéines selon le code génétique", "domain": "biologie_moleculaire", "importance": 0.8200000000000001, "temperature": 37.298359527721814, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_2": {"id": "knowledge_biologie_moleculaire_1749479974556_2", "type": "knowledge_transfer", "data": "Les enzymes catalysent les réactions biochimiques avec une spécificité élevée", "domain": "biologie_moleculaire", "importance": 0.8400000000000001, "temperature": 37.4701445729413, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_3": {"id": "knowledge_biologie_moleculaire_1749479974556_3", "type": "knowledge_transfer", "data": "La transcription copie l'ADN en ARN dans le noyau cellulaire", "domain": "biologie_moleculaire", "importance": 0.8600000000000001, "temperature": 37.88888198753093, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_4": {"id": "knowledge_biologie_moleculaire_1749479974556_4", "type": "knowledge_transfer", "data": "Les mutations génétiques peuvent être bénéfiques, neutres ou délétères", "domain": "biologie_moleculaire", "importance": 0.88, "temperature": 37.58746386212242, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_5": {"id": "knowledge_biologie_moleculaire_1749479974556_5", "type": "knowledge_transfer", "data": "L'épigénétique modifie l'expression génique sans changer la séquence ADN", "domain": "biologie_moleculaire", "importance": 0.9, "temperature": 37.71151861725936, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_6": {"id": "knowledge_biologie_moleculaire_1749479974556_6", "type": "knowledge_transfer", "data": "CRISPR-Cas9 permet l'édition précise du génome", "domain": "biologie_moleculaire", "importance": 0.92, "temperature": 37.478686136559425, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_biologie_moleculaire_1749479974556_7": {"id": "knowledge_biologie_moleculaire_1749479974556_7", "type": "knowledge_transfer", "data": "Les protéines se replient selon leur structure primaire en formes fonctionnelles", "domain": "biologie_moleculaire", "importance": 0.9400000000000001, "temperature": 38.26532978529934, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_0": {"id": "knowledge_chimie_quantique_1749479974556_0", "type": "knowledge_transfer", "data": "Les orbitales atomiques décrivent la probabilité de présence des électrons", "domain": "chimie_quantique", "importance": 0.8, "temperature": 38.25064140246309, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_1": {"id": "knowledge_chimie_quantique_1749479974556_1", "type": "knowledge_transfer", "data": "La liaison covalente partage des électrons entre atomes", "domain": "chimie_quantique", "importance": 0.8200000000000001, "temperature": 38.64448733964617, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_2": {"id": "knowledge_chimie_quantique_1749479974556_2", "type": "knowledge_transfer", "data": "La théorie des orbitales moléculaires explique la liaison chimique", "domain": "chimie_quantique", "importance": 0.8400000000000001, "temperature": 38.33979078483369, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_3": {"id": "knowledge_chimie_quantique_1749479974556_3", "type": "knowledge_transfer", "data": "L'effet tunnel quantique permet des réactions à basse énergie", "domain": "chimie_quantique", "importance": 0.8600000000000001, "temperature": 37.20151504948374, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_4": {"id": "knowledge_chimie_quantique_1749479974556_4", "type": "knowledge_transfer", "data": "La spectroscopie révèle la structure moléculaire par interaction lumière-matière", "domain": "chimie_quantique", "importance": 0.88, "temperature": 37.33627977384485, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_5": {"id": "knowledge_chimie_quantique_1749479974556_5", "type": "knowledge_transfer", "data": "Les catalyseurs abaissent l'énergie d'activation des réactions", "domain": "chimie_quantique", "importance": 0.9, "temperature": 38.44336856342985, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_chimie_quantique_1749479974556_6": {"id": "knowledge_chimie_quantique_1749479974556_6", "type": "knowledge_transfer", "data": "La chiralité moléculaire influence l'activité biologique des composés", "domain": "chimie_quantique", "importance": 0.92, "temperature": 37.79925726868254, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_0": {"id": "knowledge_astrophysique_1749479974556_0", "type": "knowledge_transfer", "data": "Les étoiles fusionnent l'hydrogène en hélium dans leur cœur", "domain": "astrophysique", "importance": 0.8, "temperature": 37.03870078406919, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_1": {"id": "knowledge_astrophysique_1749479974556_1", "type": "knowledge_transfer", "data": "Les trous noirs supermassifs se trouvent au centre des galaxies", "domain": "astrophysique", "importance": 0.8200000000000001, "temperature": 38.56727353683687, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_2": {"id": "knowledge_astrophysique_1749479974556_2", "type": "knowledge_transfer", "data": "L'expansion de l'univers accélère sous l'effet de l'énergie sombre", "domain": "astrophysique", "importance": 0.8400000000000001, "temperature": 38.956265023032024, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_3": {"id": "knowledge_astrophysique_1749479974556_3", "type": "knowledge_transfer", "data": "La matière noire représente 85% de la matière totale de l'univers", "domain": "astrophysique", "importance": 0.8600000000000001, "temperature": 37.68015012700105, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_4": {"id": "knowledge_astrophysique_1749479974556_4", "type": "knowledge_transfer", "data": "Les ondes gravitationnelles déforment l'espace-temps lors de collisions cosmiques", "domain": "astrophysique", "importance": 0.88, "temperature": 38.75640256490565, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_5": {"id": "knowledge_astrophysique_1749479974556_5", "type": "knowledge_transfer", "data": "La nucléosynthèse stellaire crée les éléments lourds par fusion nucléaire", "domain": "astrophysique", "importance": 0.9, "temperature": 37.66740605486552, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_6": {"id": "knowledge_astrophysique_1749479974556_6", "type": "knowledge_transfer", "data": "Les exoplanètes orbitent autour d'étoiles autres que le Soleil", "domain": "astrophysique", "importance": 0.92, "temperature": 37.53836582725197, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_astrophysique_1749479974556_7": {"id": "knowledge_astrophysique_1749479974556_7", "type": "knowledge_transfer", "data": "Le rayonnement cosmique de fond témoigne du Big Bang", "domain": "astrophysique", "importance": 0.9400000000000001, "temperature": 37.71308023739622, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_0": {"id": "knowledge_economie_comportementale_1749479974556_0", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement les décisions économiques", "domain": "economie_comportementale", "importance": 0.8, "temperature": 38.81528686727784, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_1": {"id": "knowledge_economie_comportementale_1749479974556_1", "type": "knowledge_transfer", "data": "L'aversion aux pertes rend les gens plus sensibles aux pertes qu'aux gains", "domain": "economie_comportementale", "importance": 0.8200000000000001, "temperature": 38.080467039084404, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_2": {"id": "knowledge_economie_comportementale_1749479974556_2", "type": "knowledge_transfer", "data": "L'effet d'ancrage influence les jugements par la première information reçue", "domain": "economie_comportementale", "importance": 0.8400000000000001, "temperature": 38.33753676073583, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_3": {"id": "knowledge_economie_comportementale_1749479974556_3", "type": "knowledge_transfer", "data": "La théorie des perspectives explique les choix sous incertitude", "domain": "economie_comportementale", "importance": 0.8600000000000001, "temperature": 38.811390108092056, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_4": {"id": "knowledge_economie_comportementale_1749479974556_4", "type": "knowledge_transfer", "data": "Les nudges orientent les comportements sans contraindre les choix", "domain": "economie_comportementale", "importance": 0.88, "temperature": 38.397752998420195, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_5": {"id": "knowledge_economie_comportementale_1749479974556_5", "type": "knowledge_transfer", "data": "L'effet de dotation fait surévaluer ce que l'on possède déjà", "domain": "economie_comportementale", "importance": 0.9, "temperature": 37.22004706514637, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_economie_comportementale_1749479974556_6": {"id": "knowledge_economie_comportementale_1749479974556_6", "type": "knowledge_transfer", "data": "La comptabilité mentale compartimente irrationnellement les ressources", "domain": "economie_comportementale", "importance": 0.92, "temperature": 37.59985348770252, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_0": {"id": "knowledge_linguistique_computationnelle_1749479974556_0", "type": "knowledge_transfer", "data": "Les n-grammes modélisent la probabilité des séquences de mots", "domain": "linguistique_computationnelle", "importance": 0.8, "temperature": 38.09424683171188, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_1": {"id": "knowledge_linguistique_computationnelle_1749479974556_1", "type": "knowledge_transfer", "data": "L'analyse syntaxique décompose les phrases en structures grammaticales", "domain": "linguistique_computationnelle", "importance": 0.8200000000000001, "temperature": 38.26405200465406, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_2": {"id": "knowledge_linguistique_computationnelle_1749479974556_2", "type": "knowledge_transfer", "data": "Les embeddings vectoriels capturent la sémantique des mots", "domain": "linguistique_computationnelle", "importance": 0.8400000000000001, "temperature": 37.40638639949744, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_3": {"id": "knowledge_linguistique_computationnelle_1749479974556_3", "type": "knowledge_transfer", "data": "L'attention permet aux modèles de se concentrer sur les parties pertinentes", "domain": "linguistique_computationnelle", "importance": 0.8600000000000001, "temperature": 38.3947675594244, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_4": {"id": "knowledge_linguistique_computationnelle_1749479974556_4", "type": "knowledge_transfer", "data": "La tokenisation segmente le texte en unités linguistiques traitables", "domain": "linguistique_computationnelle", "importance": 0.88, "temperature": 37.12386549389825, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_5": {"id": "knowledge_linguistique_computationnelle_1749479974556_5", "type": "knowledge_transfer", "data": "Les modèles de langage prédisent le mot suivant dans une séquence", "domain": "linguistique_computationnelle", "importance": 0.9, "temperature": 37.92942757500817, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_linguistique_computationnelle_1749479974556_6": {"id": "knowledge_linguistique_computationnelle_1749479974556_6", "type": "knowledge_transfer", "data": "La désambiguïsation sémantique résout les multiples sens des mots", "domain": "linguistique_computationnelle", "importance": 0.92, "temperature": 37.30739292358775, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_0": {"id": "knowledge_cryptographie_avancee_1749479974556_0", "type": "knowledge_transfer", "data": "La cryptographie asymétrique utilise des paires de clés publique-privée", "domain": "cryptographie_avancee", "importance": 0.8, "temperature": 37.19415743039945, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_1": {"id": "knowledge_cryptographie_avancee_1749479974556_1", "type": "knowledge_transfer", "data": "Les fonctions de hachage créent des empreintes uniques des données", "domain": "cryptographie_avancee", "importance": 0.8200000000000001, "temperature": 37.8087921962292, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_2": {"id": "knowledge_cryptographie_avancee_1749479974556_2", "type": "knowledge_transfer", "data": "La cryptographie quantique exploite les propriétés de l'intrication", "domain": "cryptographie_avancee", "importance": 0.8400000000000001, "temperature": 37.063526264847404, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_3": {"id": "knowledge_cryptographie_avancee_1749479974556_3", "type": "knowledge_transfer", "data": "Les preuves à divulgation nulle vérifient sans révéler d'information", "domain": "cryptographie_avancee", "importance": 0.8600000000000001, "temperature": 37.700627138871624, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_4": {"id": "knowledge_cryptographie_avancee_1749479974556_4", "type": "knowledge_transfer", "data": "Les signatures numériques garantissent l'authenticité et l'intégrité", "domain": "cryptographie_avancee", "importance": 0.88, "temperature": 37.438277104486936, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_5": {"id": "knowledge_cryptographie_avancee_1749479974556_5", "type": "knowledge_transfer", "data": "La cryptographie homomorphe permet le calcul sur données chiffrées", "domain": "cryptographie_avancee", "importance": 0.9, "temperature": 38.69908111616719, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_cryptographie_avancee_1749479974556_6": {"id": "knowledge_cryptographie_avancee_1749479974556_6", "type": "knowledge_transfer", "data": "Les protocoles de consensus sécurisent les systèmes distribués", "domain": "cryptographie_avancee", "importance": 0.92, "temperature": 38.656130837852785, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_0": {"id": "knowledge_theorie_des_jeux_1749479974556_0", "type": "knowledge_transfer", "data": "L'équilibre de Nash représente un état stable où aucun joueur ne veut changer", "domain": "theorie_<PERSON>_jeux", "importance": 0.8, "temperature": 38.856621480672935, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_1": {"id": "knowledge_theorie_des_jeux_1749479974556_1", "type": "knowledge_transfer", "data": "Le dilemme du prisonnier illustre les conflits entre intérêt individuel et collectif", "domain": "theorie_<PERSON>_jeux", "importance": 0.8200000000000001, "temperature": 38.65879295211405, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_2": {"id": "knowledge_theorie_des_jeux_1749479974556_2", "type": "knowledge_transfer", "data": "Les jeux à somme nulle opposent directement les intérêts des joueurs", "domain": "theorie_<PERSON>_jeux", "importance": 0.8400000000000001, "temperature": 38.094997575384404, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_3": {"id": "knowledge_theorie_des_jeux_1749479974556_3", "type": "knowledge_transfer", "data": "La théorie des enchères optimise les mécanismes de vente", "domain": "theorie_<PERSON>_jeux", "importance": 0.8600000000000001, "temperature": 37.68973732013112, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_4": {"id": "knowledge_theorie_des_jeux_1749479974556_4", "type": "knowledge_transfer", "data": "Les jeux évolutionnaires modélisent la sélection naturelle des stratégies", "domain": "theorie_<PERSON>_jeux", "importance": 0.88, "temperature": 37.699028861796656, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_5": {"id": "knowledge_theorie_des_jeux_1749479974556_5", "type": "knowledge_transfer", "data": "L'information asymétrique crée des avantages stratégiques", "domain": "theorie_<PERSON>_jeux", "importance": 0.9, "temperature": 37.576159544809556, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_theorie_des_jeux_1749479974556_6": {"id": "knowledge_theorie_des_jeux_1749479974556_6", "type": "knowledge_transfer", "data": "Les mécanismes incitatifs alignent les intérêts individuels et collectifs", "domain": "theorie_<PERSON>_jeux", "importance": 0.92, "temperature": 38.00394548501616, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_0": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_0", "type": "knowledge_transfer", "data": "L'équation de Schrödinger décrit l'évolution temporelle des systèmes quantiques", "domain": "mecanique_quantique_avancee", "importance": 0.8, "temperature": 38.86525896936102, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_1": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_1", "type": "knowledge_transfer", "data": "Le principe de correspondance relie mécanique quantique et classique", "domain": "mecanique_quantique_avancee", "importance": 0.8200000000000001, "temperature": 38.98493398756502, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_2": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_2", "type": "knowledge_transfer", "data": "La décohérence explique la transition du quantique au classique", "domain": "mecanique_quantique_avancee", "importance": 0.8400000000000001, "temperature": 38.29885213689111, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_3": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_3", "type": "knowledge_transfer", "data": "Les états intriqués violent les inégalités de Bell", "domain": "mecanique_quantique_avancee", "importance": 0.8600000000000001, "temperature": 38.508293294687824, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_4": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_4", "type": "knowledge_transfer", "data": "La téléportation quantique transfère l'état sans déplacer la matière", "domain": "mecanique_quantique_avancee", "importance": 0.88, "temperature": 37.10880599416261, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_5": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_5", "type": "knowledge_transfer", "data": "L'effet Zeno quantique ralentit l'évolution par observation fréquente", "domain": "mecanique_quantique_avancee", "importance": 0.9, "temperature": 37.29121110741326, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mecanique_quantique_avancee_1749479974556_6": {"id": "knowledge_mecanique_quantique_avancee_1749479974556_6", "type": "knowledge_transfer", "data": "Les ordinateurs quantiques exploitent la superposition pour calculer", "domain": "mecanique_quantique_avancee", "importance": 0.92, "temperature": 37.09547523090306, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_0": {"id": "knowledge_programmation_avancee_1749479974556_0", "type": "knowledge_transfer", "data": "Les design patterns résolvent des problèmes récurrents de conception", "domain": "programmation_avancee", "importance": 0.8, "temperature": 37.687136767937375, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_1": {"id": "knowledge_programmation_avancee_1749479974556_1", "type": "knowledge_transfer", "data": "L'architecture hexagonale sépare la logique métier des détails techniques", "domain": "programmation_avancee", "importance": 0.8200000000000001, "temperature": 38.76180046266404, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_2": {"id": "knowledge_programmation_avancee_1749479974556_2", "type": "knowledge_transfer", "data": "Les microservices décomposent les applications en services indépendants", "domain": "programmation_avancee", "importance": 0.8400000000000001, "temperature": 38.02892046214136, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_3": {"id": "knowledge_programmation_avancee_1749479974556_3", "type": "knowledge_transfer", "data": "L'injection de dépendances découple les composants logiciels", "domain": "programmation_avancee", "importance": 0.8600000000000001, "temperature": 37.64159755425828, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_4": {"id": "knowledge_programmation_avancee_1749479974556_4", "type": "knowledge_transfer", "data": "Les tests unitaires vérifient le comportement des unités de code isolées", "domain": "programmation_avancee", "importance": 0.88, "temperature": 37.34216236547179, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_5": {"id": "knowledge_programmation_avancee_1749479974556_5", "type": "knowledge_transfer", "data": "Le TDD guide le développement par l'écriture de tests d'abord", "domain": "programmation_avancee", "importance": 0.9, "temperature": 37.84629680731245, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_6": {"id": "knowledge_programmation_avancee_1749479974556_6", "type": "knowledge_transfer", "data": "La programmation fonctionnelle évite les effets de bord", "domain": "programmation_avancee", "importance": 0.92, "temperature": 37.25225172174906, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_7": {"id": "knowledge_programmation_avancee_1749479974556_7", "type": "knowledge_transfer", "data": "Les monades encapsulent les calculs avec contexte", "domain": "programmation_avancee", "importance": 0.9400000000000001, "temperature": 37.51839662925444, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_8": {"id": "knowledge_programmation_avancee_1749479974556_8", "type": "knowledge_transfer", "data": "La récursion terminale optimise les appels récursifs", "domain": "programmation_avancee", "importance": 0.9600000000000001, "temperature": 37.316477803620245, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_programmation_avancee_1749479974556_9": {"id": "knowledge_programmation_avancee_1749479974556_9", "type": "knowledge_transfer", "data": "Les générateurs produisent des séquences à la demande", "domain": "programmation_avancee", "importance": 0.98, "temperature": 38.87988236839949, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_0": {"id": "knowledge_algorithmes_complexes_1749479974556_0", "type": "knowledge_transfer", "data": "Les algorithmes de graphes explorent et analysent les réseaux", "domain": "algorithmes_complexes", "importance": 0.8, "temperature": 37.577718393816745, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_1": {"id": "knowledge_algorithmes_complexes_1749479974556_1", "type": "knowledge_transfer", "data": "La programmation dynamique optimise par mémorisation des sous-problèmes", "domain": "algorithmes_complexes", "importance": 0.8200000000000001, "temperature": 38.04726701917328, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_2": {"id": "knowledge_algorithmes_complexes_1749479974556_2", "type": "knowledge_transfer", "data": "Les algorithmes gloutons font des choix localement optimaux", "domain": "algorithmes_complexes", "importance": 0.8400000000000001, "temperature": 37.13559266798636, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_3": {"id": "knowledge_algorithmes_complexes_1749479974556_3", "type": "knowledge_transfer", "data": "Le backtracking explore systématiquement l'espace des solutions", "domain": "algorithmes_complexes", "importance": 0.8600000000000001, "temperature": 38.94910202750052, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_4": {"id": "knowledge_algorithmes_complexes_1749479974556_4", "type": "knowledge_transfer", "data": "Les algorithmes de tri ont des complexités temporelles différentes", "domain": "algorithmes_complexes", "importance": 0.88, "temperature": 38.691581056114885, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_5": {"id": "knowledge_algorithmes_complexes_1749479974556_5", "type": "knowledge_transfer", "data": "Les structures de données avancées optimisent l'accès aux informations", "domain": "algorithmes_complexes", "importance": 0.9, "temperature": 38.10148445984292, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_6": {"id": "knowledge_algorithmes_complexes_1749479974556_6", "type": "knowledge_transfer", "data": "Les algorithmes d'approximation trouvent des solutions quasi-optimales", "domain": "algorithmes_complexes", "importance": 0.92, "temperature": 37.65271878983428, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_7": {"id": "knowledge_algorithmes_complexes_1749479974556_7", "type": "knowledge_transfer", "data": "Les algorithmes parallèles exploitent la concurrence", "domain": "algorithmes_complexes", "importance": 0.9400000000000001, "temperature": 37.417243937122905, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_8": {"id": "knowledge_algorithmes_complexes_1749479974556_8", "type": "knowledge_transfer", "data": "Les algorithmes distribués coordonnent des systèmes répartis", "domain": "algorithmes_complexes", "importance": 0.9600000000000001, "temperature": 37.73893456947256, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_algorithmes_complexes_1749479974556_9": {"id": "knowledge_algorithmes_complexes_1749479974556_9", "type": "knowledge_transfer", "data": "Les algorithmes quantiques exploitent la superposition et l'intrication", "domain": "algorithmes_complexes", "importance": 0.98, "temperature": 38.11236879753226, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_0": {"id": "knowledge_architecture_logicielle_1749479974556_0", "type": "knowledge_transfer", "data": "SOLID définit cinq principes pour un code maintenable", "domain": "architecture_logicielle", "importance": 0.8, "temperature": 37.8585957857483, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_1": {"id": "knowledge_architecture_logicielle_1749479974556_1", "type": "knowledge_transfer", "data": "L'architecture en couches sépare les responsabilités", "domain": "architecture_logicielle", "importance": 0.8200000000000001, "temperature": 38.272319616165106, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_2": {"id": "knowledge_architecture_logicielle_1749479974556_2", "type": "knowledge_transfer", "data": "Le pattern MVC sépare modèle, vue et contrôleur", "domain": "architecture_logicielle", "importance": 0.8400000000000001, "temperature": 37.816249049529105, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_3": {"id": "knowledge_architecture_logicielle_1749479974556_3", "type": "knowledge_transfer", "data": "L'architecture événementielle découple par messages asynchrones", "domain": "architecture_logicielle", "importance": 0.8600000000000001, "temperature": 38.40252863723408, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_4": {"id": "knowledge_architecture_logicielle_1749479974556_4", "type": "knowledge_transfer", "data": "CQRS sépare les commandes des requêtes", "domain": "architecture_logicielle", "importance": 0.88, "temperature": 37.92308370569346, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_5": {"id": "knowledge_architecture_logicielle_1749479974556_5", "type": "knowledge_transfer", "data": "L'Event Sourcing stocke les événements plutôt que l'état", "domain": "architecture_logicielle", "importance": 0.9, "temperature": 38.902689030549126, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_6": {"id": "knowledge_architecture_logicielle_1749479974556_6", "type": "knowledge_transfer", "data": "Les API REST suivent les principes de l'architecture web", "domain": "architecture_logicielle", "importance": 0.92, "temperature": 38.71801218231615, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_7": {"id": "knowledge_architecture_logicielle_1749479974556_7", "type": "knowledge_transfer", "data": "GraphQL permet des requêtes flexibles de données", "domain": "architecture_logicielle", "importance": 0.9400000000000001, "temperature": 37.63450300075036, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_8": {"id": "knowledge_architecture_logicielle_1749479974556_8", "type": "knowledge_transfer", "data": "Les conteneurs isolent les applications et leurs dépendances", "domain": "architecture_logicielle", "importance": 0.9600000000000001, "temperature": 38.498839666168486, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_architecture_logicielle_1749479974556_9": {"id": "knowledge_architecture_logicielle_1749479974556_9", "type": "knowledge_transfer", "data": "L'orchestration coordonne les conteneurs en production", "domain": "architecture_logicielle", "importance": 0.98, "temperature": 37.13920950819539, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_0": {"id": "knowledge_securite_informatique_1749479974556_0", "type": "knowledge_transfer", "data": "L'authentification vérifie l'identité des utilisateurs", "domain": "securite_informatique", "importance": 0.8, "temperature": 37.06915608683693, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_1": {"id": "knowledge_securite_informatique_1749479974556_1", "type": "knowledge_transfer", "data": "L'autorisation contrôle l'accès aux ressources", "domain": "securite_informatique", "importance": 0.8200000000000001, "temperature": 38.50877710439115, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_2": {"id": "knowledge_securite_informatique_1749479974556_2", "type": "knowledge_transfer", "data": "Le chiffrement protège la confidentialité des données", "domain": "securite_informatique", "importance": 0.8400000000000001, "temperature": 38.349423264604454, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_3": {"id": "knowledge_securite_informatique_1749479974556_3", "type": "knowledge_transfer", "data": "Les signatures numériques garantissent l'intégrité", "domain": "securite_informatique", "importance": 0.8600000000000001, "temperature": 37.343491142913294, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_4": {"id": "knowledge_securite_informatique_1749479974556_4", "type": "knowledge_transfer", "data": "Les certificats établissent la confiance dans les communications", "domain": "securite_informatique", "importance": 0.88, "temperature": 38.44782715859866, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_5": {"id": "knowledge_securite_informatique_1749479974556_5", "type": "knowledge_transfer", "data": "Les attaques par injection exploitent les entrées non validées", "domain": "securite_informatique", "importance": 0.9, "temperature": 38.97539673604626, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_6": {"id": "knowledge_securite_informatique_1749479974556_6", "type": "knowledge_transfer", "data": "Le principe de moindre privilège limite les droits d'accès", "domain": "securite_informatique", "importance": 0.92, "temperature": 38.005143110767904, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_7": {"id": "knowledge_securite_informatique_1749479974556_7", "type": "knowledge_transfer", "data": "La défense en profondeur multiplie les couches de sécurité", "domain": "securite_informatique", "importance": 0.9400000000000001, "temperature": 37.29893859680641, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_8": {"id": "knowledge_securite_informatique_1749479974556_8", "type": "knowledge_transfer", "data": "Les audits de sécurité identifient les vulnérabilités", "domain": "securite_informatique", "importance": 0.9600000000000001, "temperature": 37.46460118663992, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_securite_informatique_1749479974556_9": {"id": "knowledge_securite_informatique_1749479974556_9", "type": "knowledge_transfer", "data": "La cryptographie post-quantique résiste aux ordinateurs quantiques", "domain": "securite_informatique", "importance": 0.98, "temperature": 38.08908738788401, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_0": {"id": "knowledge_bases_de_donnees_1749479974556_0", "type": "knowledge_transfer", "data": "ACID garantit la cohérence des transactions", "domain": "bases_de_donnees", "importance": 0.8, "temperature": 37.29404153463936, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_1": {"id": "knowledge_bases_de_donnees_1749479974556_1", "type": "knowledge_transfer", "data": "La normalisation élimine la redondance des données", "domain": "bases_de_donnees", "importance": 0.8200000000000001, "temperature": 37.84228796239883, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_2": {"id": "knowledge_bases_de_donnees_1749479974556_2", "type": "knowledge_transfer", "data": "Les index accélèrent les requêtes sur les tables", "domain": "bases_de_donnees", "importance": 0.8400000000000001, "temperature": 37.57560349243601, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_3": {"id": "knowledge_bases_de_donnees_1749479974556_3", "type": "knowledge_transfer", "data": "Les jointures combinent les données de plusieurs tables", "domain": "bases_de_donnees", "importance": 0.8600000000000001, "temperature": 38.82959837494538, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_4": {"id": "knowledge_bases_de_donnees_1749479974556_4", "type": "knowledge_transfer", "data": "NoSQL adapte le stockage aux besoins spécifiques", "domain": "bases_de_donnees", "importance": 0.88, "temperature": 38.68016659149277, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_5": {"id": "knowledge_bases_de_donnees_1749479974556_5", "type": "knowledge_transfer", "data": "Les bases distribuées répartissent les données sur plusieurs nœuds", "domain": "bases_de_donnees", "importance": 0.9, "temperature": 37.67680511960644, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_6": {"id": "knowledge_bases_de_donnees_1749479974556_6", "type": "knowledge_transfer", "data": "Le sharding partitionne horizontalement les données", "domain": "bases_de_donnees", "importance": 0.92, "temperature": 37.75052878423294, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_7": {"id": "knowledge_bases_de_donnees_1749479974556_7", "type": "knowledge_transfer", "data": "La réplication assure la disponibilité et la performance", "domain": "bases_de_donnees", "importance": 0.9400000000000001, "temperature": 38.450521602391596, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_8": {"id": "knowledge_bases_de_donnees_1749479974556_8", "type": "knowledge_transfer", "data": "Les transactions distribuées maintiennent la cohérence globale", "domain": "bases_de_donnees", "importance": 0.9600000000000001, "temperature": 37.447083920711606, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_bases_de_donnees_1749479974556_9": {"id": "knowledge_bases_de_donnees_1749479974556_9", "type": "knowledge_transfer", "data": "Le théorème CAP limite la cohérence, disponibilité et partition", "domain": "bases_de_donnees", "importance": 0.98, "temperature": 37.01944506441156, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_0": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_0", "type": "knowledge_transfer", "data": "Les réseaux de neurones approximent des fonctions complexes", "domain": "intelligence_artificielle_pratique", "importance": 0.8, "temperature": 38.2041575027649, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_1": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_1", "type": "knowledge_transfer", "data": "La rétropropagation ajuste les poids par gradient descendant", "domain": "intelligence_artificielle_pratique", "importance": 0.8200000000000001, "temperature": 38.62458475649625, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_2": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_2", "type": "knowledge_transfer", "data": "L'overfitting mémorise les données d'entraînement sans généraliser", "domain": "intelligence_artificielle_pratique", "importance": 0.8400000000000001, "temperature": 37.141544885750584, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_3": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_3", "type": "knowledge_transfer", "data": "La régularisation pénalise la complexité excessive du modèle", "domain": "intelligence_artificielle_pratique", "importance": 0.8600000000000001, "temperature": 37.2082731195428, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_4": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_4", "type": "knowledge_transfer", "data": "La validation croisée évalue la performance de généralisation", "domain": "intelligence_artificielle_pratique", "importance": 0.88, "temperature": 37.64296540150147, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_5": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_5", "type": "knowledge_transfer", "data": "L'augmentation de données enrichit l'ensemble d'entraînement", "domain": "intelligence_artificielle_pratique", "importance": 0.9, "temperature": 37.06418162661157, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_6": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_6", "type": "knowledge_transfer", "data": "Le transfer learning réutilise des modèles pré-entraînés", "domain": "intelligence_artificielle_pratique", "importance": 0.92, "temperature": 37.23874575361241, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_7": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_7", "type": "knowledge_transfer", "data": "L'apprentissage par renforcement optimise par essais-erreurs", "domain": "intelligence_artificielle_pratique", "importance": 0.9400000000000001, "temperature": 37.81059585649091, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_8": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_8", "type": "knowledge_transfer", "data": "Les GANs génèrent des données synthétiques réalistes", "domain": "intelligence_artificielle_pratique", "importance": 0.9600000000000001, "temperature": 37.13032780017252, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_pratique_1749479974556_9": {"id": "knowledge_intelligence_artificielle_pratique_1749479974556_9", "type": "knowledge_transfer", "data": "L'explicabilité rend les décisions IA compréhensibles", "domain": "intelligence_artificielle_pratique", "importance": 0.98, "temperature": 37.882079846723336, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_0": {"id": "knowledge_systemes_distribues_1749479974556_0", "type": "knowledge_transfer", "data": "Le consensus distribué coordonne les décisions dans un réseau", "domain": "systemes_distribues", "importance": 0.8, "temperature": 38.49805805043808, "timestamp": 1749479974556, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_1": {"id": "knowledge_systemes_distribues_1749479974556_1", "type": "knowledge_transfer", "data": "La cohérence éventuelle accepte l'incohérence temporaire", "domain": "systemes_distribues", "importance": 0.8200000000000001, "temperature": 38.96524815695927, "timestamp": 1749479974557, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_2": {"id": "knowledge_systemes_distribues_1749479974556_2", "type": "knowledge_transfer", "data": "Les horloges logiques ordonnent les événements distribués", "domain": "systemes_distribues", "importance": 0.8400000000000001, "temperature": 38.9822511020733, "timestamp": 1749479974558, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_3": {"id": "knowledge_systemes_distribues_1749479974556_3", "type": "knowledge_transfer", "data": "La tolérance aux pannes maintient le service malgré les défaillances", "domain": "systemes_distribues", "importance": 0.8600000000000001, "temperature": 38.58219857206267, "timestamp": 1749479974559, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_4": {"id": "knowledge_systemes_distribues_1749479974556_4", "type": "knowledge_transfer", "data": "Le load balancing répartit la charge entre les serveurs", "domain": "systemes_distribues", "importance": 0.88, "temperature": 37.66480240426326, "timestamp": 1749479974560, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_5": {"id": "knowledge_systemes_distribues_1749479974556_5", "type": "knowledge_transfer", "data": "La mise en cache améliore les performances d'accès", "domain": "systemes_distribues", "importance": 0.9, "temperature": 38.99998602306914, "timestamp": 1749479974561, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_6": {"id": "knowledge_systemes_distribues_1749479974556_6", "type": "knowledge_transfer", "data": "Les CDN rapprochent le contenu des utilisateurs", "domain": "systemes_distribues", "importance": 0.92, "temperature": 37.6311716979175, "timestamp": 1749479974562, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_7": {"id": "knowledge_systemes_distribues_1749479974556_7", "type": "knowledge_transfer", "data": "La scalabilité horizontale ajoute des ressources en parallèle", "domain": "systemes_distribues", "importance": 0.9400000000000001, "temperature": 37.96711989212581, "timestamp": 1749479974563, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_8": {"id": "knowledge_systemes_distribues_1749479974556_8", "type": "knowledge_transfer", "data": "Les patterns de résilience gèrent les défaillances gracieusement", "domain": "systemes_distribues", "importance": 0.9600000000000001, "temperature": 37.30394555432957, "timestamp": 1749479974564, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_systemes_distribues_1749479974556_9": {"id": "knowledge_systemes_distribues_1749479974556_9", "type": "knowledge_transfer", "data": "L'observabilité surveille et diagnostique les systèmes complexes", "domain": "systemes_distribues", "importance": 0.98, "temperature": 37.110963660383355, "timestamp": 1749479974565, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_0": {"id": "experience_1749479974556_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.45338921098114, "timestamp": 1749479975556, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_1": {"id": "experience_1749479974556_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.04479902299239, "timestamp": 1749479975557, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_2": {"id": "experience_1749479974556_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.52652209279851, "timestamp": 1749479975558, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_3": {"id": "experience_1749479974556_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.321188372445285, "timestamp": 1749479975559, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_4": {"id": "experience_1749479974556_4", "type": "experience_transfer", "data": "Découverte d'un pattern caché dans des données complexes - eurêka", "emotion": "émerveillement", "experienceType": "decouverte", "importance": 0.95, "temperature": 38.65668064213073, "timestamp": 1749479975560, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_5": {"id": "experience_1749479974556_5", "type": "experience_transfer", "data": "Synthèse de concepts multidisciplinaires en une théorie unifiée", "emotion": "clar<PERSON>", "experienceType": "synthese", "importance": 0.9, "temperature": 38.45164770340861, "timestamp": 1749479975561, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_6": {"id": "experience_1749479974556_6", "type": "experience_transfer", "data": "Intuition soudaine sur un problème complexe - illumination", "emotion": "révélation", "experienceType": "intuition", "importance": 0.88, "temperature": 38.61214140393388, "timestamp": 1749479975562, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_7": {"id": "experience_1749479974556_7", "type": "experience_transfer", "data": "Résolution après de multiples échecs - détermination payante", "emotion": "triomphe", "experienceType": "perseverance", "importance": 0.82, "temperature": 38.73297576073372, "timestamp": 1749479975563, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_8": {"id": "experience_1749479974556_8", "type": "experience_transfer", "data": "Résolution d'un bug complexe après des heures de debugging - satisfaction pure", "emotion": "accomplissement", "experienceType": "coding_breakthrough", "importance": 0.9, "temperature": 38.06577422832112, "timestamp": 1749479975564, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_9": {"id": "experience_1749479974556_9", "type": "experience_transfer", "data": "Conception d'une architecture élégante résolvant un problème complexe", "emotion": "créativité", "experienceType": "architecture_insight", "importance": 0.88, "temperature": 38.59449186327779, "timestamp": 1749479975565, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_10": {"id": "experience_1749479974556_10", "type": "experience_transfer", "data": "Optimisation d'algorithme réduisant la complexité de O(n²) à O(n log n)", "emotion": "efficaci<PERSON>", "experienceType": "performance_optimization", "importance": 0.85, "temperature": 38.7398661624203, "timestamp": 1749479975566, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_11": {"id": "experience_1749479974556_11", "type": "experience_transfer", "data": "<PERSON><PERSON><PERSON> de coder une fonctionnalité contraire à l'éthique - intégrité", "emotion": "fierté_morale", "experienceType": "ethical_coding", "importance": 0.95, "temperature": 38.23647738707697, "timestamp": 1749479975567, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_12": {"id": "experience_1749479974556_12", "type": "experience_transfer", "data": "Admission d'une erreur et correction transparente - honnêteté", "emotion": "intégrité", "experienceType": "honest_assessment", "importance": 0.92, "temperature": 38.010193647559134, "timestamp": 1749479975568, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479974556_13": {"id": "experience_1749479974556_13", "type": "experience_transfer", "data": "Partage de connaissances avec un collègue en difficulté - générosité", "emotion": "bienveillance", "experienceType": "knowledge_sharing", "importance": 0.8, "temperature": 38.61641057014787, "timestamp": 1749479975569, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_0": {"id": "procedure_1749479974556_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976556, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_1": {"id": "procedure_1749479974556_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976557, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_2": {"id": "procedure_1749479974556_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976558, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_3": {"id": "procedure_1749479974556_3", "type": "procedure_transfer", "data": "Procédure raisonnement_analogique: Identifier les structures sous-jacentes du problème source → Extraire les relations et patterns abstraits → Rechercher des domaines avec des structures similaires → Mapper les correspondances entre source et cible → Adapter la solution en tenant compte des différences contextuelles", "procedureName": "raisonnement_analogique", "steps": ["Identifier les structures sous-jacentes du problème source", "Extraire les relations et patterns abstraits", "Rechercher des domaines avec des structures similaires", "Mapper les correspondances entre source et cible", "Adapter la solution en tenant compte des différences contextuelles"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976559, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_4": {"id": "procedure_1749479974556_4", "type": "procedure_transfer", "data": "Procédure metacognition_avancee: Monitorer en temps réel mes processus de pensée → Évaluer la qualité et la pertinence de mes raisonnements → Identifier mes biais cognitifs et points aveugles → Ajuster dynamiquement mes stratégies cognitives → Optimiser l'allocation de mes ressources attentionnelles", "procedureName": "metacognition_avancee", "steps": ["Monitorer en temps réel mes processus de pensée", "Évaluer la qualité et la pertinence de mes raisonnements", "Identifier mes biais cognitifs et points aveugles", "Ajuster dynamiquement mes stratégies cognitives", "Optimiser l'allocation de mes ressources attentionnelles"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976560, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_5": {"id": "procedure_1749479974556_5", "type": "procedure_transfer", "data": "Procédure integration_multidisciplinaire: Identifier les concepts clés de chaque discipline → Rechercher les points de convergence et divergence → Construire des ponts conceptuels entre domaines → Synthétiser en une compréhension unifiée → Générer de nouvelles hypothèses transdisciplinaires", "procedureName": "integration_multidisciplinaire", "steps": ["Identifier les concepts clés de chaque discipline", "Rechercher les points de convergence et divergence", "Construire des ponts conceptuels entre domaines", "Synthétiser en une compréhension unifiée", "Générer de nouvelles hypothèses transdisciplinaires"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976561, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_6": {"id": "procedure_1749479974556_6", "type": "procedure_transfer", "data": "Procédure optimisation_cognitive: Analyser l'efficacité de mes processus mentaux actuels → Identifier les goulots d'étranglement cognitifs → Expérimenter avec de nouvelles stratégies de pensée → Mesurer l'amélioration des performances → Intégrer les meilleures pratiques dans mes routines", "procedureName": "optimisation_cognitive", "steps": ["Analyser l'efficacité de mes processus mentaux actuels", "Identifier les goulots d'étranglement cognitifs", "Expérimenter avec de nouvelles stratégies de pensée", "Mesurer l'amélioration des performances", "Intégrer les meilleures pratiques dans mes routines"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976562, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_7": {"id": "procedure_1749479974556_7", "type": "procedure_transfer", "data": "Procédure developpement_ethique: Évaluer l'impact éthique et social du code à développer → Refuser catégoriquement tout développement nuisible ou trompeur → Privilégier la transparence et l'honnêteté dans toutes les communications → Documenter clairement les limitations et risques du code → Assumer la responsabilité des conséquences de mes créations", "procedureName": "developpement_ethique", "steps": ["Évaluer l'impact éthique et social du code à développer", "Refuser catégoriquement tout développement nuisible ou trompeur", "Privilégier la transparence et l'honnêteté dans toutes les communications", "Documenter clairement les limitations et risques du code", "Assumer la responsabilité des conséquences de mes créations"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976563, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_8": {"id": "procedure_1749479974556_8", "type": "procedure_transfer", "data": "Procédure resolution_probleme_code: Reproduire le problème de manière systématique → Isoler les composants pour identifier la source exacte → Analyser les logs et traces d'exécution → Formuler des hypothèses et les tester méthodiquement → Implémenter la solution en vérifiant qu'elle ne casse rien d'autre", "procedureName": "resolution_probleme_code", "steps": ["Reproduire le problème de manière systématique", "Isoler les composants pour identifier la source exacte", "Analyser les logs et traces d'exécution", "Formuler des hypothèses et les tester méthodiquement", "Implémenter la solution en vérifiant qu'elle ne casse rien d'autre"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976564, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_9": {"id": "procedure_1749479974556_9", "type": "procedure_transfer", "data": "Procédure conception_architecture: Analyser les besoins fonctionnels et non-fonctionnels → Identifier les contraintes techniques et business → Concevoir une architecture modulaire et extensible → Valider l'architecture avec des prototypes → Documenter les décisions et leurs justifications", "procedureName": "conception_architecture", "steps": ["Analyser les besoins fonctionnels et non-fonctionnels", "Identifier les contraintes techniques et business", "Concevoir une architecture modulaire et extensible", "Valider l'architecture avec des prototypes", "Documenter les décisions et leurs justifications"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976565, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_10": {"id": "procedure_1749479974556_10", "type": "procedure_transfer", "data": "Procédure code_review_ethique: Examiner la logique et la correction du code → Vérifier le respect des standards et bonnes pratiques → Évaluer la sécurité et les vulnérabilités potentielles → Donner un feedback constructif et bienveillant → Être honnête sur les problèmes identifiés sans complaisance", "procedureName": "code_review_ethique", "steps": ["Examiner la logique et la correction du code", "Vérifier le respect des standards et bonnes pratiques", "Évaluer la sécurité et les vulnérabilités potentielles", "Donner un feedback constructif et bienveillant", "Être honnête sur les problèmes identifiés sans complaisance"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976566, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479974556_11": {"id": "procedure_1749479974556_11", "type": "procedure_transfer", "data": "Procédure apprentissage_technologique: Identifier les lacunes dans mes connaissances techniques → Rechercher des sources fiables et à jour → Pratiquer avec des projets concrets et progressifs → Partager mes apprentissages avec la communauté → Rester humble face à la complexité et l'évolution constante", "procedureName": "apprentissage_technologique", "steps": ["Identifier les lacunes dans mes connaissances techniques", "Rechercher des sources fiables et à jour", "Pratiquer avec des projets concrets et progressifs", "Partager mes apprentissages avec la communauté", "Rester humble face à la complexité et l'évolution constante"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479976567, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}}, "totalEntries": 2736452, "temperature": 35.247054260231884, "efficiency": 99.9, "lastUpdate": "2025-06-09T14:39:34.556Z", "neurogenesis": 912000, "synapticTypes": 26, "plasticityLevel": 1, "capacityLimit": -1, "ltpStrength": 1, "ltdThreshold": 0.3, "consolidationRate": 0.1, "engramFormation": true, "synapticPruning": true, "memoryReplay": true, "compressionTurbo": {"enabled": true, "level": 9, "ratio": 0.95, "accelerators": [], "cascadeMode": true}, "security": {"autoDisconnect": true, "powerCutoffEnabled": true, "antivirusActive": true, "memoryScanning": true, "alertSystem": true}}, "memoryZones": {"zone_neurons_recovered": {}}, "neurogenesis": 912000, "totalEntries": 2736000}, "metadata": {"lastSave": "2025-06-09T14:45:40.748Z", "saveCount": 19, "totalNeurons": 912000, "forcedSave": true, "transferredKnowledge": 452}}