#!/usr/bin/env node

/**
 * Script de test automatique pour le cerveau 3D de LOUNA AI
 * Teste toutes les fonctionnalités du cerveau 3D intégré
 */

const puppeteer = require('puppeteer');

async function testCerveau3D() {
    console.log('🧠 === TEST AUTOMATIQUE CERVEAU 3D LOUNA AI ===');
    
    let browser;
    try {
        // Lancer le navigateur
        browser = await puppeteer.launch({ 
            headless: false, // Mode visible pour voir les tests
            devtools: true 
        });
        
        const page = await browser.newPage();
        
        // Aller à l'application LOUNA AI
        console.log('🌐 Connexion à LOUNA AI...');
        await page.goto('http://localhost:52796', { waitUntil: 'networkidle2' });
        
        // Attendre que la page soit chargée
        await page.waitForTimeout(3000);
        
        // Test 1: Vérifier que l'interface est chargée
        console.log('✅ Test 1: Interface chargée');
        const title = await page.title();
        console.log(`   Titre: ${title}`);
        
        // Test 2: Aller à la section cerveau
        console.log('🧠 Test 2: Navigation vers section cerveau...');
        await page.click('a[data-section="brain"]');
        await page.waitForTimeout(2000);
        
        // Test 3: Vérifier que le canvas cerveau 3D existe
        console.log('🎨 Test 3: Vérification du canvas cerveau 3D...');
        const canvas = await page.$('#brain-3d-canvas');
        if (canvas) {
            console.log('   ✅ Canvas cerveau 3D trouvé');
        } else {
            console.log('   ❌ Canvas cerveau 3D non trouvé');
        }
        
        // Test 4: Exécuter le diagnostic
        console.log('🔍 Test 4: Exécution du diagnostic...');
        await page.evaluate(() => {
            if (window.diagnosticCerveau3D) {
                window.diagnosticCerveau3D();
            }
        });
        
        // Test 5: Forcer l'initialisation du cerveau 3D
        console.log('🚀 Test 5: Initialisation forcée du cerveau 3D...');
        await page.evaluate(() => {
            if (window.testForceCerveau3D) {
                window.testForceCerveau3D();
            }
        });
        
        await page.waitForTimeout(3000);
        
        // Test 6: Vérifier l'état du cerveau 3D
        console.log('🧠 Test 6: Vérification de l\'état du cerveau 3D...');
        const brain3DState = await page.evaluate(() => {
            return {
                active: window.brain3DActive || false,
                neurons: window.brain3DNeurons ? window.brain3DNeurons.length : 0,
                threeJS: typeof THREE !== 'undefined'
            };
        });
        
        console.log(`   État: ${brain3DState.active ? 'ACTIF' : 'INACTIF'}`);
        console.log(`   Neurones: ${brain3DState.neurons}`);
        console.log(`   Three.js: ${brain3DState.threeJS ? 'DISPONIBLE' : 'NON DISPONIBLE'}`);
        
        // Test 7: Tester la tempête neuronale
        console.log('⚡ Test 7: Test de la tempête neuronale...');
        await page.evaluate(() => {
            if (window.activateSimpleNeuralStorm) {
                window.activateSimpleNeuralStorm();
            }
        });
        
        await page.waitForTimeout(2000);
        
        // Test 8: Vérifier les métriques
        console.log('📊 Test 8: Vérification des métriques...');
        const metrics = await page.evaluate(() => {
            const neurons = document.getElementById('brain-3d-neurons');
            const synapses = document.getElementById('brain-3d-synapses');
            const activity = document.getElementById('brain-3d-activity');
            
            return {
                neurons: neurons ? neurons.textContent : 'N/A',
                synapses: synapses ? synapses.textContent : 'N/A',
                activity: activity ? activity.textContent : 'N/A'
            };
        });
        
        console.log(`   Neurones: ${metrics.neurons}`);
        console.log(`   Synapses: ${metrics.synapses}`);
        console.log(`   Activité: ${metrics.activity}`);
        
        // Test 9: Capturer une capture d'écran
        console.log('📸 Test 9: Capture d\'écran...');
        await page.screenshot({ 
            path: 'cerveau-3d-test.png', 
            fullPage: true 
        });
        console.log('   ✅ Capture sauvegardée: cerveau-3d-test.png');
        
        // Test 10: Vérifier les logs de la console
        console.log('📝 Test 10: Vérification des logs...');
        const logs = await page.evaluate(() => {
            return window.console._logs || [];
        });
        
        console.log('🎉 === TESTS TERMINÉS ===');
        console.log(`✅ Tous les tests exécutés avec succès!`);
        
        // Garder le navigateur ouvert pour inspection manuelle
        console.log('🔍 Navigateur laissé ouvert pour inspection manuelle...');
        console.log('   Fermez manuellement le navigateur quand vous avez terminé.');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests:', error);
        if (browser) {
            await browser.close();
        }
    }
}

// Exécuter les tests si le script est lancé directement
if (require.main === module) {
    testCerveau3D().catch(console.error);
}

module.exports = { testCerveau3D };
