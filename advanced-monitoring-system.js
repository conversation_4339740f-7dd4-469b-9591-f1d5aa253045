/**
 * 🚀 SYSTÈME DE MONITORING AVANCÉ POUR LOUNA AI
 * Surveillance en temps réel des performances, sécurité et optimisations automatiques
 */

const os = require('os');
const fs = require('fs').promises;
const path = require('path');

class AdvancedMonitoringSystem {
    constructor() {
        this.isActive = false;
        this.metrics = {
            system: {
                cpu: { usage: 0, temperature: 37, cores: os.cpus().length },
                memory: { used: 0, total: os.totalmem(), efficiency: 100 },
                disk: { usage: 0, speed: 100, available: 0 },
                network: { latency: 0, throughput: 100, connections: 0 }
            },
            ai: {
                neurons: 0,
                qi: { agent: 100, memory: 58, combined: 158 },
                responses: { total: 0, quality: 95, speed: 100 },
                learning: { sessions: 0, improvement: 0, accuracy: 95 }
            },
            security: {
                threats: 0,
                scans: 0,
                status: 'secure',
                lastScan: Date.now()
            },
            performance: {
                uptime: 0,
                efficiency: 99.9,
                optimization: 100,
                stability: 100
            }
        };
        
        this.alerts = [];
        this.optimizations = [];
        this.history = [];
        
        this.thresholds = {
            cpu: { warning: 70, critical: 90 },
            memory: { warning: 80, critical: 95 },
            temperature: { warning: 45, critical: 55 },
            efficiency: { warning: 85, critical: 70 }
        };
        
        this.startTime = Date.now();
        this.lastOptimization = Date.now();
        
        console.log('🚀 Système de monitoring avancé initialisé');
    }

    /**
     * Démarrer le monitoring en temps réel
     */
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        console.log('📊 Démarrage du monitoring avancé...');
        
        // Monitoring principal toutes les 2 secondes
        this.mainInterval = setInterval(() => {
            this.collectMetrics();
            this.analyzePerformance();
            this.detectAnomalies();
        }, 2000);
        
        // Optimisations automatiques toutes les 30 secondes
        this.optimizationInterval = setInterval(() => {
            this.performOptimizations();
        }, 30000);
        
        // Sauvegarde des métriques toutes les 5 minutes
        this.saveInterval = setInterval(() => {
            this.saveMetricsHistory();
        }, 300000);
        
        console.log('✅ Monitoring avancé démarré avec succès');
    }

    /**
     * Arrêter le monitoring
     */
    stop() {
        if (!this.isActive) return;
        
        this.isActive = false;
        
        if (this.mainInterval) clearInterval(this.mainInterval);
        if (this.optimizationInterval) clearInterval(this.optimizationInterval);
        if (this.saveInterval) clearInterval(this.saveInterval);
        
        console.log('⏹️ Monitoring avancé arrêté');
    }

    /**
     * Collecter les métriques système en temps réel
     */
    async collectMetrics() {
        try {
            // Métriques système
            const memUsage = process.memoryUsage();
            const cpuUsage = await this.getCPUUsage();
            
            this.metrics.system.cpu.usage = cpuUsage;
            this.metrics.system.memory.used = memUsage.heapUsed;
            this.metrics.system.memory.efficiency = Math.max(0, 100 - (memUsage.heapUsed / memUsage.heapTotal * 100));
            
            // Métriques de performance
            this.metrics.performance.uptime = (Date.now() - this.startTime) / 1000;
            this.metrics.performance.efficiency = this.calculateEfficiency();
            
            // Mise à jour de l'historique
            this.updateHistory();
            
        } catch (error) {
            console.error('❌ Erreur lors de la collecte des métriques:', error);
        }
    }

    /**
     * Calculer l'usage CPU
     */
    async getCPUUsage() {
        return new Promise((resolve) => {
            const startUsage = process.cpuUsage();
            setTimeout(() => {
                const endUsage = process.cpuUsage(startUsage);
                const totalUsage = endUsage.user + endUsage.system;
                const percentage = (totalUsage / 1000000) * 100; // Convertir en pourcentage
                resolve(Math.min(100, Math.max(0, percentage)));
            }, 100);
        });
    }

    /**
     * Calculer l'efficacité globale
     */
    calculateEfficiency() {
        const cpuEff = Math.max(0, 100 - this.metrics.system.cpu.usage);
        const memEff = this.metrics.system.memory.efficiency;
        const tempEff = Math.max(0, 100 - ((this.metrics.system.cpu.temperature - 30) * 2));
        
        return Math.round((cpuEff + memEff + tempEff) / 3 * 10) / 10;
    }

    /**
     * Analyser les performances et générer des recommandations
     */
    analyzePerformance() {
        const analysis = {
            timestamp: Date.now(),
            score: this.metrics.performance.efficiency,
            recommendations: [],
            warnings: []
        };

        // Analyse CPU
        if (this.metrics.system.cpu.usage > this.thresholds.cpu.critical) {
            analysis.warnings.push({
                type: 'CPU_CRITICAL',
                message: `Usage CPU critique: ${this.metrics.system.cpu.usage}%`,
                priority: 'HIGH'
            });
            analysis.recommendations.push('Réduire les processus en arrière-plan');
        }

        // Analyse mémoire
        const memoryUsagePercent = (this.metrics.system.memory.used / this.metrics.system.memory.total) * 100;
        if (memoryUsagePercent > this.thresholds.memory.warning) {
            analysis.warnings.push({
                type: 'MEMORY_HIGH',
                message: `Usage mémoire élevé: ${memoryUsagePercent.toFixed(1)}%`,
                priority: memoryUsagePercent > this.thresholds.memory.critical ? 'HIGH' : 'MEDIUM'
            });
            analysis.recommendations.push('Nettoyer la mémoire cache');
        }

        // Analyse température
        if (this.metrics.system.cpu.temperature > this.thresholds.temperature.warning) {
            analysis.warnings.push({
                type: 'TEMPERATURE_HIGH',
                message: `Température élevée: ${this.metrics.system.cpu.temperature}°C`,
                priority: this.metrics.system.cpu.temperature > this.thresholds.temperature.critical ? 'HIGH' : 'MEDIUM'
            });
            analysis.recommendations.push('Optimiser le refroidissement système');
        }

        // Stocker l'analyse
        this.alerts = this.alerts.concat(analysis.warnings).slice(-50); // Garder les 50 dernières alertes
        
        return analysis;
    }

    /**
     * Détecter les anomalies dans le système
     */
    detectAnomalies() {
        const anomalies = [];
        
        // Détection de pics de performance
        if (this.history.length > 10) {
            const recent = this.history.slice(-10);
            const avgCPU = recent.reduce((sum, h) => sum + h.cpu, 0) / recent.length;
            const currentCPU = this.metrics.system.cpu.usage;
            
            if (currentCPU > avgCPU * 2) {
                anomalies.push({
                    type: 'CPU_SPIKE',
                    severity: 'MEDIUM',
                    message: `Pic CPU détecté: ${currentCPU}% (moyenne: ${avgCPU.toFixed(1)}%)`
                });
            }
        }

        // Détection de fuites mémoire
        if (this.history.length > 20) {
            const memoryTrend = this.calculateMemoryTrend();
            if (memoryTrend > 5) { // Augmentation de plus de 5% par minute
                anomalies.push({
                    type: 'MEMORY_LEAK',
                    severity: 'HIGH',
                    message: `Possible fuite mémoire détectée (tendance: +${memoryTrend.toFixed(1)}%/min)`
                });
            }
        }

        return anomalies;
    }

    /**
     * Calculer la tendance d'usage mémoire
     */
    calculateMemoryTrend() {
        if (this.history.length < 10) return 0;
        
        const recent = this.history.slice(-10);
        const first = recent[0].memory;
        const last = recent[recent.length - 1].memory;
        const timeSpan = (recent[recent.length - 1].timestamp - recent[0].timestamp) / 60000; // en minutes
        
        return ((last - first) / first * 100) / timeSpan;
    }

    /**
     * Effectuer des optimisations automatiques
     */
    async performOptimizations() {
        const optimizations = [];
        
        try {
            // Optimisation mémoire
            if (this.metrics.system.memory.efficiency < this.thresholds.efficiency.warning) {
                if (global.gc) {
                    global.gc();
                    optimizations.push({
                        type: 'MEMORY_CLEANUP',
                        message: 'Nettoyage mémoire effectué',
                        impact: 'Libération de mémoire'
                    });
                }
            }

            // Optimisation cache
            if (this.metrics.performance.efficiency < this.thresholds.efficiency.critical) {
                await this.optimizeCache();
                optimizations.push({
                    type: 'CACHE_OPTIMIZATION',
                    message: 'Cache optimisé',
                    impact: 'Amélioration des performances'
                });
            }

            // Optimisation des connexions
            await this.optimizeConnections();
            optimizations.push({
                type: 'CONNECTION_OPTIMIZATION',
                message: 'Connexions optimisées',
                impact: 'Réduction de la latence'
            });

            this.optimizations = this.optimizations.concat(optimizations).slice(-100);
            this.lastOptimization = Date.now();
            
            if (optimizations.length > 0) {
                console.log(`🔧 ${optimizations.length} optimisation(s) effectuée(s)`);
            }
            
        } catch (error) {
            console.error('❌ Erreur lors des optimisations:', error);
        }
    }

    /**
     * Optimiser le cache système
     */
    async optimizeCache() {
        // Simulation d'optimisation cache
        return new Promise(resolve => {
            setTimeout(() => {
                this.metrics.performance.optimization = Math.min(100, this.metrics.performance.optimization + 1);
                resolve();
            }, 100);
        });
    }

    /**
     * Optimiser les connexions réseau
     */
    async optimizeConnections() {
        // Simulation d'optimisation réseau
        this.metrics.network = {
            latency: Math.max(1, this.metrics.network?.latency - 1 || 10),
            throughput: Math.min(100, (this.metrics.network?.throughput || 90) + 1),
            connections: this.metrics.network?.connections || 0
        };
    }

    /**
     * Mettre à jour l'historique des métriques
     */
    updateHistory() {
        const entry = {
            timestamp: Date.now(),
            cpu: this.metrics.system.cpu.usage,
            memory: (this.metrics.system.memory.used / this.metrics.system.memory.total) * 100,
            temperature: this.metrics.system.cpu.temperature,
            efficiency: this.metrics.performance.efficiency
        };
        
        this.history.push(entry);
        
        // Garder seulement les 1000 dernières entrées (environ 33 minutes)
        if (this.history.length > 1000) {
            this.history = this.history.slice(-1000);
        }
    }

    /**
     * Sauvegarder l'historique des métriques
     */
    async saveMetricsHistory() {
        try {
            const historyFile = path.join(__dirname, 'data', 'metrics-history.json');
            const data = {
                timestamp: Date.now(),
                metrics: this.metrics,
                history: this.history.slice(-100), // Sauvegarder les 100 dernières entrées
                alerts: this.alerts.slice(-20),
                optimizations: this.optimizations.slice(-20)
            };
            
            await fs.writeFile(historyFile, JSON.stringify(data, null, 2));
            console.log('💾 Historique des métriques sauvegardé');
            
        } catch (error) {
            console.error('❌ Erreur lors de la sauvegarde:', error);
        }
    }

    /**
     * Obtenir un rapport complet du système
     */
    getSystemReport() {
        const uptime = (Date.now() - this.startTime) / 1000;
        const recentAlerts = this.alerts.slice(-10);
        const recentOptimizations = this.optimizations.slice(-5);
        
        return {
            timestamp: Date.now(),
            uptime: uptime,
            status: this.getSystemStatus(),
            metrics: this.metrics,
            performance: {
                score: this.metrics.performance.efficiency,
                grade: this.getPerformanceGrade(),
                trends: this.getPerformanceTrends()
            },
            alerts: recentAlerts,
            optimizations: recentOptimizations,
            recommendations: this.getRecommendations()
        };
    }

    /**
     * Obtenir le statut général du système
     */
    getSystemStatus() {
        const efficiency = this.metrics.performance.efficiency;
        const alertCount = this.alerts.filter(a => a.priority === 'HIGH').length;
        
        if (efficiency > 95 && alertCount === 0) return 'EXCELLENT';
        if (efficiency > 85 && alertCount < 2) return 'GOOD';
        if (efficiency > 70 && alertCount < 5) return 'FAIR';
        return 'NEEDS_ATTENTION';
    }

    /**
     * Obtenir la note de performance
     */
    getPerformanceGrade() {
        const score = this.metrics.performance.efficiency;
        if (score >= 95) return 'A+';
        if (score >= 90) return 'A';
        if (score >= 85) return 'B+';
        if (score >= 80) return 'B';
        if (score >= 75) return 'C+';
        if (score >= 70) return 'C';
        return 'D';
    }

    /**
     * Obtenir les tendances de performance
     */
    getPerformanceTrends() {
        if (this.history.length < 10) return { cpu: 'stable', memory: 'stable', efficiency: 'stable' };
        
        const recent = this.history.slice(-10);
        const older = this.history.slice(-20, -10);
        
        if (older.length === 0) return { cpu: 'stable', memory: 'stable', efficiency: 'stable' };
        
        const avgRecent = {
            cpu: recent.reduce((sum, h) => sum + h.cpu, 0) / recent.length,
            memory: recent.reduce((sum, h) => sum + h.memory, 0) / recent.length,
            efficiency: recent.reduce((sum, h) => sum + h.efficiency, 0) / recent.length
        };
        
        const avgOlder = {
            cpu: older.reduce((sum, h) => sum + h.cpu, 0) / older.length,
            memory: older.reduce((sum, h) => sum + h.memory, 0) / older.length,
            efficiency: older.reduce((sum, h) => sum + h.efficiency, 0) / older.length
        };
        
        return {
            cpu: this.getTrend(avgRecent.cpu, avgOlder.cpu),
            memory: this.getTrend(avgRecent.memory, avgOlder.memory),
            efficiency: this.getTrend(avgRecent.efficiency, avgOlder.efficiency, true)
        };
    }

    /**
     * Déterminer la tendance d'une métrique
     */
    getTrend(current, previous, higherIsBetter = false) {
        const diff = current - previous;
        const threshold = 2; // 2% de différence
        
        if (Math.abs(diff) < threshold) return 'stable';
        
        if (higherIsBetter) {
            return diff > 0 ? 'improving' : 'declining';
        } else {
            return diff > 0 ? 'increasing' : 'decreasing';
        }
    }

    /**
     * Obtenir des recommandations d'amélioration
     */
    getRecommendations() {
        const recommendations = [];
        
        if (this.metrics.performance.efficiency < 85) {
            recommendations.push({
                type: 'PERFORMANCE',
                priority: 'HIGH',
                message: 'Optimiser les performances système',
                actions: ['Nettoyer la mémoire', 'Optimiser les processus', 'Vérifier les ressources']
            });
        }
        
        if (this.alerts.length > 10) {
            recommendations.push({
                type: 'MONITORING',
                priority: 'MEDIUM',
                message: 'Réduire le nombre d\'alertes',
                actions: ['Ajuster les seuils', 'Résoudre les problèmes récurrents']
            });
        }
        
        return recommendations;
    }
}

module.exports = AdvancedMonitoringSystem;
