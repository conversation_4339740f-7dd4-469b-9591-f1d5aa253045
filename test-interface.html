<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEST LOUNA AI - Diagnostic Boutons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            min-height: 100px;
        }
        
        .success {
            color: #4ecdc4;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .chat-test {
            margin-top: 30px;
        }
        
        .chat-input {
            width: 70%;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .send-btn {
            background: #4ecdc4;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 TEST DIAGNOSTIC LOUNA AI</h1>
        <p>Cette page teste si les boutons et JavaScript fonctionnent correctement.</p>
        
        <div class="result" id="test-results">
            <strong>Résultats des tests :</strong><br>
            <span id="status">En attente...</span>
        </div>
        
        <h2>Tests de Boutons</h2>
        
        <button class="test-button" onclick="testBasicClick()">
            🔴 Test Clic Simple
        </button>
        
        <button class="test-button" onclick="testAlert()">
            🟡 Test Alert
        </button>
        
        <button class="test-button" onclick="testConsole()">
            🟢 Test Console
        </button>
        
        <button class="test-button" onclick="testAPI()">
            🔵 Test API
        </button>
        
        <button class="test-button" onclick="testNavigation()">
            🟣 Test Navigation
        </button>
        
        <div class="chat-test">
            <h2>Test Chat</h2>
            <input type="text" class="chat-input" id="test-input" placeholder="Tapez un message de test...">
            <button class="send-btn" onclick="testSendMessage()">Envoyer Test</button>
        </div>
        
        <div class="result" id="chat-results">
            <strong>Messages de test :</strong><br>
            <div id="messages"></div>
        </div>
    </div>

    <script>
        // Variables globales
        let testCount = 0;
        
        // Fonction pour afficher les résultats
        function addResult(message, type = 'success') {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : 'success';
            status.innerHTML += `<br><span class="${className}">[${timestamp}] ${message}</span>`;
            console.log(`[TEST] ${message}`);
        }
        
        // Test 1: Clic simple
        function testBasicClick() {
            testCount++;
            addResult(`✅ Test ${testCount}: Clic simple fonctionne !`);
        }
        
        // Test 2: Alert
        function testAlert() {
            testCount++;
            alert('🎉 Les alertes fonctionnent !');
            addResult(`✅ Test ${testCount}: Alert fonctionne !`);
        }
        
        // Test 3: Console
        function testConsole() {
            testCount++;
            console.log('🔍 Test console - Les logs fonctionnent !');
            addResult(`✅ Test ${testCount}: Console fonctionne !`);
        }
        
        // Test 4: API
        async function testAPI() {
            testCount++;
            try {
                addResult(`🔄 Test ${testCount}: Test API en cours...`, 'info');
                const response = await fetch('/api/metrics');
                const data = await response.json();
                addResult(`✅ Test ${testCount}: API fonctionne ! QI: ${data.qi || 'N/A'}`);
            } catch (error) {
                addResult(`❌ Test ${testCount}: Erreur API - ${error.message}`, 'error');
            }
        }
        
        // Test 5: Navigation
        function testNavigation() {
            testCount++;
            addResult(`✅ Test ${testCount}: Navigation testée !`);
            setTimeout(() => {
                if (confirm('Voulez-vous naviguer vers l\'interface réelle ?')) {
                    window.location.href = '/real';
                }
            }, 1000);
        }
        
        // Test Chat
        function testSendMessage() {
            const input = document.getElementById('test-input');
            const messages = document.getElementById('messages');
            const message = input.value.trim();
            
            if (!message) {
                addResult('❌ Message vide !', 'error');
                return;
            }
            
            // Ajouter le message à l'affichage
            const timestamp = new Date().toLocaleTimeString();
            messages.innerHTML += `<div style="margin: 5px 0; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                <strong>[${timestamp}] Vous:</strong> ${message}
            </div>`;
            
            input.value = '';
            addResult(`✅ Message envoyé: "${message}"`);
            
            // Test API chat
            testChatAPI(message);
        }
        
        // Test API Chat
        async function testChatAPI(message) {
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                const messages = document.getElementById('messages');
                const timestamp = new Date().toLocaleTimeString();
                
                if (data.success && data.response) {
                    messages.innerHTML += `<div style="margin: 5px 0; padding: 5px; background: rgba(78,205,196,0.2); border-radius: 5px;">
                        <strong>[${timestamp}] LOUNA AI:</strong> ${data.response}
                    </div>`;
                    addResult('✅ API Chat fonctionne !');
                } else {
                    addResult(`❌ Erreur API Chat: ${data.error || 'Erreur inconnue'}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur réseau Chat: ${error.message}`, 'error');
            }
        }
        
        // Test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 Page chargée - JavaScript fonctionne !');
            addResult('📱 DOMContentLoaded déclenché');
            
            // Test des éléments
            const buttons = document.querySelectorAll('.test-button');
            addResult(`🔘 ${buttons.length} boutons trouvés`);
            
            const input = document.getElementById('test-input');
            if (input) {
                addResult('✅ Input de test trouvé');
                
                // Test touche Entrée
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        addResult('⌨️ Touche Entrée détectée');
                        testSendMessage();
                    }
                });
            } else {
                addResult('❌ Input de test non trouvé', 'error');
            }
            
            // Test de connectivité
            setTimeout(testAPI, 2000);
        });
        
        // Test d'erreurs
        window.addEventListener('error', function(e) {
            addResult(`❌ Erreur JavaScript: ${e.message}`, 'error');
        });
        
        console.log('🧪 Script de test chargé');
    </script>
</body>
</html>
