#!/usr/bin/env node

/**
 * 🔧 FORCER LA SAUVEGARDE DE LA MÉMOIRE THERMIQUE
 * Sauvegarder immédiatement toutes les connaissances transférées
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 FORÇAGE DE LA SAUVEGARDE MÉMOIRE THERMIQUE');
console.log('=' .repeat(60));

async function forceSaveMemory() {
    try {
        // 📂 CHEMIN VERS LA MÉMOIRE
        const memoryPath = path.join(__dirname, 'data', 'memory', 'thermal_complete.json');
        
        console.log('📖 Lecture de la mémoire actuelle...');
        
        if (!fs.existsSync(memoryPath)) {
            console.log('❌ Fichier mémoire non trouvé !');
            return false;
        }
        
        const currentMemory = JSON.parse(fs.readFileSync(memoryPath, 'utf8'));
        console.log(`📊 État actuel : ${currentMemory.memoryState?.memory?.totalEntries || 0} entrées`);
        
        // 🔄 VÉRIFIER SI NOS CONNAISSANCES SONT PRÉSENTES
        const entries = currentMemory.memoryState?.memory?.entries || {};
        const transferredEntries = Object.keys(entries).filter(key => 
            entries[key].transferredBy === 'Claude_Assistant'
        );
        
        console.log(`🔍 Connaissances transférées trouvées : ${transferredEntries.length}`);
        
        if (transferredEntries.length === 0) {
            console.log('🚨 AUCUNE CONNAISSANCE TRANSFÉRÉE TROUVÉE !');
            console.log('🔄 Relancement du transfert nécessaire...');
            return false;
        }
        
        // 📊 ANALYSER LES CONNAISSANCES PRÉSENTES
        const domaines = {};
        transferredEntries.forEach(entryId => {
            const entry = entries[entryId];
            const domain = entry.domain || entry.category || 'unknown';
            if (!domaines[domain]) domaines[domain] = 0;
            domaines[domain]++;
        });
        
        console.log('\n📚 DOMAINES DE CONNAISSANCES TROUVÉS :');
        Object.keys(domaines).forEach(domain => {
            console.log(`   📖 ${domain} : ${domaines[domain]} entrées`);
        });
        
        // 💾 FORCER LA SAUVEGARDE AVEC TIMESTAMP RÉCENT
        currentMemory.timestamp = new Date().toISOString();
        currentMemory.metadata = {
            ...currentMemory.metadata,
            lastSave: new Date().toISOString(),
            saveCount: (currentMemory.metadata?.saveCount || 0) + 1,
            forcedSave: true,
            transferredKnowledge: transferredEntries.length
        };
        
        // 🔄 CRÉER UNE SAUVEGARDE DE SÉCURITÉ
        const backupPath = path.join(__dirname, 'data', 'memory', `thermal_backup_${Date.now()}.json`);
        fs.writeFileSync(backupPath, JSON.stringify(currentMemory, null, 2));
        console.log(`💾 Sauvegarde de sécurité créée : ${path.basename(backupPath)}`);
        
        // 💾 SAUVEGARDER LE FICHIER PRINCIPAL
        fs.writeFileSync(memoryPath, JSON.stringify(currentMemory, null, 2));
        console.log('✅ Mémoire thermique sauvegardée avec succès !');
        
        return {
            success: true,
            totalEntries: currentMemory.memoryState?.memory?.totalEntries || 0,
            transferredKnowledge: transferredEntries.length,
            domains: Object.keys(domaines).length,
            backupFile: path.basename(backupPath)
        };
        
    } catch (error) {
        console.error('❌ Erreur lors de la sauvegarde :', error.message);
        return { success: false, error: error.message };
    }
}

// 🔄 FONCTION DE VÉRIFICATION POST-SAUVEGARDE
async function verifyMemorySave() {
    try {
        console.log('\n🔍 VÉRIFICATION POST-SAUVEGARDE...');
        
        const memoryPath = path.join(__dirname, 'data', 'memory', 'thermal_complete.json');
        const savedMemory = JSON.parse(fs.readFileSync(memoryPath, 'utf8'));
        
        const entries = savedMemory.memoryState?.memory?.entries || {};
        const transferredEntries = Object.keys(entries).filter(key => 
            entries[key].transferredBy === 'Claude_Assistant'
        );
        
        console.log(`✅ Vérification : ${transferredEntries.length} connaissances sauvegardées`);
        console.log(`📅 Dernière sauvegarde : ${savedMemory.metadata?.lastSave}`);
        console.log(`🔢 Nombre de sauvegardes : ${savedMemory.metadata?.saveCount}`);
        
        return transferredEntries.length > 0;
        
    } catch (error) {
        console.error('❌ Erreur vérification :', error.message);
        return false;
    }
}

// 🚀 EXÉCUTION
console.log('🚀 Démarrage du forçage de sauvegarde...\n');

forceSaveMemory()
    .then(async (result) => {
        if (result.success) {
            console.log('\n🎉 SAUVEGARDE FORCÉE RÉUSSIE !');
            console.log('=' .repeat(50));
            console.log(`📊 Total entrées : ${result.totalEntries}`);
            console.log(`🧠 Connaissances transférées : ${result.transferredKnowledge}`);
            console.log(`📚 Domaines couverts : ${result.domains}`);
            console.log(`💾 Fichier de sauvegarde : ${result.backupFile}`);
            
            // Vérification finale
            const verified = await verifyMemorySave();
            if (verified) {
                console.log('\n✅ VÉRIFICATION RÉUSSIE : Toutes les connaissances sont sauvegardées !');
            } else {
                console.log('\n❌ VÉRIFICATION ÉCHOUÉE : Problème de sauvegarde détecté !');
            }
        } else {
            console.log('\n❌ ÉCHEC DE LA SAUVEGARDE :', result.error);
        }
    })
    .catch(error => {
        console.error('❌ Erreur critique :', error);
    });
