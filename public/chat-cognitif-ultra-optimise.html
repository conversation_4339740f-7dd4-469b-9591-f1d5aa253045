<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Chat Cognitif Ultra-Optimisé</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2d1b69 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            animation: backgroundPulse 15s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { filter: brightness(1) hue-rotate(0deg); }
            33% { filter: brightness(1.1) hue-rotate(10deg); }
            66% { filter: brightness(0.9) hue-rotate(-10deg); }
        }

        .chat-container {
            display: grid;
            grid-template-columns: 300px 1fr 280px;
            grid-template-rows: 80px 1fr 100px;
            height: 100vh;
            gap: 15px;
            padding: 15px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .header {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: headerShimmer 4s infinite;
        }

        @keyframes headerShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .logo {
            font-size: 28px;
            font-weight: 900;
            background: linear-gradient(45deg, #fff 0%, #a8e6cf 25%, #667eea 50%, #ff69b4 75%, #fff 100%);
            background-size: 300% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoFlow 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        @keyframes logoFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .status-panel {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            font-weight: 600;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4ade80;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        .left-panel {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .right-panel {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .chat-main {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .chat-main::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
            animation: chatShimmer 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes chatShimmer {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(100%) translateY(100%); }
        }

        .chat-messages {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .message {
            max-width: 80%;
            padding: 18px 24px;
            border-radius: 25px;
            font-size: 15px;
            line-height: 1.6;
            position: relative;
            animation: messageSlideIn 0.5s ease-out;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .message.ai {
            align-self: flex-start;
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.9) 0%, rgba(255, 20, 147, 0.8) 100%);
            color: white;
            border-bottom-left-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            border-radius: inherit;
            animation: messageGlow 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes messageGlow {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .input-container {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            display: flex;
            gap: 15px;
            align-items: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .chat-input {
            flex: 1;
            padding: 18px 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            outline: none;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .chat-input:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .send-button {
            padding: 18px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            backdrop-filter: blur(10px);
        }

        .send-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff69b4;
            text-align: center;
            text-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .stat-value {
            font-weight: 700;
            color: #4ade80;
            text-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.05);
            padding: 12px;
            border-radius: 12px;
            margin-bottom: 10px;
            border-left: 4px solid #ff69b4;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .memory-entry:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .memory-type {
            color: #ff69b4;
            font-weight: 600;
            font-size: 11px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff69b4;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .quality-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
            margin-left: 10px;
            text-shadow: none;
        }

        .quality-excellent { background: #4ade80; color: #000; }
        .quality-good { background: #fbbf24; color: #000; }
        .quality-average { background: #f97316; color: #fff; }
        .quality-poor { background: #ef4444; color: #fff; }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="header">
            <div class="logo">🧠 LOUNA AI - Chat Cognitif Ultra-Optimisé</div>
            <div class="status-panel">
                <div class="status-item">
                    <div class="status-dot" id="deepseek-status"></div>
                    <span>DeepSeek R1 8B</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" id="thermal-status"></div>
                    <span>Mémoire Thermique</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" id="cognitive-status"></div>
                    <span>Cognition Avancée</span>
                </div>
            </div>
        </div>

        <div class="left-panel">
            <div class="panel-title">📊 Statistiques IA</div>
            <div id="ai-stats">
                <div class="stat-item">
                    <span>QI Agent:</span>
                    <span class="stat-value" id="agent-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>QI Mémoire:</span>
                    <span class="stat-value" id="memory-iq">0</span>
                </div>
                <div class="stat-item">
                    <span>QI Combiné:</span>
                    <span class="stat-value" id="combined-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>Neurones:</span>
                    <span class="stat-value" id="neurons">240</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.0°C</span>
                </div>
                <div class="stat-item">
                    <span>Efficacité:</span>
                    <span class="stat-value" id="efficiency">95%</span>
                </div>
                <div class="stat-item">
                    <span>Réponses:</span>
                    <span class="stat-value" id="response-count">0</span>
                </div>
                <div class="stat-item">
                    <span>Qualité Moy:</span>
                    <span class="stat-value" id="avg-quality">0/100</span>
                </div>
            </div>
        </div>

        <div class="chat-main">
            <div class="chat-messages" id="chat-messages">
                <div class="message ai">
                    🧠 Bonjour ! Je suis LOUNA AI avec DeepSeek R1 8B ultra-optimisé. 
                    Mon système cognitif avancé analyse chaque interaction en temps réel, 
                    apprend continuellement et s'adapte à vos besoins. 
                    Ma mémoire thermique fonctionne comme un vrai cerveau vivant. 
                    Comment puis-je vous aider aujourd'hui ?
                    <span class="quality-indicator quality-excellent">Excellent 95/100</span>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="panel-title">🧠 Mémoire Récente</div>
            <div id="recent-memories">
                <!-- Les mémoires récentes seront affichées ici -->
            </div>
        </div>

        <div class="input-container">
            <input type="text" class="chat-input" id="chat-input" 
                   placeholder="Tapez votre message... (Entrée pour envoyer)">
            <button class="send-button" id="send-button">
                <i class="fas fa-paper-plane"></i> Envoyer
            </button>
        </div>
    </div>

    <script>
        // 🧠 CHAT COGNITIF ULTRA-OPTIMISÉ
        let isProcessing = false;
        let responseCount = 0;
        let qualityScores = [];
        
        // Éléments DOM
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        
        // 📡 FONCTION D'ENVOI DE MESSAGE ULTRA-OPTIMISÉE
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isProcessing) return;
            
            isProcessing = true;
            sendButton.disabled = true;
            sendButton.innerHTML = '<div class="loading"></div> Traitement...';
            
            // Afficher le message utilisateur avec animation
            addMessage(message, 'user');
            chatInput.value = '';
            
            try {
                // 🚀 APPEL API ULTRA-OPTIMISÉ
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        message,
                        useAdvancedMode: true,
                        enableCognition: true,
                        thermalContext: true
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Afficher la réponse avec indicateur de qualité
                    const qualityClass = getQualityClass(data.quality || 75);
                    const qualityText = getQualityText(data.quality || 75);
                    
                    addMessage(
                        data.response + 
                        `<span class="quality-indicator ${qualityClass}">${qualityText} ${data.quality || 75}/100</span>`,
                        'ai'
                    );
                    
                    // Mettre à jour les statistiques
                    responseCount++;
                    if (data.quality) qualityScores.push(data.quality);
                    updateStats();
                    updateMemories();
                    
                } else {
                    addMessage(`❌ Erreur: ${data.error}`, 'ai');
                }
                
            } catch (error) {
                console.error('Erreur chat:', error);
                addMessage(`❌ Erreur de communication: ${error.message}`, 'ai');
            }
            
            isProcessing = false;
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i> Envoyer';
        }
        
        // 💬 AJOUTER UN MESSAGE AVEC ANIMATION
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            
            // Animation d'apparition
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateY(20px) scale(0.95)';
            
            chatMessages.appendChild(messageDiv);
            
            // Déclencher l'animation
            setTimeout(() => {
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0) scale(1)';
            }, 10);
            
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎨 FONCTIONS DE QUALITÉ
        function getQualityClass(score) {
            if (score >= 80) return 'quality-excellent';
            if (score >= 60) return 'quality-good';
            if (score >= 40) return 'quality-average';
            return 'quality-poor';
        }
        
        function getQualityText(score) {
            if (score >= 80) return 'Excellent';
            if (score >= 60) return 'Bon';
            if (score >= 40) return 'Moyen';
            return 'Faible';
        }
        
        // 📊 METTRE À JOUR LES STATISTIQUES
        async function updateStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('agent-iq').textContent = data.stats.agentIQ || '100';
                    document.getElementById('memory-iq').textContent = data.stats.memoryIQ || '0';
                    document.getElementById('combined-iq').textContent = data.stats.combinedIQ || '100';
                    document.getElementById('neurons').textContent = data.stats.neurons || '240';
                    document.getElementById('temperature').textContent = `${data.stats.temperature || 37.0}°C`;
                    document.getElementById('efficiency').textContent = `${data.stats.efficiency || 95}%`;
                }
                
                // Mettre à jour les stats locales
                document.getElementById('response-count').textContent = responseCount;
                const avgQuality = qualityScores.length > 0 
                    ? Math.round(qualityScores.reduce((a, b) => a + b, 0) / qualityScores.length)
                    : 0;
                document.getElementById('avg-quality').textContent = `${avgQuality}/100`;
                
            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }
        
        // 🧠 METTRE À JOUR LES MÉMOIRES
        async function updateMemories() {
            try {
                const response = await fetch('/api/thermal-memory/recent');
                const data = await response.json();
                
                if (data.success && data.memories) {
                    const memoriesContainer = document.getElementById('recent-memories');
                    memoriesContainer.innerHTML = '';
                    
                    data.memories.slice(0, 8).forEach(memory => {
                        const memoryDiv = document.createElement('div');
                        memoryDiv.className = 'memory-entry';
                        memoryDiv.innerHTML = `
                            <div class="memory-type">${memory.type || 'Mémoire'}</div>
                            <div>${typeof memory.data === 'string' ? memory.data.substring(0, 100) : JSON.stringify(memory.data).substring(0, 100)}...</div>
                        `;
                        memoriesContainer.appendChild(memoryDiv);
                    });
                }
                
            } catch (error) {
                console.error('Erreur mise à jour mémoires:', error);
            }
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 🔄 INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Chat Cognitif Ultra-Optimisé chargé');
            updateStats();
            updateMemories();
            
            // Mettre à jour les stats toutes les 5 secondes
            setInterval(updateStats, 5000);
            setInterval(updateMemories, 10000);
        });
    </script>
</body>
</html>
