<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Cerveau 3D Spectaculaire</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a1a0a 100%);
            font-family: 'Courier New', monospace;
            color: #00ff88;
            overflow: hidden;
            height: 100vh;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .header h1 {
            font-size: 1.5rem;
            text-shadow: 0 0 10px #ff69b4;
            margin-bottom: 10px;
        }

        .metrics {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
        }

        .metric {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
            text-shadow: 0 0 5px currentColor;
        }

        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #9d4edd;
            box-shadow: 0 0 20px rgba(157, 78, 221, 0.5);
        }

        .control-btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 8px 15px;
            background: linear-gradient(45deg, #ff69b4, #9d4edd);
            border: none;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.7);
        }

        .brain-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
            max-width: 300px;
        }

        .activity-indicator {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 50%;
            border: 3px solid #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.7);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 999;
        }

        .loading-text {
            font-size: 1.5rem;
            margin-bottom: 20px;
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 10px #ff69b4; }
            to { text-shadow: 0 0 20px #ff69b4, 0 0 30px #ff69b4; }
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #9d4edd, #00ff88);
            border-radius: 2px;
            animation: loading 2s infinite;
        }

        @keyframes loading {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        #brain-canvas {
            width: 100%;
            height: 100%;
        }

        .zone-info {
            margin-bottom: 10px;
            padding: 5px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 5px;
            border-left: 3px solid #ff69b4;
        }

        .neuron-count {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            color: #ffd700;
            text-shadow: 0 0 10px #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 CERVEAU 3D SPECTACULAIRE</h1>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="neuron-count" style="color: #ff69b4;">0</div>
                    <div>Neurones</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="synapse-count" style="color: #9d4edd;">0</div>
                    <div>Synapses</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="activity-level" style="color: #00ff88;">0%</div>
                    <div>Activité</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="temperature" style="color: #ffd700;">37°C</div>
                    <div>Température</div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" onclick="toggleRotation()">🔄 Rotation</button>
            <button class="control-btn" onclick="activateZone('frontal')">🧠 Lobe Frontal</button>
            <button class="control-btn" onclick="activateZone('parietal')">🎯 Lobe Pariétal</button>
            <button class="control-btn" onclick="activateZone('temporal')">👂 Lobe Temporal</button>
            <button class="control-btn" onclick="activateZone('occipital')">👁️ Lobe Occipital</button>
            <button class="control-btn" onclick="neuralStorm()">⚡ Tempête Neuronale</button>
            <button class="control-btn" onclick="resetBrain()">🔄 Reset</button>
        </div>

        <div class="loading" id="loading">
            <div class="loading-text">🧠 Initialisation du Cerveau 3D...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>

        <div class="brain-container">
            <div id="brain-canvas"></div>
        </div>

        <div class="info-panel" id="info-panel">
            <h3 style="color: #00ff88; margin-bottom: 10px;">📊 Informations Neuronales</h3>
            <div class="zone-info">
                <strong>Zone Active:</strong> <span id="active-zone">Aucune</span>
            </div>
            <div class="zone-info">
                <strong>Neurones Actifs:</strong> <span id="active-neurons">0</span>
            </div>
            <div class="zone-info">
                <strong>Fréquence:</strong> <span id="frequency">0 Hz</span>
            </div>
            <div class="zone-info">
                <strong>Efficacité:</strong> <span id="efficiency">100%</span>
            </div>
        </div>

        <div class="activity-indicator">
            <div class="neuron-count" id="live-activity">🧠</div>
        </div>
    </div>

    <!-- Three.js pour la visualisation 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <script>
        let scene, camera, renderer, brain3D, neurons3D = [], synapses3D = [], animationId;
        let neuronGeometry, neuronMaterial, synapseGeometry, synapseMaterial;
        let brainContainer, controls;
        let isRotating = true, currentZone = null;
        let activityLevel = 0, neuronCount = 0, synapseCount = 0;
        let brainLobes = {};
        
        // Initialiser la visualisation 3D spectaculaire
        async function init3DBrain() {
            try {
                console.log('🧠 Début de l\'initialisation du cerveau 3D...');

                const canvas = document.getElementById('brain-canvas');
                if (!canvas) {
                    throw new Error('Canvas brain-canvas non trouvé');
                }

                console.log('✅ Canvas trouvé, dimensions:', canvas.offsetWidth, 'x', canvas.offsetHeight);

                // Configuration de la scène 3D
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x000000);
                scene.fog = new THREE.Fog(0x000000, 50, 200);
                console.log('✅ Scène 3D créée');

                // Caméra avec perspective dynamique
                const width = canvas.offsetWidth || 800;
                const height = canvas.offsetHeight || 600;
                camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
                camera.position.set(0, 0, 50);
                console.log('✅ Caméra créée');

                // Renderer avec antialiasing et effets
                renderer = new THREE.WebGLRenderer({
                    antialias: true,
                    alpha: true,
                    powerPreference: "high-performance"
                });
                renderer.setSize(width, height);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                // Compatibilité avec différentes versions de Three.js
                if (renderer.outputEncoding !== undefined) {
                    renderer.outputEncoding = THREE.sRGBEncoding;
                }
                if (renderer.toneMapping !== undefined) {
                    renderer.toneMapping = THREE.ACESFilmicToneMapping;
                    renderer.toneMappingExposure = 1.2;
                }

                console.log('✅ Renderer créé');

                // Ajouter le renderer au canvas
                canvas.appendChild(renderer.domElement);
                console.log('✅ Renderer ajouté au DOM');

                // Contrôles de caméra (avec fallback)
                try {
                    if (THREE.OrbitControls) {
                        controls = new THREE.OrbitControls(camera, renderer.domElement);
                        controls.enableDamping = true;
                        controls.dampingFactor = 0.05;
                        controls.autoRotate = true;
                        controls.autoRotateSpeed = 0.5;
                        controls.minDistance = 20;
                        controls.maxDistance = 100;
                        console.log('✅ Contrôles OrbitControls activés');
                    } else {
                        console.warn('⚠️ OrbitControls non disponible, contrôles désactivés');
                    }
                } catch (controlError) {
                    console.warn('⚠️ Erreur contrôles:', controlError.message);
                }

                // Éclairage spectaculaire
                setupLighting();
                console.log('✅ Éclairage configuré');

                // Créer le cerveau 3D
                await create3DBrain();
                console.log('✅ Structure cérébrale créée');

                // Créer les neurones 3D
                await create3DNeurons();
                console.log('✅ Neurones créés');

                // Créer les synapses 3D
                await create3DSynapses();
                console.log('✅ Synapses créées');

                // Masquer le loading
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }

                // Démarrer l'animation
                animate3D();
                console.log('✅ Animation démarrée');

                // Démarrer les mises à jour
                startMetricsUpdate();
                console.log('✅ Mises à jour démarrées');

                // Redimensionnement automatique
                window.addEventListener('resize', onWindowResize);

                console.log('🎉 Cerveau 3D spectaculaire initialisé avec succès !');

            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation du cerveau 3D:', error);

                // Afficher l'erreur à l'utilisateur
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.innerHTML = `
                        <div class="loading-text" style="color: #ff4444;">❌ Erreur d'initialisation</div>
                        <div style="color: #888; font-size: 0.9rem; margin-top: 10px;">${error.message}</div>
                        <button onclick="location.reload()" style="
                            margin-top: 15px;
                            padding: 10px 20px;
                            background: #ff69b4;
                            border: none;
                            border-radius: 5px;
                            color: white;
                            cursor: pointer;
                        ">🔄 Recharger</button>
                    `;
                }
            }
        }

        // Configuration de l'éclairage spectaculaire
        function setupLighting() {
            // Éclairage ambiant
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            // Lumière directionnelle principale
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // Lumières colorées pour l'ambiance
            const light1 = new THREE.PointLight(0xff69b4, 2, 100);
            light1.position.set(30, 30, 30);
            scene.add(light1);

            const light2 = new THREE.PointLight(0x9d4edd, 2, 100);
            light2.position.set(-30, -30, 30);
            scene.add(light2);

            const light3 = new THREE.PointLight(0x00ff88, 1.5, 80);
            light3.position.set(0, 50, -30);
            scene.add(light3);

            const light4 = new THREE.PointLight(0xffd700, 1.5, 80);
            light4.position.set(0, -50, 30);
            scene.add(light4);
        }

        // Créer la structure 3D du cerveau
        async function create3DBrain() {
            // Géométrie du cerveau principal
            const brainGeometry = new THREE.SphereGeometry(15, 64, 64);

            // Matériau du cerveau avec transparence et effets
            const brainMaterial = new THREE.MeshPhongMaterial({
                color: 0x2a2a2a,
                transparent: true,
                opacity: 0.2,
                wireframe: true,
                wireframeLinewidth: 2
            });

            brain3D = new THREE.Mesh(brainGeometry, brainMaterial);
            brain3D.castShadow = true;
            brain3D.receiveShadow = true;
            scene.add(brain3D);

            // Ajouter des lobes cérébraux
            await createBrainLobes();

            // Ajouter des particules d'énergie
            createEnergyParticles();
        }

        // Créer les lobes cérébraux avec animations
        async function createBrainLobes() {
            const lobes = [
                { name: 'frontal', position: [0, 8, 10], color: 0xff69b4, size: 7, function: 'Raisonnement' },
                { name: 'parietal', position: [0, 12, -3], color: 0x9d4edd, size: 6, function: 'Perception' },
                { name: 'temporal', position: [12, 0, 3], color: 0x00ff88, size: 5, function: 'Mémoire' },
                { name: 'occipital', position: [0, -8, -12], color: 0x00bfff, size: 5, function: 'Vision' },
                { name: 'cerebellum', position: [0, -15, -8], color: 0xffd700, size: 4, function: 'Coordination' }
            ];

            for (const lobe of lobes) {
                const lobeGeometry = new THREE.SphereGeometry(lobe.size, 32, 32);
                const lobeMaterial = new THREE.MeshPhongMaterial({
                    color: lobe.color,
                    transparent: true,
                    opacity: 0.7,
                    emissive: lobe.color,
                    emissiveIntensity: 0.3,
                    shininess: 100
                });

                const lobeMesh = new THREE.Mesh(lobeGeometry, lobeMaterial);
                lobeMesh.position.set(...lobe.position);
                lobeMesh.castShadow = true;
                lobeMesh.receiveShadow = true;

                lobeMesh.userData = {
                    name: lobe.name,
                    originalColor: lobe.color,
                    function: lobe.function,
                    active: false,
                    pulsePhase: Math.random() * Math.PI * 2
                };

                brainLobes[lobe.name] = lobeMesh;
                scene.add(lobeMesh);

                // Ajouter un halo autour du lobe
                const haloGeometry = new THREE.SphereGeometry(lobe.size + 1, 16, 16);
                const haloMaterial = new THREE.MeshBasicMaterial({
                    color: lobe.color,
                    transparent: true,
                    opacity: 0.1,
                    side: THREE.BackSide
                });

                const halo = new THREE.Mesh(haloGeometry, haloMaterial);
                halo.position.copy(lobeMesh.position);
                scene.add(halo);

                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // Créer des particules d'énergie
        function createEnergyParticles() {
            const particleCount = 100;
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);

            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;

                // Position aléatoire dans une sphère
                const radius = 20 + Math.random() * 10;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;

                positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
                positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
                positions[i3 + 2] = radius * Math.cos(phi);

                // Couleur aléatoire
                const colorChoices = [
                    [1, 0.41, 0.71], // Rose
                    [0.61, 0.31, 0.93], // Violet
                    [0, 1, 0.53], // Vert
                    [0, 0.75, 1], // Bleu
                    [1, 0.84, 0] // Or
                ];
                const color = colorChoices[Math.floor(Math.random() * colorChoices.length)];

                colors[i3] = color[0];
                colors[i3 + 1] = color[1];
                colors[i3 + 2] = color[2];
            }

            const particleGeometry = new THREE.BufferGeometry();
            particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const particleMaterial = new THREE.PointsMaterial({
                size: 2,
                vertexColors: true,
                transparent: true,
                opacity: 0.8,
                blending: THREE.AdditiveBlending
            });

            const particles = new THREE.Points(particleGeometry, particleMaterial);
            scene.add(particles);
        }

        // Créer les neurones 3D avec animations avancées
        async function create3DNeurons() {
            neuronGeometry = new THREE.SphereGeometry(0.4, 16, 16);

            const neuronCount = 300;

            for (let i = 0; i < neuronCount; i++) {
                // Matériau du neurone avec couleur dynamique
                const colors = [0xff69b4, 0x9d4edd, 0x00ff88, 0x00bfff, 0xffd700];
                const color = colors[Math.floor(Math.random() * colors.length)];

                neuronMaterial = new THREE.MeshPhongMaterial({
                    color: color,
                    emissive: color,
                    emissiveIntensity: 0.4,
                    transparent: true,
                    opacity: 0.9,
                    shininess: 100
                });

                const neuron = new THREE.Mesh(neuronGeometry, neuronMaterial);

                // Position aléatoire dans une sphère avec zones préférentielles
                const radius = 13 + Math.random() * 12;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;

                neuron.position.x = radius * Math.sin(phi) * Math.cos(theta);
                neuron.position.y = radius * Math.sin(phi) * Math.sin(theta);
                neuron.position.z = radius * Math.cos(phi);

                neuron.castShadow = true;
                neuron.receiveShadow = true;

                // Propriétés d'animation avancées
                neuron.userData = {
                    id: i,
                    originalPosition: neuron.position.clone(),
                    pulsePhase: Math.random() * Math.PI * 2,
                    pulseSpeed: 0.02 + Math.random() * 0.03,
                    active: false,
                    activationTime: 0,
                    originalColor: color,
                    connections: [],
                    zone: determineNeuronZone(neuron.position),
                    fireRate: Math.random() * 0.1,
                    lastFire: 0
                };

                neurons3D.push(neuron);
                scene.add(neuron);

                // Ajouter un petit délai pour l'animation de création
                if (i % 20 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 50));
                    updateNeuronCount();
                }
            }

            neuronCount = neurons3D.length;
            updateNeuronCount();
        }

        // Déterminer la zone d'un neurone
        function determineNeuronZone(position) {
            if (position.y > 5 && position.z > 5) return 'frontal';
            if (position.y > 8) return 'parietal';
            if (Math.abs(position.x) > 8) return 'temporal';
            if (position.y < -5 && position.z < -5) return 'occipital';
            if (position.y < -10) return 'cerebellum';
            return 'central';
        }

        // Créer les synapses 3D avec animations de flux
        async function create3DSynapses() {
            for (let i = 0; i < neurons3D.length; i++) {
                const neuron1 = neurons3D[i];

                // Créer 3-6 connexions par neurone
                const connectionCount = 3 + Math.floor(Math.random() * 4);

                for (let j = 0; j < connectionCount; j++) {
                    const neuron2 = neurons3D[Math.floor(Math.random() * neurons3D.length)];

                    if (neuron1 !== neuron2 && neuron1.position.distanceTo(neuron2.position) < 15) {
                        createSynapse3D(neuron1, neuron2);
                        neuron1.userData.connections.push(neuron2);
                    }
                }

                if (i % 50 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 30));
                    updateSynapseCount();
                }
            }

            synapseCount = synapses3D.length;
            updateSynapseCount();
        }

        // Créer une synapse 3D avec effets visuels
        function createSynapse3D(neuron1, neuron2) {
            const points = [neuron1.position, neuron2.position];
            const geometry = new THREE.BufferGeometry().setFromPoints(points);

            const material = new THREE.LineBasicMaterial({
                color: 0x444444,
                transparent: true,
                opacity: 0.4,
                linewidth: 2
            });

            const synapse = new THREE.Line(geometry, material);
            synapse.userData = {
                neuron1: neuron1,
                neuron2: neuron2,
                active: false,
                activationTime: 0,
                pulsePosition: 0,
                strength: Math.random() * 0.5 + 0.5,
                lastActivity: 0
            };

            synapses3D.push(synapse);
            scene.add(synapse);
        }

        // Animation principale 3D
        function animate3D() {
            animationId = requestAnimationFrame(animate3D);

            const time = Date.now() * 0.001;

            // Rotation automatique du cerveau
            if (isRotating && brain3D) {
                brain3D.rotation.y += 0.005;
                brain3D.rotation.x += 0.002;
            }

            // Animation des lobes cérébraux
            Object.values(brainLobes).forEach(lobe => {
                if (lobe.userData.active) {
                    lobe.userData.pulsePhase += 0.1;
                    const pulse = Math.sin(lobe.userData.pulsePhase) * 0.3 + 1;
                    lobe.scale.setScalar(pulse);
                    lobe.material.emissiveIntensity = 0.3 + Math.sin(lobe.userData.pulsePhase) * 0.2;
                } else {
                    lobe.scale.setScalar(1);
                    lobe.material.emissiveIntensity = 0.3;
                }
            });

            // Animation des neurones
            neurons3D.forEach((neuron, index) => {
                const userData = neuron.userData;

                // Pulsation naturelle
                userData.pulsePhase += userData.pulseSpeed;
                const pulse = Math.sin(userData.pulsePhase) * 0.2 + 1;
                neuron.scale.setScalar(pulse);

                // Activation aléatoire
                if (Math.random() < userData.fireRate && time - userData.lastFire > 1) {
                    activateNeuron(neuron, 1000);
                    userData.lastFire = time;
                }

                // Animation d'activation
                if (userData.active) {
                    userData.activationTime -= 16;
                    const intensity = userData.activationTime / 1000;
                    neuron.material.emissiveIntensity = 0.4 + intensity * 0.6;

                    if (userData.activationTime <= 0) {
                        userData.active = false;
                        neuron.material.emissiveIntensity = 0.4;
                    }
                }

                // Mouvement subtil
                neuron.position.x = userData.originalPosition.x + Math.sin(time + index) * 0.1;
                neuron.position.y = userData.originalPosition.y + Math.cos(time + index) * 0.1;
                neuron.position.z = userData.originalPosition.z + Math.sin(time * 0.5 + index) * 0.1;
            });

            // Animation des synapses
            synapses3D.forEach(synapse => {
                const userData = synapse.userData;

                if (userData.active) {
                    userData.activationTime -= 16;
                    userData.pulsePosition += 0.05;

                    // Effet de flux le long de la synapse
                    const intensity = userData.activationTime / 1000;
                    synapse.material.opacity = 0.4 + intensity * 0.6;

                    if (userData.activationTime <= 0) {
                        userData.active = false;
                        synapse.material.opacity = 0.4;
                        synapse.material.color.setHex(0x444444);
                    }
                }
            });

            // Mise à jour des contrôles
            controls.update();

            // Rendu de la scène
            renderer.render(scene, camera);

            // Mise à jour des métriques
            updateActivityLevel();
        }

        // Activer un neurone avec propagation
        function activateNeuron(neuron, duration = 1000) {
            neuron.userData.active = true;
            neuron.userData.activationTime = duration;

            // Changer la couleur temporairement
            neuron.material.color.setHex(0xffffff);
            neuron.material.emissiveIntensity = 1.0;

            setTimeout(() => {
                neuron.material.color.setHex(neuron.userData.originalColor);
            }, duration);

            // Propager l'activation aux connexions
            neuron.userData.connections.forEach(connectedNeuron => {
                if (Math.random() < 0.3) { // 30% de chance de propagation
                    setTimeout(() => {
                        activateNeuron(connectedNeuron, duration * 0.7);
                    }, 100 + Math.random() * 200);
                }
            });

            // Activer les synapses connectées
            synapses3D.forEach(synapse => {
                if (synapse.userData.neuron1 === neuron || synapse.userData.neuron2 === neuron) {
                    synapse.userData.active = true;
                    synapse.userData.activationTime = duration;
                    synapse.material.color.setHex(neuron.userData.originalColor);
                }
            });
        }

        // Fonctions de contrôle
        function toggleRotation() {
            isRotating = !isRotating;
            controls.autoRotate = isRotating;
        }

        function activateZone(zoneName) {
            currentZone = zoneName;
            document.getElementById('active-zone').textContent = zoneName;

            // Activer le lobe correspondant
            if (brainLobes[zoneName]) {
                brainLobes[zoneName].userData.active = true;

                // Désactiver les autres lobes
                Object.values(brainLobes).forEach(lobe => {
                    if (lobe.userData.name !== zoneName) {
                        lobe.userData.active = false;
                    }
                });
            }

            // Activer les neurones de cette zone
            neurons3D.forEach(neuron => {
                if (neuron.userData.zone === zoneName) {
                    setTimeout(() => {
                        activateNeuron(neuron, 2000);
                    }, Math.random() * 1000);
                }
            });
        }

        function neuralStorm() {
            // Tempête neuronale spectaculaire
            let activationCount = 0;
            const maxActivations = 50;

            const stormInterval = setInterval(() => {
                if (activationCount >= maxActivations) {
                    clearInterval(stormInterval);
                    return;
                }

                // Activer des neurones aléatoires
                for (let i = 0; i < 5; i++) {
                    const randomNeuron = neurons3D[Math.floor(Math.random() * neurons3D.length)];
                    activateNeuron(randomNeuron, 1500);
                }

                activationCount++;
            }, 100);

            // Activer tous les lobes
            Object.values(brainLobes).forEach(lobe => {
                lobe.userData.active = true;
            });

            setTimeout(() => {
                Object.values(brainLobes).forEach(lobe => {
                    lobe.userData.active = false;
                });
            }, 5000);
        }

        function resetBrain() {
            // Réinitialiser toutes les activations
            neurons3D.forEach(neuron => {
                neuron.userData.active = false;
                neuron.material.color.setHex(neuron.userData.originalColor);
                neuron.material.emissiveIntensity = 0.4;
                neuron.scale.setScalar(1);
            });

            synapses3D.forEach(synapse => {
                synapse.userData.active = false;
                synapse.material.color.setHex(0x444444);
                synapse.material.opacity = 0.4;
            });

            Object.values(brainLobes).forEach(lobe => {
                lobe.userData.active = false;
                lobe.scale.setScalar(1);
                lobe.material.emissiveIntensity = 0.3;
            });

            currentZone = null;
            document.getElementById('active-zone').textContent = 'Aucune';
        }

        // Mises à jour des métriques
        function updateNeuronCount() {
            document.getElementById('neuron-count').textContent = neurons3D.length;
        }

        function updateSynapseCount() {
            document.getElementById('synapse-count').textContent = synapses3D.length;
        }

        function updateActivityLevel() {
            const activeNeurons = neurons3D.filter(n => n.userData.active).length;
            const activityPercent = Math.round((activeNeurons / neurons3D.length) * 100);

            document.getElementById('activity-level').textContent = activityPercent + '%';
            document.getElementById('active-neurons').textContent = activeNeurons;
            document.getElementById('live-activity').textContent = activeNeurons > 10 ? '🔥' : activeNeurons > 5 ? '⚡' : '🧠';

            // Calculer la fréquence
            const frequency = Math.round(activityPercent * 0.4); // Simulation
            document.getElementById('frequency').textContent = frequency + ' Hz';

            // Calculer l'efficacité
            const efficiency = Math.max(50, 100 - activityPercent * 0.3);
            document.getElementById('efficiency').textContent = Math.round(efficiency) + '%';
        }

        // Mise à jour des métriques en temps réel
        function startMetricsUpdate() {
            setInterval(async () => {
                try {
                    const response = await fetch('/api/metrics');
                    const data = await response.json();

                    if (data.success) {
                        // Mettre à jour la température
                        const temp = data.temperature || 37;
                        document.getElementById('temperature').textContent = temp.toFixed(1) + '°C';

                        // Ajuster l'activité en fonction des métriques réelles
                        const realNeurons = data.neurons || 0;
                        if (realNeurons > 0) {
                            // Activer des neurones en fonction des métriques réelles
                            const activationRate = Math.min(realNeurons / 1000, 0.1);

                            neurons3D.forEach(neuron => {
                                neuron.userData.fireRate = activationRate;
                            });
                        }
                    }
                } catch (error) {
                    console.log('Métriques non disponibles, utilisation des valeurs par défaut');
                }
            }, 2000);
        }

        // Redimensionnement de la fenêtre
        function onWindowResize() {
            const canvas = document.getElementById('brain-canvas');
            camera.aspect = canvas.offsetWidth / canvas.offsetHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.offsetWidth, canvas.offsetHeight);
        }

        // Initialisation au chargement avec vérifications
        function startBrain3D() {
            console.log('🧠 Démarrage de l\'initialisation du cerveau 3D...');

            // Vérifier que Three.js est chargé
            if (typeof THREE === 'undefined') {
                console.error('❌ Three.js non chargé, nouvelle tentative dans 500ms...');
                setTimeout(startBrain3D, 500);
                return;
            }

            // Vérifier que OrbitControls est disponible
            if (typeof THREE.OrbitControls === 'undefined') {
                console.warn('⚠️ OrbitControls non disponible, chargement alternatif...');
                // Utiliser une version alternative ou continuer sans contrôles
            }

            console.log('✅ Three.js détecté, initialisation du cerveau 3D...');
            init3DBrain();
        }

        // Démarrage immédiat et backup
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startBrain3D);
        } else {
            startBrain3D();
        }

        // Backup au cas où
        window.addEventListener('load', () => {
            if (!scene) {
                console.log('🔄 Backup: Nouvelle tentative d\'initialisation...');
                setTimeout(startBrain3D, 500);
            }
        });

        // Gestion des erreurs
        window.addEventListener('error', (e) => {
            console.error('Erreur dans la visualisation 3D:', e.error);
            document.getElementById('loading').innerHTML = `
                <div class="loading-text" style="color: #ff4444;">❌ Erreur de chargement</div>
                <div style="color: #888; font-size: 0.9rem;">Vérifiez la console pour plus de détails</div>
            `;
        });

        console.log('🧠 Script de visualisation 3D spectaculaire chargé !');
    </script>
</body>
</html>
