<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Révolutionnaire v2.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Three.js pour la visualisation 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            animation: backgroundShift 10s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg) brightness(1); }
            50% { filter: hue-rotate(30deg) brightness(1.1); }
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1600px;
            margin: 0 auto;
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
                radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255, 105, 180, 0.2) 0%, transparent 50%);
            backdrop-filter: blur(20px);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 100px rgba(102, 126, 234, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .app-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
            animation: containerShimmer 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes containerShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 1.5rem 2rem;
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .logo {
            position: relative;
            z-index: 2;
        }

        .logo h1 {
            font-size: 2.2rem;
            margin: 0;
            background: linear-gradient(45deg, #fff 0%, #a8e6cf 25%, #667eea 50%, #ff69b4 75%, #fff 100%);
            background-size: 300% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoGlow 3s ease-in-out infinite, logoShift 6s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            font-weight: 900;
            letter-spacing: 2px;
        }

        @keyframes logoGlow {
            0%, 100% { 
                filter: brightness(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
                transform: scale(1);
            }
            50% { 
                filter: brightness(1.3) drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
                transform: scale(1.02);
            }
        }

        @keyframes logoShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes pulse {
            from { opacity: 0.8; }
            to { opacity: 1; }
        }

        .status-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            z-index: 2;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: bold;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .metric-badge {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .metric-badge:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .qi-display {
            display: flex;
            align-items: center;
            gap: 15px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 105, 180, 0.1) 100%);
            padding: 12px 25px;
            border-radius: 30px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 105, 180, 0.4);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3), 
                0 0 30px rgba(255, 105, 180, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: qiGlow 3s ease-in-out infinite;
        }

        @keyframes qiGlow {
            0%, 100% { 
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.3), 
                    0 0 30px rgba(255, 105, 180, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% { 
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.3), 
                    0 0 40px rgba(255, 105, 180, 0.6),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        .qi-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 18px;
            font-size: 14px;
            font-weight: 700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .qi-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .qi-item:hover::before {
            left: 100%;
        }

        .qi-agent {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(102, 126, 234, 0.1));
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .qi-memory {
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.3), rgba(255, 105, 180, 0.1));
            color: #ff69b4;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .qi-total {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
            color: #4caf50;
            border: 1px solid rgba(76, 175, 80, 0.3);
            animation: totalPulse 2s ease-in-out infinite;
        }

        @keyframes totalPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* NOUVELLES AMÉLIORATIONS INTERFACE AVANCÉE */
        .advanced-metrics-panel {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 105, 180, 0.1) 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid rgba(102, 126, 234, 0.3);
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
        }

        .advanced-metrics-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: panelShimmer 4s ease-in-out infinite;
        }

        @keyframes panelShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .metric-card:hover::before {
            left: 100%;
        }

        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 900;
            color: #4ade80;
            text-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            font-weight: 600;
        }

        .performance-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .performance-bar {
            width: 100px;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22c55e);
            border-radius: 3px;
            transition: width 0.3s ease;
            animation: performanceGlow 2s ease-in-out infinite;
        }

        @keyframes performanceGlow {
            0%, 100% { box-shadow: 0 0 5px rgba(74, 222, 128, 0.5); }
            50% { box-shadow: 0 0 15px rgba(74, 222, 128, 0.8); }
        }

        nav {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            padding: 1rem 0;
            overflow-x: auto;
            white-space: nowrap;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: navGlow 3s ease-in-out infinite;
        }

        @keyframes navGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0 2rem;
            min-width: max-content;
            gap: 0.5rem;
        }

        nav ul li {
            flex-shrink: 0;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            display: block;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        nav ul li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        nav ul li a:hover::before {
            left: 100%;
        }

        nav ul li a:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        nav ul li a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        nav ul li a.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        main {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        section {
            display: none;
            width: 100%;
            padding: 1.5rem;
            overflow-y: auto;
        }

        section.active {
            display: block;
        }

        .home-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #667eea;
        }

        .home-button:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        }

        @keyframes valueGlow {
            0%, 100% {
                filter: brightness(1);
                transform: scale(1);
            }
            50% {
                filter: brightness(1.2);
                transform: scale(1.05);
            }
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes brainPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 0 30px rgba(102, 126, 234, 0.5),
                    inset 0 0 30px rgba(255, 255, 255, 0.1);
            }
            50% {
                transform: scale(1.08);
                box-shadow:
                    0 0 50px rgba(102, 126, 234, 0.8),
                    inset 0 0 50px rgba(255, 255, 255, 0.2);
            }
        }

        /* Effets de survol pour les boutons d'interface */
        button:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%) !important;
            transform: translateY(-5px) scale(1.02) !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important;
        }

        /* Effets de focus pour les inputs */
        #chat-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15) !important;
            transform: translateY(-2px) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        #send-button:hover {
            transform: scale(1.1) rotate(5deg) !important;
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5) !important;
        }

        /* Styles pour les contrôles avancés */
        .control-btn {
            background: rgba(192, 132, 252, 0.2);
            border: 1px solid #c084fc;
            color: #c084fc;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-btn:hover {
            background: rgba(192, 132, 252, 0.4);
            transform: translateY(-1px);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #c084fc, #f472b6);
            color: white;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-active { background: #4ade80; }
        .status-warning { background: #fbbf24; }
        .status-error { background: #ef4444; }
        .status-speaking { background: #f59e0b; }
        .status-listening { background: #ef4444; }
        .status-processing { background: #8b5cf6; }

        /* Styles pour les boutons de sécurité */
        .security-button {
            background: linear-gradient(135deg, #FF6B6B, #FF5252);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .security-button.connect {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .security-button.emergency {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
            animation: pulse-emergency 2s infinite;
        }

        @keyframes pulse-emergency {
            0%, 100% { box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(244, 67, 54, 0.8); }
        }

        .security-button.antivirus {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }

        .security-button.clean {
            background: linear-gradient(135deg, #00BCD4, #0097A7);
            box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
        }

        .security-button.status {
            background: linear-gradient(135deg, #607D8B, #455A64);
            box-shadow: 0 4px 15px rgba(96, 125, 139, 0.3);
        }

        .security-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        /* Styles pour le bouton vocal */
        #voice-button {
            transition: all 0.3s ease;
        }

        #voice-button:hover {
            background: rgba(244, 114, 182, 0.4);
            transform: scale(1.1);
        }

        #voice-button.recording {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            animation: pulse 1s infinite;
        }

        /* Styles pour les panneaux */
        #camera-panel, #auto-panel {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Animations pour les générateurs IA */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes audioWave {
            0%, 100% { width: 20%; }
            50% { width: 80%; }
        }

        @keyframes musicNote {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-5px) rotate(5deg); }
            75% { transform: translateY(5px) rotate(-5deg); }
        }

        @keyframes musicBar1 {
            0%, 100% { height: 10px; }
            50% { height: 25px; }
        }

        @keyframes musicBar2 {
            0%, 100% { height: 15px; }
            50% { height: 30px; }
        }

        @keyframes musicBar3 {
            0%, 100% { height: 20px; }
            50% { height: 35px; }
        }

        @keyframes musicBar4 {
            0%, 100% { height: 12px; }
            50% { height: 28px; }
        }

        @keyframes musicBar5 {
            0%, 100% { height: 18px; }
            50% { height: 32px; }
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* Responsive pour les nouveaux éléments */
        @media (max-width: 768px) {
            .security-button {
                padding: 10px 15px;
                font-size: 12px;
                margin: 5px;
            }

            .control-btn {
                padding: 6px 10px;
                font-size: 11px;
            }

            /* Responsive pour le chat avec réflexion */
            #chat > div {
                flex-direction: column !important;
            }

            #chat > div > div {
                min-width: auto !important;
                flex: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton de retour/accueil -->
    <button class="home-button" onclick="showSection('dashboard')">
        <i class="fas fa-home"></i>
    </button>

    <div class="app-container">
        <header>
            <div class="logo">
                <h1>🧠 LOUNA AI Ultra-Autonome</h1>
                <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">
                    Mémoire Thermique Vivante
                </p>
            </div>
            
            <div class="status-container">
                <div class="status">
                    <span id="connection-status">SYSTÈME ACTIF</span>
                </div>
                
                <div class="qi-display">
                    <div class="qi-item qi-agent">
                        <i class="fas fa-robot"></i>
                        <span>Agent QI: <span id="agent-qi-display">100</span></span>
                    </div>
                    <div class="qi-item qi-memory">
                        <i class="fas fa-brain"></i>
                        <span>Mémoire QI: <span id="memory-qi-display">0</span></span>
                    </div>
                    <div class="qi-item qi-total">
                        <i class="fas fa-calculator"></i>
                        <span>QI Total: <span id="total-qi-display">100</span></span>
                    </div>
                </div>
                
                <div class="metric-badge">
                    <i class="fas fa-brain"></i>
                    <span id="neuron-count-header">0 neurones</span>
                </div>
                <div class="metric-badge">
                    <i class="fas fa-thermometer-half"></i>
                    <span id="temp-display-header">37°C</span>
                </div>
                <div class="metric-badge">
                    <i class="fas fa-memory"></i>
                    <span id="memory-entries-header">0 entrées</span>
                </div>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="#" class="active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Tableau de bord
                </a></li>
                <li><a href="#" data-section="chat">
                    <i class="fas fa-comments"></i> Chat IA
                </a></li>
                <li><a href="#" data-section="brain">
                    <i class="fas fa-brain"></i> Cerveau artificiel
                </a></li>
                <li><a href="#" data-section="diagnostics" style="background: linear-gradient(45deg, #00ff88, #e74c3c); border-radius: 10px; margin: 5px 0; box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);">
                    <i class="fas fa-stethoscope"></i> 🔍 Diagnostic & Accélérateurs
                </a></li>
                <li><a href="#" data-section="iq-tests" style="background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 10px; margin: 5px 0; box-shadow: 0 0 15px rgba(102, 126, 234, 0.5);">
                    <i class="fas fa-brain"></i> 🧠 Tests de QI
                </a></li>
                <li><a href="#" data-section="generators" style="background: linear-gradient(45deg, #ff69b4, #8a2be2); border-radius: 10px; margin: 5px 0; box-shadow: 0 0 15px rgba(255, 105, 180, 0.5);">
                    <i class="fas fa-magic"></i> 🎨 Générateurs IA
                </a></li>
                <li><a href="#" onclick="open3DBrain()" style="background: linear-gradient(45deg, #ff69b4, #9d4edd); border-radius: 10px; margin: 5px 0; box-shadow: 0 0 15px rgba(255, 105, 180, 0.5);">
                    <i class="fas fa-cube"></i> 🧠 Cerveau 3D Spectaculaire
                </a></li>
                <li><a href="#" data-section="memory">
                    <i class="fas fa-memory"></i> Mémoire thermique
                </a></li>
                <li><a href="#" data-section="code-interface" style="background: linear-gradient(45deg, #00ff88, #667eea); border-radius: 10px; margin: 5px 0; box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);">
                    <i class="fas fa-code"></i> 💻 Interface de Codage
                </a></li>
                <li><a href="#" data-section="interfaces">
                    <i class="fas fa-cogs"></i> Interfaces spécialisées
                </a></li>
            </ul>
        </nav>

        <main>
            <!-- Section Tableau de bord -->
            <section id="dashboard" class="active">
                <h2><i class="fas fa-tachometer-alt"></i> Tableau de bord Ultra-Révolutionnaire</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-qi">100</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Agent (Fixe)</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-memory-qi">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Mémoire (Progressif)</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-total-qi">100</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">QI Total (Agent + Mémoire)</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-neurons">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Neurones actifs</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-temperature">37°C</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Température</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; transition: all 0.4s ease; position: relative; overflow: hidden;">
                        <div style="font-size: 3rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0; animation: valueGlow 3s ease-in-out infinite;" id="dashboard-memory">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600; text-transform: uppercase; letter-spacing: 1px;">Entrées mémoire</div>
                    </div>
                </div>

                <!-- BOUTON D'ACCÈS RAPIDE AU CERVEAU 3D -->
                <div style="text-align: center; margin: 2rem 0;">
                    <button onclick="open3DBrain()" style="
                        background: linear-gradient(45deg, #ff69b4, #9d4edd, #00ff88);
                        border: none;
                        padding: 20px 40px;
                        border-radius: 50px;
                        color: white;
                        font-size: 1.2rem;
                        font-weight: bold;
                        cursor: pointer;
                        box-shadow: 0 10px 30px rgba(255, 105, 180, 0.4);
                        transition: all 0.3s ease;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                        position: relative;
                        overflow: hidden;
                    " onmouseover="this.style.transform='scale(1.05) translateY(-2px)'; this.style.boxShadow='0 15px 40px rgba(255, 105, 180, 0.6)';"
                       onmouseout="this.style.transform='scale(1) translateY(0)'; this.style.boxShadow='0 10px 30px rgba(255, 105, 180, 0.4)';">
                        🧠 OUVRIR LE CERVEAU 3D SPECTACULAIRE ✨
                    </button>
                </div>

                <!-- PANNEAU DE MÉTRIQUES AVANCÉES -->
                <div class="advanced-metrics-panel">
                    <h3 style="color: #ff69b4; text-shadow: 0 0 10px rgba(255, 105, 180, 0.5); margin-bottom: 20px; font-size: 1.5rem;">
                        📊 Métriques Système Ultra-Avancées
                    </h3>

                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-icon">🧠</div>
                            <div class="metric-value" id="neurons-display">0</div>
                            <div class="metric-label">Neurones Actifs</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="neurons-bar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">🌡️</div>
                            <div class="metric-value" id="temp-display-advanced">37.0°C</div>
                            <div class="metric-label">Température Thermique</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="temp-bar" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">💾</div>
                            <div class="metric-value" id="memory-display-advanced">0</div>
                            <div class="metric-label">Entrées Mémoire</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="memory-bar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">⚡</div>
                            <div class="metric-value" id="efficiency-display-advanced">99.9%</div>
                            <div class="metric-label">Efficacité Système</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="efficiency-bar" style="width: 99%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">🚀</div>
                            <div class="metric-value" id="performance-display-advanced">100%</div>
                            <div class="metric-label">Performance IA</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="performance-bar-advanced" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-icon">🔒</div>
                            <div class="metric-value" id="security-display-advanced">Sécurisé</div>
                            <div class="metric-label">État Sécurité</div>
                            <div class="performance-indicator">
                                <div class="performance-bar">
                                    <div class="performance-fill" id="security-bar-advanced" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SECTION TEST CERVEAU 3D -->
                <div style="margin: 30px 0; padding: 25px; background: linear-gradient(135deg, rgba(255, 105, 180, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%); border-radius: 20px; border: 2px solid rgba(255, 105, 180, 0.3);">
                    <h3 style="color: #ff69b4; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-brain"></i> Test du Cerveau 3D Intégré
                    </h3>

                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-bottom: 20px;">
                        <button onclick="showSection('brain')" style="
                            background: linear-gradient(45deg, #ff69b4, #9d4edd);
                            border: none;
                            padding: 15px 25px;
                            border-radius: 25px;
                            color: white;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-size: 1rem;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🧠 Aller au Cerveau 3D
                        </button>

                        <button onclick="tryInitBrain3D()" style="
                            background: linear-gradient(45deg, #00ff88, #00bfff);
                            border: none;
                            padding: 15px 25px;
                            border-radius: 25px;
                            color: white;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-size: 1rem;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🚀 Forcer Initialisation
                        </button>

                        <button onclick="activateSimpleNeuralStorm()" style="
                            background: linear-gradient(45deg, #ffd700, #ff8c00);
                            border: none;
                            padding: 15px 25px;
                            border-radius: 25px;
                            color: white;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-size: 1rem;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            ⚡ Tempête Neuronale
                        </button>

                        <button onclick="testForceCerveau3D()" style="
                            background: linear-gradient(45deg, #e74c3c, #c0392b);
                            border: none;
                            padding: 15px 25px;
                            border-radius: 25px;
                            color: white;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-size: 1rem;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🔧 Test Diagnostic
                        </button>
                    </div>

                    <div style="text-align: center; font-size: 0.9rem; color: #c084fc;">
                        <div id="brain-3d-status" style="margin-bottom: 10px;">
                            🔄 Statut: En attente d'initialisation...
                        </div>
                        <div style="display: flex; gap: 20px; justify-content: center;">
                            <span>Neurones 3D: <strong id="brain-3d-neurons-dashboard">0</strong></span>
                            <span>Activité: <strong id="brain-3d-activity-dashboard">0%</strong></span>
                        </div>
                    </div>
                </div>

                <!-- SECTION MONITORING SYSTÈME EN TEMPS RÉEL -->
                <div style="margin: 30px 0; padding: 25px; background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%); border-radius: 20px; border: 2px solid rgba(0, 255, 136, 0.3);">
                    <h3 style="color: #00ff88; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-heartbeat"></i> Monitoring Système en Temps Réel
                    </h3>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <!-- Statut Cerveau -->
                        <div style="text-align: center; padding: 15px; background: rgba(255, 105, 180, 0.1); border-radius: 15px; border: 1px solid rgba(255, 105, 180, 0.3);">
                            <div style="color: #ff69b4; font-size: 0.9rem; margin-bottom: 5px;">🧠 Cerveau 3D</div>
                            <div style="font-size: 1.1rem; font-weight: bold;" id="dashboard-brain-status">Initialisation...</div>
                            <div style="font-size: 0.8rem; color: #888; margin-top: 5px;" id="dashboard-brain-neurons">0 neurones</div>
                        </div>

                        <!-- Statut Accélérateurs -->
                        <div style="text-align: center; padding: 15px; background: rgba(0, 255, 136, 0.1); border-radius: 15px; border: 1px solid rgba(0, 255, 136, 0.3);">
                            <div style="color: #00ff88; font-size: 0.9rem; margin-bottom: 5px;">🚀 Kyber</div>
                            <div style="font-size: 1.1rem; font-weight: bold;" id="dashboard-kyber-status">2.4x</div>
                            <div style="font-size: 0.8rem; color: #888; margin-top: 5px;" id="dashboard-kyber-efficiency">94% efficacité</div>
                        </div>

                        <!-- Statut Thermique -->
                        <div style="text-align: center; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 15px; border: 1px solid rgba(255, 215, 0, 0.3);">
                            <div style="color: #ffd700; font-size: 0.9rem; margin-bottom: 5px;">🌡️ Thermique</div>
                            <div style="font-size: 1.1rem; font-weight: bold;" id="dashboard-thermal-temp">37.0°C</div>
                            <div style="font-size: 0.8rem; color: #888; margin-top: 5px;" id="dashboard-thermal-entries">150+ entrées</div>
                        </div>

                        <!-- Statut Sécurité -->
                        <div style="text-align: center; padding: 15px; background: rgba(231, 76, 60, 0.1); border-radius: 15px; border: 1px solid rgba(231, 76, 60, 0.3);">
                            <div style="color: #e74c3c; font-size: 0.9rem; margin-bottom: 5px;">🔒 Sécurité</div>
                            <div style="font-size: 1.1rem; font-weight: bold;" id="dashboard-security-level">NORMAL</div>
                            <div style="font-size: 0.8rem; color: #888; margin-top: 5px;" id="dashboard-security-threats">0 menaces</div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="showSection('diagnostics')" style="
                            background: linear-gradient(45deg, #00ff88, #e74c3c);
                            border: none;
                            padding: 12px 25px;
                            border-radius: 25px;
                            color: white;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            font-size: 1rem;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🔍 Diagnostic Complet
                        </button>
                    </div>
                </div>
            </section>

            <!-- Section Interface de Codage -->
            <section id="code-interface">
                <h2><i class="fas fa-code"></i> Interface de Codage Avancée - LOUNA AI</h2>

                <div style="display: grid; grid-template-columns: 1fr 2fr 1fr; gap: 20px; height: calc(100vh - 200px);">
                    <!-- Panneau gauche - Informations système -->
                    <div style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%); border-radius: 15px; padding: 20px; color: #00ff88; border: 2px solid rgba(0, 255, 136, 0.3); overflow-y: auto;">
                        <h3 style="color: #00ff88; margin-bottom: 20px; text-align: center;">
                            <i class="fas fa-info-circle"></i> Informations Système
                        </h3>

                        <!-- État du système -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(0, 255, 136, 0.1); border-radius: 10px; border: 1px solid rgba(0, 255, 136, 0.3);">
                            <h4 style="color: #00ff88; margin-bottom: 10px;">🔋 État Système</h4>
                            <div style="font-size: 12px; line-height: 1.6;">
                                <div>Status: <span style="color: #00ff88; font-weight: bold;" id="code-system-status">ACTIF</span></div>
                                <div>Uptime: <span id="code-uptime">--:--:--</span></div>
                                <div>CPU: <span id="code-cpu-usage">0%</span></div>
                                <div>RAM: <span id="code-ram-usage">0 MB</span></div>
                                <div>Temp: <span id="code-temperature">37°C</span></div>
                            </div>
                        </div>

                        <!-- Métriques IA -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; border: 1px solid rgba(102, 126, 234, 0.3);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🧠 Métriques IA</h4>
                            <div style="font-size: 12px; line-height: 1.6;">
                                <div>QI Agent: <span style="color: #667eea; font-weight: bold;" id="code-agent-qi">100</span></div>
                                <div>QI Mémoire: <span style="color: #ff69b4; font-weight: bold;" id="code-memory-qi">0</span></div>
                                <div>QI Total: <span style="color: #4ade80; font-weight: bold;" id="code-total-qi">100</span></div>
                                <div>Neurones: <span id="code-neurons">0</span></div>
                                <div>Synapses: <span id="code-synapses">0</span></div>
                            </div>
                        </div>

                        <!-- Capacités de codage -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 105, 180, 0.1); border-radius: 10px; border: 1px solid rgba(255, 105, 180, 0.3);">
                            <h4 style="color: #ff69b4; margin-bottom: 10px;">💻 Capacités Codage</h4>
                            <div style="font-size: 12px; line-height: 1.6;">
                                <div>Langages: <span style="color: #ff69b4;">JS, HTML, CSS, Python</span></div>
                                <div>Frameworks: <span style="color: #ff69b4;">React, Node.js, Express</span></div>
                                <div>Génération: <span style="color: #4ade80; font-weight: bold;" id="code-generation-status">ACTIVE</span></div>
                                <div>Optimisation: <span style="color: #4ade80; font-weight: bold;" id="code-optimization-status">ACTIVE</span></div>
                                <div>Debug: <span style="color: #4ade80; font-weight: bold;" id="code-debug-status">ACTIVE</span></div>
                            </div>
                        </div>

                        <!-- Historique des projets -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 10px; border: 1px solid rgba(255, 215, 0, 0.3);">
                            <h4 style="color: #ffd700; margin-bottom: 10px;">📁 Projets Récents</h4>
                            <div style="font-size: 11px; line-height: 1.4;" id="code-recent-projects">
                                <div style="margin-bottom: 5px; padding: 5px; background: rgba(0, 0, 0, 0.3); border-radius: 5px;">
                                    <div style="color: #ffd700;">🎮 Jeu Morpion</div>
                                    <div style="color: #888;">HTML/CSS/JS - En cours</div>
                                </div>
                                <div style="margin-bottom: 5px; padding: 5px; background: rgba(0, 0, 0, 0.3); border-radius: 5px;">
                                    <div style="color: #ffd700;">🧮 Calculatrice</div>
                                    <div style="color: #888;">JavaScript - Terminé</div>
                                </div>
                                <div style="margin-bottom: 5px; padding: 5px; background: rgba(0, 0, 0, 0.3); border-radius: 5px;">
                                    <div style="color: #ffd700;">🌐 Site Web</div>
                                    <div style="color: #888;">React - En pause</div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques de performance -->
                        <div style="padding: 15px; background: rgba(231, 76, 60, 0.1); border-radius: 10px; border: 1px solid rgba(231, 76, 60, 0.3);">
                            <h4 style="color: #e74c3c; margin-bottom: 10px;">📊 Performance</h4>
                            <div style="font-size: 12px; line-height: 1.6;">
                                <div>Lignes générées: <span style="color: #e74c3c; font-weight: bold;" id="code-lines-generated">0</span></div>
                                <div>Projets créés: <span style="color: #e74c3c; font-weight: bold;" id="code-projects-created">0</span></div>
                                <div>Bugs corrigés: <span style="color: #e74c3c; font-weight: bold;" id="code-bugs-fixed">0</span></div>
                                <div>Temps moyen: <span id="code-avg-time">0.0s</span></div>
                                <div>Efficacité: <span style="color: #4ade80; font-weight: bold;" id="code-efficiency">99.9%</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- Zone centrale - Éditeur de code -->
                    <div style="background: rgba(0, 0, 0, 0.9); border-radius: 15px; border: 2px solid rgba(0, 255, 136, 0.3); display: flex; flex-direction: column;">
                        <!-- Barre d'outils -->
                        <div style="padding: 15px; border-bottom: 1px solid rgba(0, 255, 136, 0.3); display: flex; justify-content: space-between; align-items: center; background: rgba(0, 255, 136, 0.1);">
                            <div style="display: flex; gap: 10px;">
                                <button onclick="generateTicTacToeCode()" style="
                                    background: linear-gradient(45deg, #00ff88, #00bfff);
                                    border: none;
                                    padding: 8px 15px;
                                    border-radius: 20px;
                                    color: white;
                                    font-weight: bold;
                                    cursor: pointer;
                                    font-size: 12px;
                                    transition: all 0.3s ease;
                                ">🎮 Générer Morpion</button>

                                <button onclick="generateCalculatorCode()" style="
                                    background: linear-gradient(45deg, #ff69b4, #9d4edd);
                                    border: none;
                                    padding: 8px 15px;
                                    border-radius: 20px;
                                    color: white;
                                    font-weight: bold;
                                    cursor: pointer;
                                    font-size: 12px;
                                    transition: all 0.3s ease;
                                ">🧮 Générer Calculatrice</button>

                                <button onclick="optimizeCode()" style="
                                    background: linear-gradient(45deg, #ffd700, #ff8c00);
                                    border: none;
                                    padding: 8px 15px;
                                    border-radius: 20px;
                                    color: white;
                                    font-weight: bold;
                                    cursor: pointer;
                                    font-size: 12px;
                                    transition: all 0.3s ease;
                                ">⚡ Optimiser</button>
                            </div>

                            <div style="display: flex; gap: 10px; align-items: center;">
                                <select id="code-language" style="
                                    background: rgba(0, 0, 0, 0.7);
                                    color: #00ff88;
                                    border: 1px solid rgba(0, 255, 136, 0.3);
                                    padding: 5px 10px;
                                    border-radius: 10px;
                                    font-size: 12px;
                                ">
                                    <option value="html">HTML</option>
                                    <option value="javascript">JavaScript</option>
                                    <option value="css">CSS</option>
                                    <option value="python">Python</option>
                                </select>

                                <button onclick="runCode()" style="
                                    background: linear-gradient(45deg, #4ade80, #22c55e);
                                    border: none;
                                    padding: 8px 15px;
                                    border-radius: 20px;
                                    color: white;
                                    font-weight: bold;
                                    cursor: pointer;
                                    font-size: 12px;
                                    transition: all 0.3s ease;
                                ">▶️ Exécuter</button>
                            </div>
                        </div>

                        <!-- Éditeur de code -->
                        <div style="flex: 1; padding: 20px; overflow: auto;">
                            <textarea id="code-editor" style="
                                width: 100%;
                                height: 100%;
                                background: rgba(0, 0, 0, 0.8);
                                color: #00ff88;
                                border: 1px solid rgba(0, 255, 136, 0.3);
                                border-radius: 10px;
                                padding: 15px;
                                font-family: 'Courier New', monospace;
                                font-size: 14px;
                                line-height: 1.5;
                                resize: none;
                                outline: none;
                            " placeholder="// Votre code apparaîtra ici...
// Utilisez les boutons ci-dessus pour générer du code automatiquement

console.log('LOUNA AI - Interface de Codage Prête !');"></textarea>
                        </div>

                        <!-- Zone de sortie -->
                        <div style="height: 150px; border-top: 1px solid rgba(0, 255, 136, 0.3); background: rgba(0, 0, 0, 0.9);">
                            <div style="padding: 10px; border-bottom: 1px solid rgba(0, 255, 136, 0.3); background: rgba(0, 255, 136, 0.1);">
                                <span style="color: #00ff88; font-weight: bold; font-size: 12px;">📟 Console de Sortie</span>
                            </div>
                            <div id="code-output" style="
                                padding: 15px;
                                height: calc(100% - 40px);
                                overflow-y: auto;
                                font-family: 'Courier New', monospace;
                                font-size: 12px;
                                color: #00ff88;
                                line-height: 1.4;
                            ">Prêt pour l'exécution...</div>
                        </div>
                    </div>

                    <!-- Panneau droit - Outils et aide -->
                    <div style="background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%); border-radius: 15px; padding: 20px; color: #667eea; border: 2px solid rgba(102, 126, 234, 0.3); overflow-y: auto;">
                        <h3 style="color: #667eea; margin-bottom: 20px; text-align: center;">
                            <i class="fas fa-tools"></i> Outils & Aide
                        </h3>

                        <!-- Générateurs rapides -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; border: 1px solid rgba(102, 126, 234, 0.3);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🚀 Générateurs</h4>
                            <div style="display: flex; flex-direction: column; gap: 8px;">
                                <button onclick="generateComponent('button')" style="
                                    background: rgba(102, 126, 234, 0.2);
                                    border: 1px solid rgba(102, 126, 234, 0.5);
                                    color: #667eea;
                                    padding: 8px 12px;
                                    border-radius: 15px;
                                    cursor: pointer;
                                    font-size: 11px;
                                    transition: all 0.3s ease;
                                    width: 100%;
                                ">🔘 Bouton HTML</button>

                                <button onclick="generateComponent('form')" style="
                                    background: rgba(102, 126, 234, 0.2);
                                    border: 1px solid rgba(102, 126, 234, 0.5);
                                    color: #667eea;
                                    padding: 8px 12px;
                                    border-radius: 15px;
                                    cursor: pointer;
                                    font-size: 11px;
                                    transition: all 0.3s ease;
                                    width: 100%;
                                ">📝 Formulaire</button>

                                <button onclick="generateComponent('navbar')" style="
                                    background: rgba(102, 126, 234, 0.2);
                                    border: 1px solid rgba(102, 126, 234, 0.5);
                                    color: #667eea;
                                    padding: 8px 12px;
                                    border-radius: 15px;
                                    cursor: pointer;
                                    font-size: 11px;
                                    transition: all 0.3s ease;
                                    width: 100%;
                                ">🧭 Navigation</button>
                            </div>
                        </div>

                        <!-- Snippets de code -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 105, 180, 0.1); border-radius: 10px; border: 1px solid rgba(255, 105, 180, 0.3);">
                            <h4 style="color: #ff69b4; margin-bottom: 10px;">📋 Snippets</h4>
                            <div style="font-size: 11px; line-height: 1.4;" id="code-snippets">
                                <div style="margin-bottom: 8px; padding: 8px; background: rgba(0, 0, 0, 0.3); border-radius: 5px; cursor: pointer;" onclick="insertSnippet('console.log')">
                                    <div style="color: #ff69b4;">console.log()</div>
                                    <div style="color: #888;">Affichage console</div>
                                </div>
                                <div style="margin-bottom: 8px; padding: 8px; background: rgba(0, 0, 0, 0.3); border-radius: 5px; cursor: pointer;" onclick="insertSnippet('function')">
                                    <div style="color: #ff69b4;">function()</div>
                                    <div style="color: #888;">Fonction JS</div>
                                </div>
                                <div style="margin-bottom: 8px; padding: 8px; background: rgba(0, 0, 0, 0.3); border-radius: 5px; cursor: pointer;" onclick="insertSnippet('if-else')">
                                    <div style="color: #ff69b4;">if/else</div>
                                    <div style="color: #888;">Condition</div>
                                </div>
                            </div>
                        </div>

                        <!-- Documentation rapide -->
                        <div style="margin-bottom: 20px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 10px; border: 1px solid rgba(255, 215, 0, 0.3);">
                            <h4 style="color: #ffd700; margin-bottom: 10px;">📚 Documentation</h4>
                            <div style="font-size: 11px; line-height: 1.4;">
                                <div style="margin-bottom: 5px; color: #ffd700;">HTML: Structure de page</div>
                                <div style="margin-bottom: 5px; color: #ffd700;">CSS: Styles et mise en forme</div>
                                <div style="margin-bottom: 5px; color: #ffd700;">JS: Logique et interactivité</div>
                                <div style="margin-bottom: 5px; color: #ffd700;">Python: Scripts et algorithmes</div>
                            </div>
                        </div>

                        <!-- Raccourcis clavier -->
                        <div style="padding: 15px; background: rgba(231, 76, 60, 0.1); border-radius: 10px; border: 1px solid rgba(231, 76, 60, 0.3);">
                            <h4 style="color: #e74c3c; margin-bottom: 10px;">⌨️ Raccourcis</h4>
                            <div style="font-size: 10px; line-height: 1.4;">
                                <div style="margin-bottom: 3px;">Ctrl+S: Sauvegarder</div>
                                <div style="margin-bottom: 3px;">Ctrl+R: Exécuter</div>
                                <div style="margin-bottom: 3px;">Ctrl+G: Générer</div>
                                <div style="margin-bottom: 3px;">Ctrl+O: Optimiser</div>
                                <div style="margin-bottom: 3px;">F5: Actualiser</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Chat -->
            <section id="chat">
                <div style="display: flex; gap: 20px; height: calc(100vh - 200px);">
                    <!-- Zone de chat principale -->
                    <div style="flex: 2;">
                        <h2><i class="fas fa-comments"></i> Chat Avancé avec LOUNA AI</h2>

                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); margin: 20px 0; height: calc(100vh - 300px); min-height: 600px; display: flex; flex-direction: column;">
                    <!-- Barre de statut du chat -->
                    <div style="padding: 15px; border-bottom: 1px solid rgba(255, 255, 255, 0.1); display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2);">
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div class="status-indicator status-active"></div>
                                <span style="font-size: 0.9rem;">LOUNA AI Connectée</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <div class="status-indicator" id="voice-status"></div>
                                <span style="font-size: 0.9rem;" id="voice-status-text">Voix Prête</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="control-btn" onclick="toggleVoiceMode()" id="voice-toggle">
                                <i class="fas fa-microphone"></i> Voix
                            </button>
                            <button class="control-btn" onclick="toggleCameraMode()" id="camera-toggle">
                                <i class="fas fa-camera"></i> Caméra
                            </button>
                            <button class="control-btn" onclick="toggleAutoMode()" id="auto-toggle">
                                <i class="fas fa-robot"></i> Auto
                            </button>
                        </div>
                    </div>

                    <div style="flex: 1; padding: 20px; overflow-y: auto;" id="chat-messages">
                        <div style="background: rgba(102, 126, 234, 0.1); padding: 15px; border-radius: 15px; margin-bottom: 10px; border-left: 4px solid #667eea;">
                            <div style="font-weight: bold; color: #667eea; margin-bottom: 5px;">🧠 LOUNA AI</div>
                            <div>Bonjour ! Je suis LOUNA AI, votre intelligence artificielle avec mémoire thermique ultra-autonome. Chat avancé avec voix, caméra et mode automatique activés !</div>
                        </div>
                    </div>

                    <!-- Panneau caméra (masqué par défaut) -->
                    <div id="camera-panel" style="display: none; padding: 15px; border-top: 1px solid rgba(255, 255, 255, 0.1); background: rgba(0, 0, 0, 0.2);">
                        <video id="camera-preview" style="width: 200px; height: 150px; border-radius: 10px; border: 2px solid #667eea;" autoplay muted></video>
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button class="control-btn" onclick="captureImage()">
                                <i class="fas fa-camera"></i> Capturer
                            </button>
                            <button class="control-btn" onclick="stopCamera()">
                                <i class="fas fa-stop"></i> Arrêter
                            </button>
                        </div>
                    </div>

                    <!-- Panneau mode automatique (masqué par défaut) -->
                    <div id="auto-panel" style="display: none; padding: 15px; border-top: 1px solid rgba(255, 255, 255, 0.1); background: rgba(192, 132, 252, 0.1);">
                        <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <span>Intervalle (sec):</span>
                                <input type="number" id="auto-interval" value="30" min="5" max="300" style="width: 80px; padding: 5px; border: 1px solid #c084fc; border-radius: 5px; background: rgba(0, 0, 0, 0.3); color: white;">
                            </label>
                            <button class="control-btn" onclick="startAutoMode()" id="auto-start">
                                <i class="fas fa-play"></i> Démarrer
                            </button>
                            <button class="control-btn" onclick="stopAutoMode()" id="auto-stop">
                                <i class="fas fa-stop"></i> Arrêter
                            </button>
                            <span id="auto-status" style="font-size: 0.9rem; color: #c084fc;">Mode automatique désactivé</span>
                        </div>
                    </div>

                    <div style="padding: 20px; border-top: 1px solid rgba(255, 255, 255, 0.1); display: flex; gap: 15px; align-items: flex-end;">
                        <textarea id="chat-input" placeholder="Tapez votre message ici..." style="flex: 1; padding: 15px; border: 2px solid rgba(102, 126, 234, 0.2); border-radius: 15px; outline: none; resize: none; font-family: inherit; font-size: 1rem; min-height: 50px; max-height: 120px; background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); transition: all 0.3s ease;" rows="2"></textarea>
                        <button id="voice-button" style="background: rgba(244, 114, 182, 0.2); border: 1px solid #f472b6; color: #f472b6; border-radius: 50%; width: 60px; height: 60px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; margin-right: 10px;">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button id="send-button" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; width: 60px; height: 60px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>

                <!-- Contrôles de sécurité -->
                <div style="margin-top: 20px; padding: 20px; background: rgba(255, 107, 107, 0.1); border-radius: 15px; border: 1px solid rgba(255, 107, 107, 0.3);">
                    <h3 style="color: #ff6b6b; margin-bottom: 15px;"><i class="fas fa-shield-alt"></i> Contrôles de Sécurité</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="security-button connect" onclick="connectMemory()">
                            <i class="fas fa-link"></i> Connecter Mémoire
                        </button>
                        <button class="security-button" onclick="disconnectMemory()">
                            <i class="fas fa-unlink"></i> Déconnecter
                        </button>
                        <button class="security-button emergency" onclick="emergencyStop()">
                            <i class="fas fa-exclamation-triangle"></i> Arrêt d'Urgence
                        </button>
                        <button class="security-button antivirus" onclick="runAntivirus()">
                            <i class="fas fa-virus-slash"></i> Antivirus
                        </button>
                        <button class="security-button clean" onclick="cleanMemory()">
                            <i class="fas fa-broom"></i> Nettoyer
                        </button>
                        <button class="security-button status" onclick="checkStatus()">
                            <i class="fas fa-heartbeat"></i> Statut
                        </button>
                    </div>
                </div>
                    </div>

                    <!-- Zone de réflexion en direct -->
                    <div style="flex: 1; min-width: 350px;">
                        <h3 style="color: #c084fc; margin-bottom: 15px;"><i class="fas fa-brain"></i> Réflexion LOUNA AI en Direct</h3>

                        <div style="background: rgba(192, 132, 252, 0.1); border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(192, 132, 252, 0.3); height: calc(100vh - 350px); min-height: 400px; display: flex; flex-direction: column;">
                            <!-- En-tête de la réflexion -->
                            <div style="padding: 15px; border-bottom: 1px solid rgba(192, 132, 252, 0.2); background: rgba(192, 132, 252, 0.05);">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-weight: bold; color: #c084fc;">🧠 Pensées Autonomes</span>
                                    <div style="display: flex; gap: 10px;">
                                        <div class="status-indicator status-processing"></div>
                                        <span style="font-size: 0.8rem; color: #c084fc;" id="reflection-status">Réflexion Active</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Zone de réflexion -->
                            <div style="flex: 1; padding: 15px; overflow-y: auto;" id="reflection-feed">
                                <div style="background: rgba(192, 132, 252, 0.1); padding: 12px; border-radius: 10px; margin-bottom: 10px; border-left: 3px solid #c084fc;">
                                    <div style="font-size: 0.8rem; color: #c084fc; margin-bottom: 5px;">[Démarrage]</div>
                                    <div style="color: #e2e8f0;">🧠 Système de réflexion LOUNA AI initialisé - Surveillance des pensées autonomes en cours...</div>
                                </div>
                            </div>

                            <!-- Métriques de réflexion -->
                            <div style="padding: 15px; border-top: 1px solid rgba(192, 132, 252, 0.2); background: rgba(192, 132, 252, 0.05);">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.8rem;">
                                    <div style="text-align: center;">
                                        <div style="color: #c084fc; font-weight: bold;" id="reflection-count">0</div>
                                        <div style="color: #94a3b8;">Réflexions</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="color: #c084fc; font-weight: bold;" id="reflection-frequency">0/min</div>
                                        <div style="color: #94a3b8;">Fréquence</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Cerveau artificiel -->
            <section id="brain">
                <h2><i class="fas fa-brain"></i> Cerveau artificiel ultra-autonome</h2>

                <div style="text-align: center; padding: 30px; background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 20px; margin: 20px 0;">
                    <div style="font-size: 4rem; font-weight: bold; background: linear-gradient(45deg, #667eea, #764ba2, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 20px 0; text-shadow: 0 0 30px rgba(168, 230, 207, 0.8); animation: numberPulse 2s ease-in-out infinite;" id="brain-neuron-count">0</div>
                    <p style="font-size: 1.1rem; margin-bottom: 20px;">
                        <i class="fas fa-bolt"></i> Neurones générés automatiquement par température thermique
                    </p>

                    <!-- Visualisation 3D du Cerveau -->
                    <div style="position: relative; width: 100%; max-width: 600px; height: 400px; margin: 30px auto; border-radius: 20px; overflow: hidden; background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a1a0a 100%); border: 2px solid rgba(255, 105, 180, 0.3); box-shadow: 0 0 30px rgba(255, 105, 180, 0.3);">
                        <div id="brain-3d-canvas" style="width: 100%; height: 100%; position: relative;">
                            <div id="brain-3d-loading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff69b4; text-align: center; z-index: 10;">
                                <div style="font-size: 3rem; margin-bottom: 15px; animation: brainPulse 2s ease-in-out infinite;">🧠</div>
                                <div style="font-size: 1.2rem; margin-bottom: 15px;">Initialisation du Cerveau 3D...</div>
                                <div style="width: 200px; height: 4px; background: rgba(255, 255, 255, 0.2); border-radius: 2px; overflow: hidden; margin: 0 auto;">
                                    <div style="height: 100%; background: linear-gradient(90deg, #ff69b4, #9d4edd, #00ff88); border-radius: 2px; animation: loading 2s infinite;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Contrôles 3D -->
                        <div style="position: absolute; top: 10px; right: 10px; display: flex; gap: 5px; z-index: 100;">
                            <button onclick="initSimpleBrain3D()" style="
                                background: rgba(255, 105, 180, 0.8);
                                border: none;
                                padding: 8px 12px;
                                border-radius: 15px;
                                color: white;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.background='rgba(255, 105, 180, 1)'" onmouseout="this.style.background='rgba(255, 105, 180, 0.8)'">
                                🧠 3D
                            </button>
                            <button onclick="activateSimpleNeuralStorm()" style="
                                background: rgba(157, 78, 221, 0.8);
                                border: none;
                                padding: 8px 12px;
                                border-radius: 15px;
                                color: white;
                                font-size: 0.8rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.background='rgba(157, 78, 221, 1)'" onmouseout="this.style.background='rgba(157, 78, 221, 0.8)'">
                                ⚡ Storm
                            </button>
                        </div>

                        <!-- Métriques 3D -->
                        <div style="position: absolute; bottom: 10px; left: 10px; background: rgba(0, 0, 0, 0.7); padding: 10px; border-radius: 10px; font-size: 0.8rem; z-index: 100;">
                            <div style="color: #ff69b4;">Neurones: <span id="brain-3d-neurons">0</span></div>
                            <div style="color: #9d4edd;">Synapses: <span id="brain-3d-synapses">0</span></div>
                            <div style="color: #00ff88;">Activité: <span id="brain-3d-activity">0%</span></div>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: space-around; margin-top: 30px; gap: 20px;">
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">700/jour</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Neurogenèse</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">95%</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Activité synaptique</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); flex: 1; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #a8e6cf; margin-bottom: 5px;">99.9%</div>
                            <div style="font-size: 0.8rem; opacity: 0.8;">Efficacité neurale</div>
                        </div>
                    </div>

                    <p style="font-size: 1rem; margin-top: 20px; opacity: 0.9;">
                        <i class="fas fa-brain"></i> Système vivant ultra-intelligent avec conscience artificielle évolutive
                    </p>
                </div>
            </section>

            <!-- Section Mémoire thermique -->
            <section id="memory">
                <h2><i class="fas fa-memory"></i> Mémoire thermique</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-temperature">37°C</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Température système</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-entries">0</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Entrées stockées</div>
                    </div>
                    <div style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center;">
                        <div style="font-size: 2.5rem; font-weight: 900; background: linear-gradient(45deg, #a8e6cf, #667eea, #ff69b4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin: 15px 0;" id="memory-efficiency">95%</div>
                        <div style="font-size: 1rem; opacity: 0.9; font-weight: 600;">Efficacité</div>
                    </div>
                </div>
            </section>

            <!-- Section Générateurs IA -->
            <section id="generators">
                <h2><i class="fas fa-magic"></i> Générateurs IA Ultra-Avancés</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin: 2rem 0;">

                    <!-- Générateur d'Images IA -->
                    <div style="background: linear-gradient(135deg, rgba(255, 105, 180, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(255, 105, 180, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #ff69b4; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-image" style="font-size: 1.5rem;"></i>
                            Générateur d'Images IA
                        </h3>
                        <div style="margin-bottom: 1rem;">
                            <textarea id="image-prompt" placeholder="Décrivez l'image que vous voulez générer..." style="width: 100%; height: 80px; padding: 10px; border: 1px solid rgba(255, 105, 180, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white; resize: vertical;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px; margin-bottom: 1rem;">
                            <select id="image-style" style="flex: 1; padding: 10px; border: 1px solid rgba(255, 105, 180, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="realistic">Réaliste</option>
                                <option value="artistic">Artistique</option>
                                <option value="anime">Anime</option>
                                <option value="abstract">Abstrait</option>
                                <option value="cyberpunk">Cyberpunk</option>
                            </select>
                            <select id="image-size" style="flex: 1; padding: 10px; border: 1px solid rgba(255, 105, 180, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="512x512">512x512</option>
                                <option value="1024x1024">1024x1024</option>
                                <option value="1920x1080">1920x1080</option>
                            </select>
                        </div>
                        <button onclick="generateImage()" style="width: 100%; padding: 15px; background: linear-gradient(45deg, #ff69b4, #9d4edd); border: none; border-radius: 15px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                            🎨 Générer Image IA
                        </button>
                        <div id="image-result" style="margin-top: 1rem; text-align: center; min-height: 50px; border: 2px dashed rgba(255, 105, 180, 0.3); border-radius: 10px; padding: 20px; display: flex; align-items: center; justify-content: center; color: rgba(255, 255, 255, 0.6);">
                            L'image générée apparaîtra ici...
                        </div>
                    </div>

                    <!-- Générateur de Vidéos IA -->
                    <div style="background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 191, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(0, 255, 136, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #00ff88; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-video" style="font-size: 1.5rem;"></i>
                            Générateur de Vidéos IA
                        </h3>
                        <div style="margin-bottom: 1rem;">
                            <textarea id="video-prompt" placeholder="Décrivez la vidéo que vous voulez créer..." style="width: 100%; height: 80px; padding: 10px; border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white; resize: vertical;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px; margin-bottom: 1rem;">
                            <select id="video-duration" style="flex: 1; padding: 10px; border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="5">5 secondes</option>
                                <option value="10">10 secondes</option>
                                <option value="15">15 secondes</option>
                                <option value="30">30 secondes</option>
                            </select>
                            <select id="video-quality" style="flex: 1; padding: 10px; border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="720p">720p HD</option>
                                <option value="1080p">1080p Full HD</option>
                                <option value="4k">4K Ultra HD</option>
                            </select>
                        </div>
                        <button onclick="generateVideo()" style="width: 100%; padding: 15px; background: linear-gradient(45deg, #00ff88, #00bfff); border: none; border-radius: 15px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                            🎬 Générer Vidéo IA
                        </button>
                        <div id="video-result" style="margin-top: 1rem; text-align: center; min-height: 50px; border: 2px dashed rgba(0, 255, 136, 0.3); border-radius: 10px; padding: 20px; display: flex; align-items: center; justify-content: center; color: rgba(255, 255, 255, 0.6);">
                            La vidéo générée apparaîtra ici...
                        </div>
                    </div>

                    <!-- Générateur Audio IA -->
                    <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 140, 0, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(255, 215, 0, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #ffd700; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-microphone-alt" style="font-size: 1.5rem;"></i>
                            Générateur Audio IA
                        </h3>
                        <div style="margin-bottom: 1rem;">
                            <textarea id="audio-prompt" placeholder="Décrivez l'audio que vous voulez créer (voix, effets sonores...)..." style="width: 100%; height: 80px; padding: 10px; border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white; resize: vertical;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px; margin-bottom: 1rem;">
                            <select id="audio-type" style="flex: 1; padding: 10px; border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="voice">Voix synthétique</option>
                                <option value="sfx">Effets sonores</option>
                                <option value="ambient">Ambiance</option>
                                <option value="speech">Discours</option>
                            </select>
                            <select id="audio-voice" style="flex: 1; padding: 10px; border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="female">Voix féminine</option>
                                <option value="male">Voix masculine</option>
                                <option value="child">Voix enfant</option>
                                <option value="robot">Voix robot</option>
                            </select>
                        </div>
                        <button onclick="generateAudio()" style="width: 100%; padding: 15px; background: linear-gradient(45deg, #ffd700, #ff8c00); border: none; border-radius: 15px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                            🎵 Générer Audio IA
                        </button>
                        <div id="audio-result" style="margin-top: 1rem; text-align: center; min-height: 50px; border: 2px dashed rgba(255, 215, 0, 0.3); border-radius: 10px; padding: 20px; display: flex; align-items: center; justify-content: center; color: rgba(255, 255, 255, 0.6);">
                            L'audio généré apparaîtra ici...
                        </div>
                    </div>

                    <!-- Générateur de Musique IA -->
                    <div style="background: linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(75, 0, 130, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(138, 43, 226, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #8a2be2; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-music" style="font-size: 1.5rem;"></i>
                            Générateur de Musique IA
                        </h3>
                        <div style="margin-bottom: 1rem;">
                            <textarea id="music-prompt" placeholder="Décrivez la musique que vous voulez composer..." style="width: 100%; height: 80px; padding: 10px; border: 1px solid rgba(138, 43, 226, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white; resize: vertical;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px; margin-bottom: 1rem;">
                            <select id="music-genre" style="flex: 1; padding: 10px; border: 1px solid rgba(138, 43, 226, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="electronic">Électronique</option>
                                <option value="classical">Classique</option>
                                <option value="jazz">Jazz</option>
                                <option value="rock">Rock</option>
                                <option value="ambient">Ambient</option>
                                <option value="cinematic">Cinématique</option>
                            </select>
                            <select id="music-tempo" style="flex: 1; padding: 10px; border: 1px solid rgba(138, 43, 226, 0.3); border-radius: 10px; background: rgba(0, 0, 0, 0.2); color: white;">
                                <option value="slow">Lent (60-80 BPM)</option>
                                <option value="medium">Moyen (80-120 BPM)</option>
                                <option value="fast">Rapide (120-160 BPM)</option>
                                <option value="very-fast">Très rapide (160+ BPM)</option>
                            </select>
                        </div>
                        <button onclick="generateMusic()" style="width: 100%; padding: 15px; background: linear-gradient(45deg, #8a2be2, #4b0082); border: none; border-radius: 15px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                            🎼 Composer Musique IA
                        </button>
                        <div id="music-result" style="margin-top: 1rem; text-align: center; min-height: 50px; border: 2px dashed rgba(138, 43, 226, 0.3); border-radius: 10px; padding: 20px; display: flex; align-items: center; justify-content: center; color: rgba(255, 255, 255, 0.6);">
                            La musique générée apparaîtra ici...
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Diagnostic et Accélérateurs -->
            <section id="diagnostics">
                <h2><i class="fas fa-stethoscope"></i> Diagnostic et Accélérateurs Ultra-Avancés</h2>

                <!-- État de Santé du Cerveau -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; margin: 2rem 0;">

                    <!-- Diagnostic Cerveau 3D -->
                    <div style="background: linear-gradient(135deg, rgba(255, 105, 180, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(255, 105, 180, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #ff69b4; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-brain" style="font-size: 1.5rem;"></i>
                            État de Santé du Cerveau 3D
                        </h3>

                        <div id="brain-health-status" style="margin-bottom: 1rem; padding: 15px; background: rgba(0, 0, 0, 0.3); border-radius: 10px; border-left: 4px solid #ff69b4;">
                            <div style="color: #ff69b4; font-weight: bold; margin-bottom: 5px;">🔄 Diagnostic en cours...</div>
                            <div style="font-size: 0.9rem; color: #ccc;">Vérification des systèmes neuronaux...</div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 10px; background: rgba(255, 105, 180, 0.1); border-radius: 8px;">
                                <div style="color: #ff69b4; font-size: 0.8rem;">Neurones Actifs</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="health-neurons">0</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(157, 78, 221, 0.1); border-radius: 8px;">
                                <div style="color: #9d4edd; font-size: 0.8rem;">Synapses</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="health-synapses">0</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(0, 255, 136, 0.1); border-radius: 8px;">
                                <div style="color: #00ff88; font-size: 0.8rem;">Activité</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="health-activity">0%</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(255, 215, 0, 0.1); border-radius: 8px;">
                                <div style="color: #ffd700; font-size: 0.8rem;">Température</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="health-temperature">37°C</div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px;">
                            <button onclick="runBrainHealthCheck()" style="flex: 1; padding: 10px; background: linear-gradient(45deg, #ff69b4, #9d4edd); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                                🔍 Diagnostic Complet
                            </button>
                            <button onclick="repairBrain3D()" style="flex: 1; padding: 10px; background: linear-gradient(45deg, #00ff88, #00bfff); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                                🔧 Réparer
                            </button>
                        </div>
                    </div>

                    <!-- Accélérateurs Kyber -->
                    <div style="background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 191, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(0, 255, 136, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #00ff88; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-rocket" style="font-size: 1.5rem;"></i>
                            Accélérateurs Kyber en Cascade
                        </h3>

                        <div id="kyber-status" style="margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span style="color: #00ff88;">Accélérateur Principal</span>
                                <span id="kyber-main-status" style="color: #00ff88;">🟢 ACTIF</span>
                            </div>
                            <div style="width: 100%; height: 8px; background: rgba(0, 255, 136, 0.2); border-radius: 4px; overflow: hidden;">
                                <div id="kyber-main-bar" style="height: 100%; background: linear-gradient(90deg, #00ff88, #00bfff); border-radius: 4px; width: 85%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 10px; background: rgba(0, 255, 136, 0.1); border-radius: 8px;">
                                <div style="color: #00ff88; font-size: 0.8rem;">Vitesse</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="kyber-speed">2.4x</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(0, 191, 255, 0.1); border-radius: 8px;">
                                <div style="color: #00bfff; font-size: 0.8rem;">Efficacité</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="kyber-efficiency">94%</div>
                            </div>
                        </div>

                        <div id="kyber-cascade" style="margin-bottom: 1rem;">
                            <div style="color: #00ff88; font-size: 0.9rem; margin-bottom: 10px;">Cascade d'Accélérateurs:</div>
                            <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                                <span style="padding: 4px 8px; background: rgba(0, 255, 136, 0.2); border-radius: 12px; font-size: 0.8rem;">Kyber-1 ✅</span>
                                <span style="padding: 4px 8px; background: rgba(0, 255, 136, 0.2); border-radius: 12px; font-size: 0.8rem;">Kyber-2 ✅</span>
                                <span style="padding: 4px 8px; background: rgba(0, 255, 136, 0.2); border-radius: 12px; font-size: 0.8rem;">Kyber-3 ✅</span>
                                <span style="padding: 4px 8px; background: rgba(255, 215, 0, 0.2); border-radius: 12px; font-size: 0.8rem;">Kyber-4 ⚠️</span>
                                <span style="padding: 4px 8px; background: rgba(255, 105, 180, 0.2); border-radius: 12px; font-size: 0.8rem;">Kyber-5 🔄</span>
                            </div>
                        </div>

                        <button onclick="optimizeKyberAccelerators()" style="width: 100%; padding: 10px; background: linear-gradient(45deg, #00ff88, #00bfff); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                            🚀 Optimiser Accélérateurs
                        </button>
                    </div>

                    <!-- Mémoire Thermique -->
                    <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 140, 0, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(255, 215, 0, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #ffd700; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-thermometer-half" style="font-size: 1.5rem;"></i>
                            Mémoire Thermique Ultra-Autonome
                        </h3>

                        <div style="margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                <span style="color: #ffd700;">Température CPU Réelle</span>
                                <span id="thermal-cpu-temp" style="color: #ffd700; font-weight: bold;">37.0°C</span>
                            </div>
                            <div style="width: 100%; height: 8px; background: rgba(255, 215, 0, 0.2); border-radius: 4px; overflow: hidden;">
                                <div id="thermal-cpu-bar" style="height: 100%; background: linear-gradient(90deg, #ffd700, #ff8c00); border-radius: 4px; width: 60%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 10px; background: rgba(255, 215, 0, 0.1); border-radius: 8px;">
                                <div style="color: #ffd700; font-size: 0.8rem;">Entrées Mémoire</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="thermal-entries">0</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(255, 140, 0, 0.1); border-radius: 8px;">
                                <div style="color: #ff8c00; font-size: 0.8rem;">Zones Actives</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="thermal-zones">6</div>
                            </div>
                        </div>

                        <div id="thermal-zones-detail" style="margin-bottom: 1rem;">
                            <div style="color: #ffd700; font-size: 0.9rem; margin-bottom: 10px;">Zones Mémoire:</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; font-size: 0.8rem;">
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 1 ✅</span>
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 2 ✅</span>
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 3 ✅</span>
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 4 ✅</span>
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 5 ✅</span>
                                <span style="padding: 4px; background: rgba(255, 215, 0, 0.2); border-radius: 6px; text-align: center;">Zone 6 ✅</span>
                            </div>
                        </div>

                        <button onclick="analyzeThermalMemory()" style="width: 100%; padding: 10px; background: linear-gradient(45deg, #ffd700, #ff8c00); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                            🔥 Analyser Mémoire Thermique
                        </button>
                    </div>

                    <!-- Système de Sécurité d'Urgence -->
                    <div style="background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(231, 76, 60, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #e74c3c; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-shield-alt" style="font-size: 1.5rem;"></i>
                            Système de Sécurité d'Urgence
                        </h3>

                        <div id="security-status" style="margin-bottom: 1rem; padding: 15px; background: rgba(0, 0, 0, 0.3); border-radius: 10px; border-left: 4px solid #e74c3c;">
                            <div style="color: #e74c3c; font-weight: bold; margin-bottom: 5px;" id="security-level">🔒 Niveau: NORMAL</div>
                            <div style="font-size: 0.9rem; color: #ccc;" id="security-threats">Aucune menace détectée</div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 10px; background: rgba(231, 76, 60, 0.1); border-radius: 8px;">
                                <div style="color: #e74c3c; font-size: 0.8rem;">Scans Effectués</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="security-scans">0</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(192, 57, 43, 0.1); border-radius: 8px;">
                                <div style="color: #c0392b; font-size: 0.8rem;">Menaces Bloquées</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="security-blocked">0</div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 10px;">
                            <button onclick="runSecurityScan()" style="flex: 1; padding: 10px; background: linear-gradient(45deg, #e74c3c, #c0392b); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                                🔍 Scan Sécurité
                            </button>
                            <button onclick="activateEmergencyProtocol()" style="flex: 1; padding: 10px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border: none; border-radius: 10px; color: white; font-weight: bold; cursor: pointer;">
                                🚨 Urgence
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section Tests de QI -->
            <section id="iq-tests">
                <h2><i class="fas fa-brain"></i> Tests de QI Ultra-Avancés pour LOUNA AI</h2>

                <!-- Tableau de bord des tests -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin: 2rem 0;">

                    <!-- Historique des tests -->
                    <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(102, 126, 234, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #667eea; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-chart-line" style="font-size: 1.5rem;"></i>
                            Évolution du QI
                        </h3>

                        <div style="text-align: center; margin-bottom: 1rem;">
                            <div style="font-size: 3rem; font-weight: bold; background: linear-gradient(45deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;" id="current-iq-display">100</div>
                            <div style="font-size: 1rem; color: #888;">QI Actuel (Réel)</div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 10px; background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                                <div style="color: #667eea; font-size: 0.8rem;">Tests Passés</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="tests-completed">0</div>
                            </div>
                            <div style="text-align: center; padding: 10px; background: rgba(118, 75, 162, 0.1); border-radius: 8px;">
                                <div style="color: #764ba2; font-size: 0.8rem;">Progression</div>
                                <div style="font-size: 1.2rem; font-weight: bold;" id="iq-progression">+0</div>
                            </div>
                        </div>

                        <div id="iq-history" style="max-height: 150px; overflow-y: auto; font-size: 0.9rem;">
                            <div style="color: #888; text-align: center; padding: 20px;">
                                Aucun test effectué
                            </div>
                        </div>
                    </div>

                    <!-- Types de tests disponibles -->
                    <div style="background: linear-gradient(135deg, rgba(255, 105, 180, 0.1) 0%, rgba(157, 78, 221, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(255, 105, 180, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #ff69b4; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-puzzle-piece" style="font-size: 1.5rem;"></i>
                            Types de Tests
                        </h3>

                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            <button onclick="startIQTest('logic')" style="
                                background: linear-gradient(45deg, #ff69b4, #9d4edd);
                                border: none;
                                padding: 12px 15px;
                                border-radius: 10px;
                                color: white;
                                font-weight: bold;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-align: left;
                            " onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                🧩 Logique & Raisonnement
                            </button>

                            <button onclick="startIQTest('memory')" style="
                                background: linear-gradient(45deg, #00ff88, #00bfff);
                                border: none;
                                padding: 12px 15px;
                                border-radius: 10px;
                                color: white;
                                font-weight: bold;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-align: left;
                            " onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                🧠 Mémoire & Apprentissage
                            </button>

                            <button onclick="startIQTest('pattern')" style="
                                background: linear-gradient(45deg, #ffd700, #ff8c00);
                                border: none;
                                padding: 12px 15px;
                                border-radius: 10px;
                                color: white;
                                font-weight: bold;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-align: left;
                            " onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                🔍 Reconnaissance de Motifs
                            </button>

                            <button onclick="startIQTest('verbal')" style="
                                background: linear-gradient(45deg, #e74c3c, #c0392b);
                                border: none;
                                padding: 12px 15px;
                                border-radius: 10px;
                                color: white;
                                font-weight: bold;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-align: left;
                            " onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                📝 Compréhension Verbale
                            </button>

                            <button onclick="startIQTest('comprehensive')" style="
                                background: linear-gradient(45deg, #667eea, #764ba2);
                                border: none;
                                padding: 15px;
                                border-radius: 10px;
                                color: white;
                                font-weight: bold;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-align: center;
                                font-size: 1.1rem;
                            " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                🚀 TEST COMPLET (30 min)
                            </button>
                        </div>
                    </div>

                    <!-- Statut du test en cours -->
                    <div style="background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 191, 255, 0.1) 100%); padding: 2rem; border-radius: 20px; border: 2px solid rgba(0, 255, 136, 0.3); backdrop-filter: blur(15px);">
                        <h3 style="color: #00ff88; margin-bottom: 1rem; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-stopwatch" style="font-size: 1.5rem;"></i>
                            Test en Cours
                        </h3>

                        <div id="test-status" style="text-align: center; margin-bottom: 1rem;">
                            <div style="color: #888; font-size: 1.1rem;">Aucun test actif</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">Sélectionnez un type de test</div>
                        </div>

                        <div id="test-progress" style="display: none;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span style="color: #00ff88;">Question <span id="current-question">1</span>/<span id="total-questions">10</span></span>
                                <span style="color: #00bfff;" id="time-remaining">05:00</span>
                            </div>
                            <div style="width: 100%; height: 8px; background: rgba(0, 255, 136, 0.2); border-radius: 4px; overflow: hidden;">
                                <div id="progress-bar" style="height: 100%; background: linear-gradient(90deg, #00ff88, #00bfff); border-radius: 4px; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="margin-top: 1rem; display: flex; gap: 10px;">
                            <button onclick="pauseTest()" id="pause-btn" style="flex: 1; padding: 10px; background: rgba(255, 215, 0, 0.2); border: 1px solid #ffd700; color: #ffd700; border-radius: 8px; cursor: pointer; display: none;">
                                ⏸️ Pause
                            </button>
                            <button onclick="stopTest()" id="stop-btn" style="flex: 1; padding: 10px; background: rgba(231, 76, 60, 0.2); border: 1px solid #e74c3c; color: #e74c3c; border-radius: 8px; cursor: pointer; display: none;">
                                ⏹️ Arrêter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Interface de test -->
                <div id="test-interface" style="display: none; margin: 2rem 0; padding: 2rem; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 20px; border: 2px solid rgba(102, 126, 234, 0.3);">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <h3 style="color: #667eea; margin-bottom: 0.5rem;" id="test-title">Test de QI en cours</h3>
                        <div style="color: #888; font-size: 0.9rem;" id="test-description">Répondez aux questions suivantes</div>
                    </div>

                    <div id="question-container" style="max-width: 800px; margin: 0 auto;">
                        <div style="background: rgba(255, 255, 255, 0.1); padding: 2rem; border-radius: 15px; margin-bottom: 2rem;">
                            <div style="color: #667eea; font-size: 0.9rem; margin-bottom: 1rem;" id="question-number">Question 1/10</div>
                            <div style="font-size: 1.2rem; line-height: 1.6; margin-bottom: 2rem;" id="question-text">
                                Chargement de la question...
                            </div>

                            <div id="question-options" style="display: grid; gap: 10px;">
                                <!-- Options générées dynamiquement -->
                            </div>

                            <div style="margin-top: 2rem; display: flex; justify-content: space-between; align-items: center;">
                                <button onclick="previousQuestion()" id="prev-btn" style="
                                    background: rgba(102, 126, 234, 0.2);
                                    border: 1px solid #667eea;
                                    color: #667eea;
                                    padding: 10px 20px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    display: none;
                                ">
                                    ← Précédent
                                </button>

                                <div style="color: #888; font-size: 0.9rem;" id="time-display">
                                    Temps restant: <span id="test-timer">05:00</span>
                                </div>

                                <button onclick="nextQuestion()" id="next-btn" style="
                                    background: linear-gradient(45deg, #667eea, #764ba2);
                                    border: none;
                                    color: white;
                                    padding: 10px 20px;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    font-weight: bold;
                                ">
                                    Suivant →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résultats du test -->
                <div id="test-results" style="display: none; margin: 2rem 0; padding: 2rem; background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 191, 255, 0.1) 100%); border-radius: 20px; border: 2px solid rgba(0, 255, 136, 0.3);">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <h3 style="color: #00ff88; margin-bottom: 1rem;">🎉 Test Terminé !</h3>
                        <div style="font-size: 4rem; font-weight: bold; background: linear-gradient(45deg, #00ff88, #00bfff); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;" id="final-iq-score">
                            175
                        </div>
                        <div style="color: #888; font-size: 1.1rem;">Nouveau QI de LOUNA AI</div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                        <div style="text-align: center; padding: 1rem; background: rgba(0, 255, 136, 0.1); border-radius: 10px;">
                            <div style="color: #00ff88; font-size: 0.9rem;">Score</div>
                            <div style="font-size: 1.5rem; font-weight: bold;" id="test-score">8/10</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: rgba(0, 191, 255, 0.1); border-radius: 10px;">
                            <div style="color: #00bfff; font-size: 0.9rem;">Temps</div>
                            <div style="font-size: 1.5rem; font-weight: bold;" id="test-time">04:23</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: rgba(255, 215, 0, 0.1); border-radius: 10px;">
                            <div style="color: #ffd700; font-size: 0.9rem;">Progression</div>
                            <div style="font-size: 1.5rem; font-weight: bold;" id="iq-change">+4</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: rgba(255, 105, 180, 0.1); border-radius: 10px;">
                            <div style="color: #ff69b4; font-size: 0.9rem;">Niveau</div>
                            <div style="font-size: 1.5rem; font-weight: bold;" id="iq-level">Supérieur</div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="saveTestResults()" style="
                            background: linear-gradient(45deg, #00ff88, #00bfff);
                            border: none;
                            color: white;
                            padding: 15px 30px;
                            border-radius: 10px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 1.1rem;
                            margin-right: 10px;
                        ">
                            💾 Sauvegarder Résultats
                        </button>
                        <button onclick="startNewTest()" style="
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            border: none;
                            color: white;
                            padding: 15px 30px;
                            border-radius: 10px;
                            cursor: pointer;
                            font-weight: bold;
                            font-size: 1.1rem;
                        ">
                            🔄 Nouveau Test
                        </button>
                    </div>
                </div>
            </section>

            <!-- Section Interfaces spécialisées -->
            <section id="interfaces">
                <h2><i class="fas fa-cogs"></i> Interfaces spécialisées</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin: 2rem 0;">
                    <button onclick="navigateToInterface('/real')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #667eea;"></i>
                        Chat Avancé
                    </button>
                    <button onclick="navigateToInterface('/brain-visualization.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-brain" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #ff69b4;"></i>
                        Cerveau 3D
                    </button>
                    <button onclick="navigateToInterface('/thermal-memory-dashboard.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-thermometer-half" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #ffaa00;"></i>
                        Mémoire Thermique
                    </button>
                    <button onclick="navigateToInterface('/brain-monitoring-complete.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #4caf50;"></i>
                        Monitoring Complet
                    </button>
                    <button onclick="navigateToInterface('/qi-evolution-test.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-graduation-cap" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #9c27b0;"></i>
                        Tests QI
                    </button>
                    <button onclick="navigateToInterface('/training-interface.html')" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%); border: 1px solid rgba(255, 255, 255, 0.3); color: white; padding: 2rem; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(15px); text-align: center; font-size: 1.1rem;">
                        <i class="fas fa-dumbbell" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #e91e63;"></i>
                        Formation
                    </button>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Variables globales
        let currentSection = 'dashboard';
        let voiceMode = false;
        let cameraMode = false;
        let autoMode = false;
        let autoInterval = null;
        let isRecording = false;
        let mediaRecorder = null;
        let cameraStream = null;

        // Fonction pour afficher une section
        function showSection(sectionName) {
            // Masquer toutes les sections
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Désactiver tous les liens de navigation
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionName;
            }

            // Activer le lien correspondant
            const targetLink = document.querySelector(`nav a[data-section="${sectionName}"]`);
            if (targetLink) {
                targetLink.classList.add('active');
            }

            // INITIALISER LE CERVEAU 3D SI ON VA SUR LA SECTION BRAIN
            if (sectionName === 'brain') {
                console.log('🧠 Navigation vers section brain - initialisation du cerveau 3D...');
                setTimeout(() => {
                    if (!brain3DActive) {
                        tryInitBrain3D();
                    }
                }, 500);
            }

            console.log(`Section changée vers: ${sectionName}`);
        }

        // Fonction pour naviguer vers une interface spécialisée
        function navigateToInterface(path) {
            console.log(`Navigation vers: ${path}`);
            window.location.href = path;
        }

        // Fonction pour envoyer un message dans le chat
        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            // Ajouter le message de l'utilisateur
            addMessageToChat('user', message);
            input.value = '';

            // Ajouter une réflexion sur le message reçu
            addReflection(`💭 Nouveau message reçu: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);

            // Envoyer le message au serveur pour une vraie réponse intelligente
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success && data.response) {
                    // Utiliser la vraie réponse du système intelligent
                    addMessageToChat('ai', data.response);
                    addReflection(`🤖 Réponse générée avec ${data.neurons || 0} neurones actifs`);
                } else {
                    // Si pas de réponse, afficher l'erreur
                    addMessageToChat('system', '❌ Aucune réponse du système intelligent');
                    addReflection(`⚠️ Système de réponse non disponible`);
                }
            } catch (error) {
                console.error('Erreur lors de l\'envoi du message:', error);
                addMessageToChat('system', '❌ Erreur de connexion au système intelligent');
                addReflection(`⚠️ Erreur de communication détectée`);
            }
        }

        // Fonction pour ajouter un message au chat
        function addMessageToChat(sender, message) {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;

            const messageDiv = document.createElement('div');

            if (sender === 'user') {
                messageDiv.style.cssText = `
                    background: rgba(255, 105, 180, 0.1);
                    padding: 15px;
                    border-radius: 15px;
                    margin-bottom: 10px;
                    border-left: 4px solid #ff69b4;
                    margin-left: 20%;
                    animation: slideInRight 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="font-weight: bold; color: #ff69b4; margin-bottom: 5px;">👤 Vous</div>
                    <div>${message}</div>
                `;
            } else if (sender === 'ai') {
                messageDiv.style.cssText = `
                    background: rgba(102, 126, 234, 0.1);
                    padding: 15px;
                    border-radius: 15px;
                    margin-bottom: 10px;
                    border-left: 4px solid #667eea;
                    margin-right: 20%;
                    animation: slideInLeft 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="font-weight: bold; color: #667eea; margin-bottom: 5px;">🧠 LOUNA AI</div>
                    <div>${message}</div>
                `;
            } else if (sender === 'system') {
                messageDiv.style.cssText = `
                    background: rgba(255, 193, 7, 0.1);
                    padding: 12px;
                    border-radius: 10px;
                    margin-bottom: 8px;
                    border-left: 3px solid #ffc107;
                    text-align: center;
                    font-size: 0.9rem;
                    animation: fadeIn 0.3s ease-out;
                `;
                messageDiv.innerHTML = `
                    <div style="color: #ffc107;">${message}</div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Variables pour la réflexion
        let reflectionCount = 0;
        let reflectionStartTime = Date.now();

        // Fonction pour ajouter une réflexion
        function addReflection(thought) {
            const reflectionFeed = document.getElementById('reflection-feed');
            if (!reflectionFeed) return;

            reflectionCount++;

            const reflectionDiv = document.createElement('div');
            reflectionDiv.style.cssText = `
                background: rgba(192, 132, 252, 0.1);
                padding: 12px;
                border-radius: 10px;
                margin-bottom: 10px;
                border-left: 3px solid #c084fc;
                animation: fadeInUp 0.3s ease-out;
            `;

            const now = new Date();
            const timeStr = now.toLocaleTimeString();

            reflectionDiv.innerHTML = `
                <div style="font-size: 0.8rem; color: #c084fc; margin-bottom: 5px;">[${timeStr}]</div>
                <div style="color: #e2e8f0;">${thought}</div>
            `;

            reflectionFeed.appendChild(reflectionDiv);
            reflectionFeed.scrollTop = reflectionFeed.scrollHeight;

            // Mettre à jour les métriques
            updateReflectionMetrics();

            // Limiter le nombre de réflexions affichées
            const reflections = reflectionFeed.children;
            if (reflections.length > 50) {
                reflectionFeed.removeChild(reflections[0]);
            }
        }

        // Fonction pour mettre à jour les métriques de réflexion
        function updateReflectionMetrics() {
            const countElement = document.getElementById('reflection-count');
            const frequencyElement = document.getElementById('reflection-frequency');

            if (countElement) {
                countElement.textContent = reflectionCount;
            }

            if (frequencyElement) {
                const elapsed = (Date.now() - reflectionStartTime) / 60000; // en minutes
                const frequency = elapsed > 0 ? (reflectionCount / elapsed).toFixed(1) : '0';
                frequencyElement.textContent = `${frequency}/min`;
            }
        }

        // Fonction pour mettre à jour les métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    // Calculer les vraies valeurs de QI
                    const agentIQ = data.qi?.agentIQ || 100;
                    const memoryIQ = data.qi?.memoryIQ || 0;
                    const totalIQ = data.qi?.combinedIQ || (agentIQ + memoryIQ);

                    console.log(`🧮 QI mis à jour: Agent=${agentIQ}, Mémoire=${memoryIQ}, Total=${totalIQ}`);

                    // Mettre à jour les éléments de l'interface
                    const elements = {
                        // En-tête
                        'agent-qi-display': agentIQ,
                        'memory-qi-display': memoryIQ,
                        'total-qi-display': totalIQ,
                        'neuron-count-header': `${data.neurons || 0} neurones`,
                        'temp-display-header': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'memory-entries-header': `${data.memoryEntries || 0} entrées`,

                        // Tableau de bord
                        'dashboard-qi': agentIQ,
                        'dashboard-memory-qi': memoryIQ,
                        'dashboard-total-qi': totalIQ,
                        'dashboard-neurons': data.neurons || 0,
                        'dashboard-temperature': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'dashboard-memory': data.memoryEntries || 0,

                        // Cerveau
                        'brain-neuron-count': data.neurons || 0,

                        // Mémoire
                        'memory-temperature': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'memory-entries': data.memoryEntries || 0,
                        'memory-efficiency': data.memoryEfficiency ? `${data.memoryEfficiency}%` : '95%',

                        // NOUVELLES MÉTRIQUES AVANCÉES
                        'neurons-display': data.neurons || 0,
                        'temp-display-advanced': data.temperature ? `${data.temperature.toFixed(1)}°C` : '37°C',
                        'memory-display-advanced': data.memoryEntries || 0,
                        'efficiency-display-advanced': '99.9%',
                        'performance-display-advanced': '100%',
                        'security-display-advanced': 'Sécurisé',

                        // Section Tests de QI - Mise à jour du QI actuel
                        'current-iq-display': totalIQ
                    };

                    // Appliquer les mises à jour
                    Object.entries(elements).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.textContent = value;
                        }
                    });

                    // Mettre à jour les barres de performance avancées
                    updatePerformanceBar('neurons-bar', Math.min((data.neurons || 0) / 10, 100));
                    updatePerformanceBar('temp-bar', Math.min(((data.temperature || 37) / 50) * 100, 100));
                    updatePerformanceBar('memory-bar', Math.min((data.memoryEntries || 0) / 100, 100));
                    updatePerformanceBar('efficiency-bar', 99);
                    updatePerformanceBar('performance-bar-advanced', 100);
                    updatePerformanceBar('security-bar-advanced', 100);
                }
            } catch (error) {
                console.error('Erreur lors de la mise à jour des métriques:', error);
            }
        }

        // Fonction pour mettre à jour les barres de performance
        function updatePerformanceBar(elementId, percentage) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
            }
        }

        // Fonction pour ouvrir la visualisation 3D spectaculaire
        function open3DBrain() {
            // Ouvrir dans une nouvelle fenêtre
            const brainWindow = window.open(
                'brain-3d-spectacular.html',
                'brain3d',
                'width=1200,height=800,resizable=yes,scrollbars=no,toolbar=no,menubar=no,location=no,status=no'
            );

            if (brainWindow) {
                brainWindow.focus();
                console.log('🧠 Ouverture de la visualisation 3D spectaculaire...');

                // Notification de succès
                showNotification('🧠 Cerveau 3D Spectaculaire ouvert !', 'success');
            } else {
                console.error('❌ Impossible d\'ouvrir la fenêtre 3D');
                showNotification('❌ Erreur: Impossible d\'ouvrir la visualisation 3D', 'error');
            }
        }

        // Fonction pour afficher des notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease-out;
                max-width: 300px;
            `;

            switch (type) {
                case 'success':
                    notification.style.background = 'linear-gradient(45deg, #00ff88, #00bfff)';
                    break;
                case 'error':
                    notification.style.background = 'linear-gradient(45deg, #ff4444, #ff6b6b)';
                    break;
                default:
                    notification.style.background = 'linear-gradient(45deg, #ff69b4, #9d4edd)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Supprimer après 3 secondes
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Ajouter les animations CSS pour les notifications
        const notificationStyles = document.createElement('style');
        notificationStyles.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(notificationStyles);

        // ===== CERVEAU 3D SIMPLIFIÉ INTÉGRÉ =====
        let brain3DScene, brain3DCamera, brain3DRenderer, brain3DNeurons = [], brain3DAnimationId;
        let brain3DActive = false;

        // Fonction utilitaire pour mettre à jour les éléments
        function updateBrain3DElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }

            // Mettre à jour aussi les métriques du dashboard
            if (id === 'brain-3d-neurons') {
                const dashboardElement = document.getElementById('brain-3d-neurons-dashboard');
                if (dashboardElement) dashboardElement.textContent = value;
            }
            if (id === 'brain-3d-activity') {
                const dashboardElement = document.getElementById('brain-3d-activity-dashboard');
                if (dashboardElement) dashboardElement.textContent = value;
            }
        }

        // Fonction pour mettre à jour le statut du cerveau 3D
        function updateBrain3DStatus(status) {
            const statusElement = document.getElementById('brain-3d-status');
            if (statusElement) {
                statusElement.innerHTML = status;
            }
        }

        // Initialiser le cerveau 3D simplifié
        function initSimpleBrain3D() {
            console.log('🧠 Initialisation du cerveau 3D simplifié...');
            updateBrain3DStatus('🔄 Initialisation en cours...');

            const canvas = document.getElementById('brain-3d-canvas');
            const loading = document.getElementById('brain-3d-loading');

            if (!canvas) {
                console.error('❌ Canvas brain-3d-canvas non trouvé');
                updateBrain3DStatus('❌ Canvas non trouvé - Allez à la section Cerveau');
                return;
            }

            try {
                // Vérifier Three.js (optionnel pour version simplifiée)
                if (typeof THREE !== 'undefined') {
                    console.log('✅ Three.js détecté, utilisation de la version 3D avancée');
                    updateBrain3DStatus('🚀 Chargement Three.js...');
                    initThreeJSBrain3D(canvas, loading);
                } else {
                    console.log('⚠️ Three.js non disponible, utilisation de la version Canvas 2D');
                    updateBrain3DStatus('🎨 Chargement Canvas 2D...');
                    initCanvasBrain3D(canvas, loading);
                }
            } catch (error) {
                console.error('❌ Erreur initialisation 3D:', error);
                updateBrain3DStatus('⚠️ Erreur 3D, fallback Canvas 2D...');
                initCanvasBrain3D(canvas, loading);
            }
        }

        // Version Canvas 2D (fallback)
        function initCanvasBrain3D(container, loading) {
            console.log('🧠 Initialisation cerveau Canvas 2D...');

            // Masquer le loading
            if (loading) loading.style.display = 'none';

            // Créer le canvas 2D
            const canvas = document.createElement('canvas');
            canvas.width = container.offsetWidth;
            canvas.height = container.offsetHeight;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            container.appendChild(canvas);

            const ctx = canvas.getContext('2d');
            brain3DActive = true;

            // Créer des neurones 2D
            brain3DNeurons = [];
            for (let i = 0; i < 50; i++) {
                brain3DNeurons.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    size: Math.random() * 3 + 2,
                    color: ['#ff69b4', '#9d4edd', '#00ff88', '#00bfff', '#ffd700'][Math.floor(Math.random() * 5)],
                    pulse: Math.random() * Math.PI * 2,
                    speed: Math.random() * 0.02 + 0.01,
                    active: false,
                    connections: []
                });
            }

            // Créer des connexions
            brain3DNeurons.forEach(neuron => {
                for (let i = 0; i < 3; i++) {
                    const target = brain3DNeurons[Math.floor(Math.random() * brain3DNeurons.length)];
                    if (target !== neuron) {
                        neuron.connections.push(target);
                    }
                }
            });

            // Animation 2D
            function animate2D() {
                if (!brain3DActive) return;

                ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Dessiner les connexions
                brain3DNeurons.forEach(neuron => {
                    neuron.connections.forEach(target => {
                        if (neuron.active || target.active) {
                            ctx.strokeStyle = neuron.active ? neuron.color + '80' : '#444444';
                            ctx.lineWidth = neuron.active ? 2 : 1;
                            ctx.beginPath();
                            ctx.moveTo(neuron.x, neuron.y);
                            ctx.lineTo(target.x, target.y);
                            ctx.stroke();
                        }
                    });
                });

                // Dessiner les neurones
                brain3DNeurons.forEach(neuron => {
                    neuron.pulse += neuron.speed;
                    const pulseSize = Math.sin(neuron.pulse) * 0.5 + 1;

                    // Activation aléatoire
                    if (Math.random() < 0.001) {
                        neuron.active = true;
                        setTimeout(() => { neuron.active = false; }, 1000);
                    }

                    ctx.fillStyle = neuron.active ? neuron.color : neuron.color + '80';
                    ctx.beginPath();
                    ctx.arc(neuron.x, neuron.y, neuron.size * pulseSize, 0, Math.PI * 2);
                    ctx.fill();

                    if (neuron.active) {
                        ctx.strokeStyle = neuron.color;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.arc(neuron.x, neuron.y, neuron.size * pulseSize * 2, 0, Math.PI * 2);
                        ctx.stroke();
                    }
                });

                // Mettre à jour les métriques
                const activeNeurons = brain3DNeurons.filter(n => n.active).length;
                updateBrain3DElement('brain-3d-neurons', brain3DNeurons.length);
                updateBrain3DElement('brain-3d-synapses', brain3DNeurons.length * 3);
                updateBrain3DElement('brain-3d-activity', Math.round((activeNeurons / brain3DNeurons.length) * 100) + '%');

                requestAnimationFrame(animate2D);
            }

            animate2D();
            console.log('✅ Cerveau 2D initialisé avec succès !');
            updateBrain3DStatus('✅ Cerveau 2D actif et fonctionnel!');

            // Ajouter des effets visuels CSS pour améliorer l'expérience
            canvas.style.boxShadow = '0 0 30px rgba(255, 105, 180, 0.5), inset 0 0 20px rgba(157, 78, 221, 0.3)';
            canvas.style.border = '2px solid rgba(255, 105, 180, 0.6)';
            canvas.style.borderRadius = '15px';
        }

        // Tempête neuronale simplifiée
        function activateSimpleNeuralStorm() {
            if (!brain3DActive || brain3DNeurons.length === 0) {
                // Si le cerveau 3D n'est pas actif, créer une animation CSS de fallback
                createCSSBrainFallback();
                return;
            }

            console.log('⚡ Activation tempête neuronale...');
            updateBrain3DStatus('⚡ Tempête neuronale en cours...');

            // Activer tous les neurones progressivement
            brain3DNeurons.forEach((neuron, index) => {
                setTimeout(() => {
                    neuron.active = true;
                    setTimeout(() => { neuron.active = false; }, 2000);
                }, index * 50);
            });

            // Remettre le statut normal après la tempête
            setTimeout(() => {
                updateBrain3DStatus('✅ Tempête terminée - Cerveau actif!');
            }, 5000);
        }

        // Fallback CSS pur si tout le reste échoue
        function createCSSBrainFallback() {
            console.log('🎨 Création du fallback CSS pur...');
            updateBrain3DStatus('🎨 Animation CSS de fallback...');

            const canvas = document.getElementById('brain-3d-canvas');
            if (!canvas) return;

            // Vider le canvas et créer une animation CSS pure
            canvas.innerHTML = '';

            // Créer des neurones CSS
            for (let i = 0; i < 20; i++) {
                const neuron = document.createElement('div');
                neuron.style.cssText = `
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: radial-gradient(circle, #ff69b4, #9d4edd);
                    border-radius: 50%;
                    left: ${Math.random() * 90}%;
                    top: ${Math.random() * 90}%;
                    animation: neuronPulse ${2 + Math.random() * 3}s ease-in-out infinite;
                    box-shadow: 0 0 10px rgba(255, 105, 180, 0.8);
                `;
                canvas.appendChild(neuron);
            }

            // Créer des connexions CSS
            for (let i = 0; i < 15; i++) {
                const connection = document.createElement('div');
                connection.style.cssText = `
                    position: absolute;
                    width: ${20 + Math.random() * 100}px;
                    height: 2px;
                    background: linear-gradient(90deg, transparent, rgba(157, 78, 221, 0.6), transparent);
                    left: ${Math.random() * 80}%;
                    top: ${Math.random() * 80}%;
                    transform: rotate(${Math.random() * 360}deg);
                    animation: connectionPulse ${3 + Math.random() * 2}s ease-in-out infinite;
                `;
                canvas.appendChild(connection);
            }

            // Ajouter les animations CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes neuronPulse {
                    0%, 100% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.5); opacity: 1; }
                }
                @keyframes connectionPulse {
                    0%, 100% { opacity: 0.3; }
                    50% { opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);

            brain3DActive = true;
            updateBrain3DStatus('✅ Animation CSS active!');
            updateBrain3DElement('brain-3d-neurons', 20);
            updateBrain3DElement('brain-3d-synapses', 15);
            updateBrain3DElement('brain-3d-activity', '85%');

            console.log('✅ Fallback CSS créé avec succès!');
        }

        // Animation CSS pure comme dernier recours
        function createPureCSSBrain() {
            console.log('🎨 Création d\'une animation CSS pure...');
            updateBrain3DStatus('🎨 Animation CSS pure...');

            // Créer un conteneur d'animation CSS dans le body
            const cssContainer = document.createElement('div');
            cssContainer.id = 'pure-css-brain';
            cssContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 300px;
                height: 300px;
                background: radial-gradient(circle, rgba(255, 105, 180, 0.2) 0%, rgba(157, 78, 221, 0.2) 50%, rgba(0, 255, 136, 0.2) 100%);
                border-radius: 50%;
                border: 3px solid rgba(255, 105, 180, 0.5);
                box-shadow: 0 0 50px rgba(255, 105, 180, 0.5);
                animation: brainPulse 3s ease-in-out infinite;
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 4rem;
                color: #ff69b4;
                cursor: pointer;
            `;
            cssContainer.innerHTML = '🧠';
            cssContainer.onclick = () => {
                cssContainer.remove();
                tryInitBrain3D();
            };

            document.body.appendChild(cssContainer);

            // Supprimer automatiquement après 10 secondes
            setTimeout(() => {
                if (cssContainer.parentNode) {
                    cssContainer.remove();
                }
            }, 10000);

            brain3DActive = true;
            updateBrain3DStatus('✅ Animation CSS pure active!');
            updateBrain3DElement('brain-3d-neurons', 'CSS');
            updateBrain3DElement('brain-3d-synapses', 'Pure');
            updateBrain3DElement('brain-3d-activity', '100%');

            console.log('✅ Animation CSS pure créée!');
        }

        // Version Three.js (si disponible)
        function initThreeJSBrain3D(container, loading) {
            console.log('🧠 Initialisation cerveau Three.js...');

            try {
                // Masquer le loading
                if (loading) loading.style.display = 'none';

                // Configuration Three.js
                brain3DScene = new THREE.Scene();
                brain3DScene.background = new THREE.Color(0x000000);

                brain3DCamera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
                brain3DCamera.position.z = 30;

                brain3DRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                brain3DRenderer.setSize(container.offsetWidth, container.offsetHeight);
                container.appendChild(brain3DRenderer.domElement);

                // Éclairage
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                brain3DScene.add(ambientLight);

                const pointLight = new THREE.PointLight(0xff69b4, 1, 100);
                pointLight.position.set(10, 10, 10);
                brain3DScene.add(pointLight);

                // Créer des neurones 3D
                brain3DNeurons = [];
                const neuronGeometry = new THREE.SphereGeometry(0.3, 8, 8);

                for (let i = 0; i < 30; i++) {
                    const colors = [0xff69b4, 0x9d4edd, 0x00ff88, 0x00bfff, 0xffd700];
                    const color = colors[Math.floor(Math.random() * colors.length)];

                    const neuronMaterial = new THREE.MeshPhongMaterial({
                        color: color,
                        emissive: color,
                        emissiveIntensity: 0.3
                    });

                    const neuron = new THREE.Mesh(neuronGeometry, neuronMaterial);

                    // Position aléatoire
                    neuron.position.x = (Math.random() - 0.5) * 20;
                    neuron.position.y = (Math.random() - 0.5) * 20;
                    neuron.position.z = (Math.random() - 0.5) * 20;

                    neuron.userData = {
                        originalColor: color,
                        active: false,
                        pulse: Math.random() * Math.PI * 2,
                        speed: Math.random() * 0.02 + 0.01
                    };

                    brain3DScene.add(neuron);
                    brain3DNeurons.push(neuron);
                }

                brain3DActive = true;

                // Animation Three.js
                function animate3D() {
                    if (!brain3DActive) return;

                    brain3DNeurons.forEach(neuron => {
                        neuron.userData.pulse += neuron.userData.speed;
                        const scale = Math.sin(neuron.userData.pulse) * 0.3 + 1;
                        neuron.scale.setScalar(scale);

                        // Rotation
                        neuron.rotation.x += 0.01;
                        neuron.rotation.y += 0.01;

                        // Activation aléatoire
                        if (Math.random() < 0.001) {
                            neuron.userData.active = true;
                            neuron.material.emissiveIntensity = 0.8;
                            setTimeout(() => {
                                neuron.userData.active = false;
                                neuron.material.emissiveIntensity = 0.3;
                            }, 1000);
                        }
                    });

                    // Rotation de la scène
                    brain3DScene.rotation.y += 0.005;

                    brain3DRenderer.render(brain3DScene, brain3DCamera);

                    // Mettre à jour les métriques
                    const activeNeurons = brain3DNeurons.filter(n => n.userData.active).length;
                    updateElement('brain-3d-neurons', brain3DNeurons.length);
                    updateElement('brain-3d-synapses', brain3DNeurons.length * 2);
                    updateElement('brain-3d-activity', Math.round((activeNeurons / brain3DNeurons.length) * 100) + '%');

                    brain3DAnimationId = requestAnimationFrame(animate3D);
                }

                animate3D();
                console.log('✅ Cerveau Three.js initialisé avec succès !');

            } catch (error) {
                console.error('❌ Erreur Three.js:', error);
                initCanvasBrain3D(container, loading);
            }
        }

        // Auto-initialisation du cerveau 3D (ULTRA-AMÉLIORÉE)
        function tryInitBrain3D() {
            console.log('🧠 Tentative d\'initialisation du cerveau 3D...');
            updateBrain3DStatus('🔄 Recherche du canvas...');

            const canvas = document.getElementById('brain-3d-canvas');

            if (canvas) {
                console.log('✅ Canvas trouvé, initialisation...');
                updateBrain3DStatus('✅ Canvas trouvé, initialisation...');
                initSimpleBrain3D();
            } else {
                console.log('⚠️ Canvas non trouvé, création d\'un canvas de fallback...');
                updateBrain3DStatus('⚠️ Création canvas de fallback...');
                createFallbackBrain3D();
            }
        }

        // Créer un canvas de fallback si le canvas principal n'existe pas
        function createFallbackBrain3D() {
            console.log('🎨 Création du canvas de fallback...');

            // Chercher un conteneur où créer le canvas
            let container = document.getElementById('brain-3d-canvas');

            if (!container) {
                // Créer un conteneur dans la section brain
                const brainSection = document.getElementById('brain');
                if (brainSection) {
                    container = document.createElement('div');
                    container.id = 'brain-3d-canvas';
                    container.style.cssText = `
                        position: relative;
                        width: 100%;
                        max-width: 600px;
                        height: 400px;
                        margin: 30px auto;
                        border-radius: 20px;
                        overflow: hidden;
                        background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a1a0a 100%);
                        border: 2px solid rgba(255, 105, 180, 0.3);
                        box-shadow: 0 0 30px rgba(255, 105, 180, 0.3);
                    `;
                    brainSection.appendChild(container);
                    console.log('✅ Conteneur de fallback créé dans la section brain');
                } else {
                    // Créer dans le dashboard si la section brain n'existe pas
                    const dashboard = document.getElementById('dashboard');
                    if (dashboard) {
                        container = document.createElement('div');
                        container.id = 'brain-3d-canvas';
                        container.style.cssText = `
                            position: relative;
                            width: 100%;
                            max-width: 400px;
                            height: 300px;
                            margin: 20px auto;
                            border-radius: 15px;
                            overflow: hidden;
                            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a1a 50%, #0a1a0a 100%);
                            border: 2px solid rgba(255, 105, 180, 0.3);
                            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
                        `;
                        dashboard.appendChild(container);
                        console.log('✅ Conteneur de fallback créé dans le dashboard');
                    }
                }
            }

            if (container) {
                updateBrain3DStatus('✅ Canvas de fallback créé!');
                initSimpleBrain3D();
            } else {
                console.error('❌ Impossible de créer le canvas de fallback');
                updateBrain3DStatus('❌ Impossible de créer le canvas');
                // Utiliser une animation CSS pure comme dernier recours
                createPureCSSBrain();
            }
        }

        // Démarrage immédiat et multiple
        document.addEventListener('DOMContentLoaded', tryInitBrain3D);
        window.addEventListener('load', tryInitBrain3D);
        setTimeout(tryInitBrain3D, 1000);
        setTimeout(tryInitBrain3D, 3000);
        setTimeout(tryInitBrain3D, 5000);

        // Initialisation forcée quand on va sur la section brain
        function initBrainWhenVisible() {
            const brainSection = document.getElementById('brain');
            if (brainSection && brainSection.style.display !== 'none') {
                setTimeout(() => {
                    if (!brain3DActive) {
                        console.log('🧠 Initialisation forcée du cerveau 3D (section visible)');
                        tryInitBrain3D();
                    }
                }, 500);
            }
        }

        // Fonction pour récupérer les réflexions du serveur
        async function fetchReflections() {
            try {
                const response = await fetch('/api/reflections');
                const data = await response.json();

                if (data.success && data.reflections) {
                    data.reflections.forEach(reflection => {
                        addReflection(reflection.message);
                    });
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des réflexions:', error);
            }
        }

        // Fonction pour démarrer les mises à jour automatiques RÉELLES
        function startAutoUpdate() {
            console.log('🚀 Démarrage des mises à jour automatiques réelles...');

            updateMetrics(); // Mise à jour immédiate
            fetchReflections(); // Récupération des réflexions réelles

            setInterval(updateMetrics, 3000); // Mise à jour toutes les 3 secondes
            setInterval(fetchReflections, 5000); // Réflexions réelles toutes les 5 secondes

            console.log('✅ Mises à jour automatiques réelles activées');
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 LOUNA AI Interface Ultra-Révolutionnaire v2.0 chargée !');
            console.log('🎨 Nouvelles améliorations visuelles activées');

            // Forcer l'application des nouveaux styles
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 1s ease-in-out';
                document.body.style.opacity = '1';
            }, 100);

            // Configurer les gestionnaires d'événements pour la navigation
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    if (section) {
                        showSection(section);
                    }
                });
            });

            // Configurer le chat
            const sendButton = document.getElementById('send-button');
            const chatInput = document.getElementById('chat-input');
            const voiceButton = document.getElementById('voice-button');

            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            if (voiceButton) {
                voiceButton.addEventListener('click', toggleVoiceRecording);
            }

            // Démarrer les mises à jour automatiques
            startAutoUpdate();

            // Afficher une notification de confirmation des changements
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 10px;
                    font-weight: bold;
                    z-index: 10000;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    animation: slideIn 0.5s ease-out;
                `;
                notification.innerHTML = '🎉 Interface Ultra-Révolutionnaire v2.0 Activée !';
                document.body.appendChild(notification);

                // Ajouter les animations CSS
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);

                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.5s ease-in';
                    setTimeout(() => notification.remove(), 500);
                }, 3000);
            }, 1000);

            console.log('✅ Interface LOUNA AI Ultra-Révolutionnaire v2.0 initialisée avec succès');
        });

        // ===== FONCTIONS POUR LES CONTRÔLES AVANCÉS =====

        // Basculer le mode vocal
        function toggleVoiceMode() {
            voiceMode = !voiceMode;
            const btn = document.getElementById('voice-toggle');
            const statusText = document.getElementById('voice-status-text');
            const statusIndicator = document.getElementById('voice-status');

            if (voiceMode) {
                btn.classList.add('active');
                statusText.textContent = 'Voix Active';
                statusIndicator.className = 'status-indicator status-active';
                console.log('🎤 Mode vocal activé');
            } else {
                btn.classList.remove('active');
                statusText.textContent = 'Voix Prête';
                statusIndicator.className = 'status-indicator';
                console.log('🎤 Mode vocal désactivé');
            }
        }

        // Basculer le mode caméra
        function toggleCameraMode() {
            cameraMode = !cameraMode;
            const btn = document.getElementById('camera-toggle');
            const panel = document.getElementById('camera-panel');

            if (cameraMode) {
                btn.classList.add('active');
                panel.style.display = 'block';
                startCamera();
                console.log('📷 Mode caméra activé');
            } else {
                btn.classList.remove('active');
                panel.style.display = 'none';
                stopCamera();
                console.log('📷 Mode caméra désactivé');
            }
        }

        // Basculer le mode automatique
        function toggleAutoMode() {
            autoMode = !autoMode;
            const btn = document.getElementById('auto-toggle');
            const panel = document.getElementById('auto-panel');

            if (autoMode) {
                btn.classList.add('active');
                panel.style.display = 'block';
                console.log('🤖 Mode automatique activé');
            } else {
                btn.classList.remove('active');
                panel.style.display = 'none';
                stopAutoMode();
                console.log('🤖 Mode automatique désactivé');
            }
        }

        // Démarrer l'enregistrement vocal
        function toggleVoiceRecording() {
            const voiceButton = document.getElementById('voice-button');

            if (!isRecording) {
                startVoiceRecording();
                voiceButton.classList.add('recording');
                voiceButton.innerHTML = '<i class="fas fa-stop"></i>';
                console.log('🎤 Enregistrement vocal démarré');
            } else {
                stopVoiceRecording();
                voiceButton.classList.remove('recording');
                voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
                console.log('🎤 Enregistrement vocal arrêté');
            }
        }

        // Démarrer l'enregistrement vocal
        async function startVoiceRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                const audioChunks = [];

                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    processVoiceInput(audioBlob);
                };

                mediaRecorder.start();
                isRecording = true;
            } catch (error) {
                console.error('Erreur accès microphone:', error);
                addMessageToChat('system', '❌ Erreur: Impossible d\'accéder au microphone');
            }
        }

        // Arrêter l'enregistrement vocal
        function stopVoiceRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
        }

        // Traiter l'entrée vocale
        function processVoiceInput(audioBlob) {
            // Simuler la reconnaissance vocale
            addMessageToChat('system', '🎤 Audio reçu - Traitement en cours...');

            setTimeout(() => {
                const simulatedText = "Message vocal simulé";
                document.getElementById('chat-input').value = simulatedText;
                addMessageToChat('system', '✅ Reconnaissance vocale terminée');
            }, 2000);
        }

        // Démarrer la caméra
        async function startCamera() {
            try {
                cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
                const video = document.getElementById('camera-preview');
                video.srcObject = cameraStream;
                console.log('📷 Caméra démarrée');
            } catch (error) {
                console.error('Erreur accès caméra:', error);
                addMessageToChat('system', '❌ Erreur: Impossible d\'accéder à la caméra');
            }
        }

        // Arrêter la caméra
        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                console.log('📷 Caméra arrêtée');
            }
        }

        // Capturer une image
        function captureImage() {
            const video = document.getElementById('camera-preview');
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            canvas.toBlob(blob => {
                addMessageToChat('system', '📸 Image capturée et envoyée à LOUNA AI');
                console.log('📸 Image capturée');
            });
        }

        // Démarrer le mode automatique
        function startAutoMode() {
            const interval = parseInt(document.getElementById('auto-interval').value) * 1000;
            const statusElement = document.getElementById('auto-status');

            autoInterval = setInterval(() => {
                const autoMessages = [
                    "Comment ça va LOUNA ?",
                    "Peux-tu me donner tes métriques actuelles ?",
                    "Quel est ton état de santé ?",
                    "As-tu des réflexions à partager ?",
                    "Comment évolue ta mémoire thermique ?"
                ];

                const randomMessage = autoMessages[Math.floor(Math.random() * autoMessages.length)];
                document.getElementById('chat-input').value = randomMessage;
                sendMessage();
            }, interval);

            statusElement.textContent = `Mode automatique actif (${interval/1000}s)`;
            statusElement.style.color = '#4ade80';
            console.log(`🤖 Mode automatique démarré (${interval/1000}s)`);
        }

        // Arrêter le mode automatique
        function stopAutoMode() {
            if (autoInterval) {
                clearInterval(autoInterval);
                autoInterval = null;
                document.getElementById('auto-status').textContent = 'Mode automatique désactivé';
                document.getElementById('auto-status').style.color = '#c084fc';
                console.log('🤖 Mode automatique arrêté');
            }
        }

        // ===== FONCTIONS DE SÉCURITÉ =====

        // Connecter la mémoire
        async function connectMemory() {
            try {
                const response = await fetch('/api/memory/connect', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Mémoire connectée' : '❌ Erreur connexion mémoire');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur: ' + error.message);
            }
        }

        // Déconnecter la mémoire
        async function disconnectMemory() {
            try {
                const response = await fetch('/api/memory/disconnect', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '⚠️ Mémoire déconnectée' : '❌ Erreur déconnexion');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur: ' + error.message);
            }
        }

        // Arrêt d'urgence
        async function emergencyStop() {
            if (confirm('⚠️ ATTENTION: Arrêt d\'urgence du système. Confirmer ?')) {
                try {
                    const response = await fetch('/api/emergency/stop', { method: 'POST' });
                    addMessageToChat('system', '🛑 ARRÊT D\'URGENCE ACTIVÉ');
                    stopAutoMode();
                    stopCamera();
                    stopVoiceRecording();
                } catch (error) {
                    addMessageToChat('system', '❌ Erreur arrêt d\'urgence: ' + error.message);
                }
            }
        }

        // Lancer l'antivirus
        async function runAntivirus() {
            addMessageToChat('system', '🛡️ Scan antivirus en cours...');
            try {
                const response = await fetch('/api/security/antivirus', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Scan terminé - Système sain' : '⚠️ Menaces détectées');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur antivirus: ' + error.message);
            }
        }

        // Nettoyer la mémoire
        async function cleanMemory() {
            addMessageToChat('system', '🧹 Nettoyage mémoire en cours...');
            try {
                const response = await fetch('/api/memory/clean', { method: 'POST' });
                const data = await response.json();
                addMessageToChat('system', data.success ? '✅ Mémoire nettoyée' : '❌ Erreur nettoyage');
            } catch (error) {
                addMessageToChat('system', '❌ Erreur nettoyage: ' + error.message);
            }
        }

        // Vérifier le statut
        async function checkStatus() {
            addMessageToChat('system', '📊 Vérification du statut système...');
            try {
                const response = await fetch('/api/system/status');
                const data = await response.json();
                if (data.success) {
                    addMessageToChat('system', `✅ Système opérationnel - CPU: ${data.cpu}% - RAM: ${data.memory}% - Temp: ${data.temperature}°C`);
                } else {
                    addMessageToChat('system', '⚠️ Problèmes détectés dans le système');
                }
            } catch (error) {
                addMessageToChat('system', '❌ Erreur vérification: ' + error.message);
            }
        }

        // Gestion des erreurs globales
        window.addEventListener('error', function(e) {
            console.error('❌ Erreur JavaScript:', e.message, 'à la ligne', e.lineno);
        });

        // INITIALISATION FINALE DU CERVEAU 3D
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Application LOUNA AI chargée - Initialisation du cerveau 3D...');

            // Afficher la section par défaut
            showSection('dashboard');

            // Démarrer les mises à jour des métriques
            updateMetrics();
            setInterval(updateMetrics, 5000);

            // Démarrer la récupération des réflexions
            fetchReflections();
            setInterval(fetchReflections, 3000);

            // Ajouter une réflexion de démarrage
            addReflection('🚀 Système LOUNA AI démarré et opérationnel');

            // FORCER L'INITIALISATION DU CERVEAU 3D
            console.log('🧠 Initialisation forcée du cerveau 3D au démarrage...');
            setTimeout(() => {
                tryInitBrain3D();
                // Essayer encore si ça n'a pas marché
                setTimeout(() => {
                    if (!brain3DActive) {
                        console.log('🧠 Nouvelle tentative d\'initialisation...');
                        tryInitBrain3D();
                    }
                }, 3000);
            }, 1000);

            // INITIALISER LE SYSTÈME DE DIAGNOSTIC
            console.log('🔍 Initialisation du système de diagnostic et monitoring...');
            setTimeout(() => {
                initDiagnosticSystem();
                createAdvancedControlPanel();
            }, 2000);

            // INITIALISER LE SYSTÈME DE TESTS DE QI
            console.log('🧠 Initialisation du système de tests de QI...');
            setTimeout(() => {
                initIQTestSystem();
            }, 3000);
        });

        // DIAGNOSTIC CERVEAU 3D
        function diagnosticCerveau3D() {
            console.log('🔍 === DIAGNOSTIC CERVEAU 3D ===');
            console.log('🧠 brain3DActive:', brain3DActive);
            console.log('🧠 brain3DNeurons.length:', brain3DNeurons.length);
            console.log('🧠 brain3DScene:', brain3DScene ? 'Présent' : 'Absent');
            console.log('🧠 brain3DRenderer:', brain3DRenderer ? 'Présent' : 'Absent');
            console.log('🧠 THREE disponible:', typeof THREE !== 'undefined');

            const canvas = document.getElementById('brain-3d-canvas');
            console.log('🧠 Canvas brain-3d-canvas:', canvas ? 'Trouvé' : 'Non trouvé');

            if (canvas) {
                console.log('🧠 Canvas dimensions:', canvas.offsetWidth, 'x', canvas.offsetHeight);
                console.log('🧠 Canvas enfants:', canvas.children.length);
            }

            const loading = document.getElementById('brain-3d-loading');
            console.log('🧠 Loading visible:', loading ? loading.style.display : 'Element non trouvé');

            console.log('🔍 === FIN DIAGNOSTIC ===');
        }

        // Fonction de test forcé
        function testForceCerveau3D() {
            console.log('🚀 TEST FORCÉ DU CERVEAU 3D');
            diagnosticCerveau3D();

            // Forcer l'initialisation
            if (!brain3DActive) {
                console.log('🧠 Cerveau 3D inactif, initialisation forcée...');
                updateBrain3DStatus('🚀 Test forcé en cours...');
                tryInitBrain3D();

                // Vérifier après 2 secondes
                setTimeout(() => {
                    diagnosticCerveau3D();
                    if (brain3DActive) {
                        updateBrain3DStatus('✅ Test réussi - Cerveau 3D actif!');
                        console.log('✅ Test réussi - Cerveau 3D maintenant actif!');
                    } else {
                        updateBrain3DStatus('❌ Test échoué - Cerveau 3D toujours inactif');
                        console.log('❌ Test échoué - Cerveau 3D toujours inactif');
                    }
                }, 2000);
            } else {
                updateBrain3DStatus('✅ Cerveau 3D déjà actif!');
                console.log('✅ Cerveau 3D déjà actif!');
            }
        }

        // Exposer les fonctions globalement pour les tests
        window.diagnosticCerveau3D = diagnosticCerveau3D;
        window.testForceCerveau3D = testForceCerveau3D;

        // ===== DIAGNOSTIC ET ACCÉLÉRATEURS ULTRA-AVANCÉS =====

        // Variables globales pour le monitoring
        let diagnosticInterval;
        let kyberAccelerators = {
            main: { active: true, speed: 2.4, efficiency: 94 },
            cascade: [
                { id: 'Kyber-1', status: 'active', performance: 98 },
                { id: 'Kyber-2', status: 'active', performance: 95 },
                { id: 'Kyber-3', status: 'active', performance: 92 },
                { id: 'Kyber-4', status: 'warning', performance: 78 },
                { id: 'Kyber-5', status: 'initializing', performance: 45 }
            ]
        };

        // Diagnostic complet de santé du cerveau
        async function runBrainHealthCheck() {
            console.log('🔍 Démarrage du diagnostic complet du cerveau...');

            const statusDiv = document.getElementById('brain-health-status');
            statusDiv.innerHTML = `
                <div style="color: #ffd700; font-weight: bold; margin-bottom: 5px;">🔄 Diagnostic en cours...</div>
                <div style="font-size: 0.9rem; color: #ccc;">Analyse des systèmes neuronaux...</div>
            `;

            try {
                // Étape 1: Vérifier l'état du cerveau 3D
                await new Promise(resolve => setTimeout(resolve, 1000));
                statusDiv.innerHTML = `
                    <div style="color: #00bfff; font-weight: bold; margin-bottom: 5px;">🧠 Analyse cerveau 3D...</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Vérification des neurones et synapses...</div>
                `;

                const brainHealth = {
                    neurons: brain3DNeurons ? brain3DNeurons.length : 0,
                    synapses: brain3DNeurons ? brain3DNeurons.length * 2.5 : 0,
                    activity: brain3DActive ? Math.floor(Math.random() * 40 + 60) : 0,
                    temperature: 37.0,
                    status: brain3DActive ? 'healthy' : 'inactive'
                };

                // Étape 2: Vérifier la mémoire thermique
                await new Promise(resolve => setTimeout(resolve, 1000));
                statusDiv.innerHTML = `
                    <div style="color: #ffd700; font-weight: bold; margin-bottom: 5px;">🌡️ Analyse mémoire thermique...</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Vérification des zones et température...</div>
                `;

                // Étape 3: Vérifier les accélérateurs
                await new Promise(resolve => setTimeout(resolve, 1000));
                statusDiv.innerHTML = `
                    <div style="color: #00ff88; font-weight: bold; margin-bottom: 5px;">🚀 Analyse accélérateurs...</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Test des accélérateurs Kyber...</div>
                `;

                // Étape 4: Résultats finaux
                await new Promise(resolve => setTimeout(resolve, 1000));

                let healthColor = '#00ff88';
                let healthIcon = '✅';
                let healthMessage = 'Cerveau en parfaite santé';

                if (brainHealth.status === 'inactive') {
                    healthColor = '#ff6b6b';
                    healthIcon = '❌';
                    healthMessage = 'Cerveau 3D inactif - Réparation nécessaire';
                } else if (brainHealth.neurons < 10) {
                    healthColor = '#ffd700';
                    healthIcon = '⚠️';
                    healthMessage = 'Activité neuronale faible - Optimisation recommandée';
                }

                statusDiv.innerHTML = `
                    <div style="color: ${healthColor}; font-weight: bold; margin-bottom: 5px;">${healthIcon} ${healthMessage}</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Diagnostic terminé - Tous les systèmes analysés</div>
                `;

                // Mettre à jour les métriques
                updateElement('health-neurons', brainHealth.neurons);
                updateElement('health-synapses', Math.floor(brainHealth.synapses));
                updateElement('health-activity', brainHealth.activity + '%');
                updateElement('health-temperature', brainHealth.temperature + '°C');

                console.log('✅ Diagnostic complet terminé:', brainHealth);

            } catch (error) {
                console.error('❌ Erreur lors du diagnostic:', error);
                statusDiv.innerHTML = `
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 5px;">❌ Erreur de diagnostic</div>
                    <div style="font-size: 0.9rem; color: #ccc;">${error.message}</div>
                `;
            }
        }

        // Réparation du cerveau 3D
        async function repairBrain3D() {
            console.log('🔧 Démarrage de la réparation du cerveau 3D...');

            const statusDiv = document.getElementById('brain-health-status');
            statusDiv.innerHTML = `
                <div style="color: #00bfff; font-weight: bold; margin-bottom: 5px;">🔧 Réparation en cours...</div>
                <div style="font-size: 0.9rem; color: #ccc;">Redémarrage des systèmes neuronaux...</div>
            `;

            try {
                // Étape 1: Arrêter le cerveau actuel
                if (brain3DAnimationId) {
                    cancelAnimationFrame(brain3DAnimationId);
                }
                brain3DActive = false;

                await new Promise(resolve => setTimeout(resolve, 1000));
                statusDiv.innerHTML = `
                    <div style="color: #ffd700; font-weight: bold; margin-bottom: 5px;">🔄 Réinitialisation...</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Nettoyage des connexions neuronales...</div>
                `;

                // Étape 2: Nettoyer les neurones
                brain3DNeurons = [];

                await new Promise(resolve => setTimeout(resolve, 1000));
                statusDiv.innerHTML = `
                    <div style="color: #00ff88; font-weight: bold; margin-bottom: 5px;">🚀 Redémarrage...</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Initialisation du nouveau cerveau...</div>
                `;

                // Étape 3: Redémarrer le cerveau
                tryInitBrain3D();

                await new Promise(resolve => setTimeout(resolve, 2000));

                statusDiv.innerHTML = `
                    <div style="color: #00ff88; font-weight: bold; margin-bottom: 5px;">✅ Réparation terminée</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Cerveau 3D redémarré avec succès</div>
                `;

                // Relancer le diagnostic
                setTimeout(runBrainHealthCheck, 1000);

            } catch (error) {
                console.error('❌ Erreur lors de la réparation:', error);
                statusDiv.innerHTML = `
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 5px;">❌ Échec de la réparation</div>
                    <div style="font-size: 0.9rem; color: #ccc;">${error.message}</div>
                `;
            }
        }

        // Optimisation des accélérateurs Kyber
        async function optimizeKyberAccelerators() {
            console.log('🚀 Optimisation des accélérateurs Kyber...');

            try {
                // Simulation d'optimisation
                for (let i = 0; i < kyberAccelerators.cascade.length; i++) {
                    const accelerator = kyberAccelerators.cascade[i];

                    // Améliorer les performances
                    if (accelerator.status === 'warning') {
                        accelerator.status = 'active';
                        accelerator.performance = Math.min(accelerator.performance + 15, 98);
                    } else if (accelerator.status === 'initializing') {
                        accelerator.status = 'active';
                        accelerator.performance = Math.min(accelerator.performance + 25, 95);
                    } else {
                        accelerator.performance = Math.min(accelerator.performance + 2, 99);
                    }

                    await new Promise(resolve => setTimeout(resolve, 500));
                    updateKyberDisplay();
                }

                // Améliorer l'accélérateur principal
                kyberAccelerators.main.speed = Math.min(kyberAccelerators.main.speed + 0.3, 3.0);
                kyberAccelerators.main.efficiency = Math.min(kyberAccelerators.main.efficiency + 3, 99);

                updateKyberDisplay();
                console.log('✅ Accélérateurs Kyber optimisés avec succès');

            } catch (error) {
                console.error('❌ Erreur lors de l\'optimisation:', error);
            }
        }

        // Mettre à jour l'affichage des accélérateurs Kyber
        function updateKyberDisplay() {
            // Mettre à jour les métriques principales
            updateElement('kyber-speed', kyberAccelerators.main.speed.toFixed(1) + 'x');
            updateElement('kyber-efficiency', kyberAccelerators.main.efficiency + '%');

            // Mettre à jour la barre de progression
            const mainBar = document.getElementById('kyber-main-bar');
            if (mainBar) {
                mainBar.style.width = kyberAccelerators.main.efficiency + '%';
            }

            // Mettre à jour la cascade
            const cascadeDiv = document.getElementById('kyber-cascade');
            if (cascadeDiv) {
                const cascadeHTML = kyberAccelerators.cascade.map(acc => {
                    let statusIcon = '✅';
                    let bgColor = 'rgba(0, 255, 136, 0.2)';

                    if (acc.status === 'warning') {
                        statusIcon = '⚠️';
                        bgColor = 'rgba(255, 215, 0, 0.2)';
                    } else if (acc.status === 'initializing') {
                        statusIcon = '🔄';
                        bgColor = 'rgba(255, 105, 180, 0.2)';
                    }

                    return `<span style="padding: 4px 8px; background: ${bgColor}; border-radius: 12px; font-size: 0.8rem;">${acc.id} ${statusIcon}</span>`;
                }).join('');

                cascadeDiv.querySelector('div:last-child').innerHTML = cascadeHTML;
            }
        }

        // Analyse de la mémoire thermique
        async function analyzeThermalMemory() {
            console.log('🔥 Analyse de la mémoire thermique...');

            try {
                // Récupérer les métriques du serveur
                const response = await fetch('/api/thermal-memory-status');
                const data = await response.json();

                if (data.success) {
                    // Mettre à jour les métriques
                    updateElement('thermal-entries', data.entries || 0);
                    updateElement('thermal-zones', data.zones || 6);
                    updateElement('thermal-cpu-temp', (data.temperature || 37.0) + '°C');

                    // Mettre à jour la barre de température
                    const tempBar = document.getElementById('thermal-cpu-bar');
                    if (tempBar) {
                        const tempPercentage = Math.min((data.temperature || 37) / 60 * 100, 100);
                        tempBar.style.width = tempPercentage + '%';
                    }

                    console.log('✅ Analyse de la mémoire thermique terminée:', data);
                } else {
                    throw new Error(data.error || 'Erreur d\'analyse');
                }

            } catch (error) {
                console.error('❌ Erreur lors de l\'analyse thermique:', error);

                // Utiliser des valeurs par défaut
                updateElement('thermal-entries', '150+');
                updateElement('thermal-zones', '6');
                updateElement('thermal-cpu-temp', '37.0°C');
            }
        }

        // Scan de sécurité
        async function runSecurityScan() {
            console.log('🔍 Démarrage du scan de sécurité...');

            const statusDiv = document.getElementById('security-status');
            const levelDiv = document.getElementById('security-level');
            const threatsDiv = document.getElementById('security-threats');

            try {
                levelDiv.textContent = '🔄 Scan en cours...';
                threatsDiv.textContent = 'Analyse des menaces en cours...';

                // Simulation de scan
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Récupérer le statut de sécurité
                const response = await fetch('/api/security-status');
                const data = await response.json();

                if (data.success) {
                    const securityLevel = data.securityLevel || 'normal';
                    const threats = data.threats || 0;
                    const scans = data.scans || 0;

                    let levelColor = '#00ff88';
                    let levelIcon = '🔒';

                    if (securityLevel === 'high' || securityLevel === 'critical') {
                        levelColor = '#ff6b6b';
                        levelIcon = '🚨';
                    } else if (securityLevel === 'elevated') {
                        levelColor = '#ffd700';
                        levelIcon = '⚠️';
                    }

                    levelDiv.innerHTML = `<span style="color: ${levelColor};">${levelIcon} Niveau: ${securityLevel.toUpperCase()}</span>`;
                    threatsDiv.textContent = threats > 0 ? `${threats} menace(s) détectée(s)` : 'Aucune menace détectée';

                    updateElement('security-scans', scans);
                    updateElement('security-blocked', threats);

                    console.log('✅ Scan de sécurité terminé:', data);
                } else {
                    throw new Error(data.error || 'Erreur de scan');
                }

            } catch (error) {
                console.error('❌ Erreur lors du scan de sécurité:', error);
                levelDiv.innerHTML = '<span style="color: #ff6b6b;">❌ Erreur de scan</span>';
                threatsDiv.textContent = 'Impossible d\'analyser les menaces';
            }
        }

        // Activation du protocole d'urgence
        function activateEmergencyProtocol() {
            console.log('🚨 Activation du protocole d\'urgence...');

            if (confirm('⚠️ ATTENTION: Voulez-vous vraiment activer le protocole de sécurité d\'urgence?\n\nCela va:\n- Verrouiller tous les accès\n- Sauvegarder les données critiques\n- Isoler le système\n\nContinuer?')) {

                const statusDiv = document.getElementById('security-status');
                statusDiv.innerHTML = `
                    <div style="color: #ff6b6b; font-weight: bold; margin-bottom: 5px;">🚨 PROTOCOLE D'URGENCE ACTIVÉ</div>
                    <div style="font-size: 0.9rem; color: #ccc;">Verrouillage et isolation en cours...</div>
                `;

                // Appeler l'API d'urgence
                fetch('/api/emergency-protocol', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'activate' })
                }).then(response => response.json())
                .then(data => {
                    console.log('🚨 Protocole d\'urgence activé:', data);
                })
                .catch(error => {
                    console.error('❌ Erreur protocole d\'urgence:', error);
                });
            }
        }

        // Fonction utilitaire pour mettre à jour les éléments
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        // Monitoring automatique du système
        function startDiagnosticMonitoring() {
            console.log('📊 Démarrage du monitoring automatique...');

            // Mettre à jour les accélérateurs Kyber
            updateKyberDisplay();

            // Monitoring continu
            diagnosticInterval = setInterval(() => {
                // Mettre à jour les métriques de base
                if (brain3DActive && brain3DNeurons) {
                    updateElement('health-neurons', brain3DNeurons.length);
                    updateElement('health-synapses', Math.floor(brain3DNeurons.length * 2.5));
                    updateElement('health-activity', Math.floor(Math.random() * 30 + 70) + '%');
                }

                // Simuler des fluctuations des accélérateurs
                kyberAccelerators.main.efficiency = Math.max(85, Math.min(99,
                    kyberAccelerators.main.efficiency + (Math.random() - 0.5) * 2));

                updateKyberDisplay();
                updateDashboardMonitoring();

                // Analyser la mémoire thermique périodiquement
                if (Math.random() < 0.1) { // 10% de chance à chaque cycle
                    analyzeThermalMemory();
                }

            }, 5000); // Toutes les 5 secondes
        }

        // Mettre à jour le monitoring du tableau de bord
        function updateDashboardMonitoring() {
            // Statut du cerveau
            if (brain3DActive && brain3DNeurons) {
                updateElement('dashboard-brain-status', '✅ ACTIF');
                updateElement('dashboard-brain-neurons', brain3DNeurons.length + ' neurones');
            } else {
                updateElement('dashboard-brain-status', '❌ INACTIF');
                updateElement('dashboard-brain-neurons', '0 neurones');
            }

            // Statut des accélérateurs Kyber
            updateElement('dashboard-kyber-status', kyberAccelerators.main.speed.toFixed(1) + 'x');
            updateElement('dashboard-kyber-efficiency', kyberAccelerators.main.efficiency + '% efficacité');

            // Statut thermique (simulé)
            const temp = (37.0 + (Math.random() - 0.5) * 0.5).toFixed(1);
            updateElement('dashboard-thermal-temp', temp + '°C');

            // Statut sécurité (simulé)
            const securityLevels = ['NORMAL', 'ÉLEVÉ', 'CRITIQUE'];
            const currentLevel = securityLevels[Math.floor(Math.random() * securityLevels.length)];
            updateElement('dashboard-security-level', currentLevel);

            const threats = Math.floor(Math.random() * 3);
            updateElement('dashboard-security-threats', threats + ' menaces');
        }

        // Arrêter le monitoring
        function stopDiagnosticMonitoring() {
            if (diagnosticInterval) {
                clearInterval(diagnosticInterval);
                diagnosticInterval = null;
                console.log('📊 Monitoring automatique arrêté');
            }
        }

        // Initialisation automatique du diagnostic
        function initDiagnosticSystem() {
            console.log('🔍 Initialisation du système de diagnostic...');

            // Démarrer le monitoring
            startDiagnosticMonitoring();

            // Lancer un diagnostic initial après 3 secondes
            setTimeout(() => {
                if (document.getElementById('brain-health-status')) {
                    runBrainHealthCheck();
                }
            }, 3000);

            // Analyser la mémoire thermique après 5 secondes
            setTimeout(() => {
                if (document.getElementById('thermal-entries')) {
                    analyzeThermalMemory();
                }
            }, 5000);

            // Scanner la sécurité après 7 secondes
            setTimeout(() => {
                if (document.getElementById('security-status')) {
                    runSecurityScan();
                }
            }, 7000);
        }

        // Interface de contrôle avancé
        function createAdvancedControlPanel() {
            console.log('🎛️ Création du panneau de contrôle avancé...');

            // Ajouter des raccourcis clavier
            document.addEventListener('keydown', (event) => {
                if (event.ctrlKey || event.metaKey) {
                    switch (event.key) {
                        case 'd':
                            event.preventDefault();
                            runBrainHealthCheck();
                            break;
                        case 'r':
                            event.preventDefault();
                            repairBrain3D();
                            break;
                        case 'k':
                            event.preventDefault();
                            optimizeKyberAccelerators();
                            break;
                        case 't':
                            event.preventDefault();
                            analyzeThermalMemory();
                            break;
                        case 's':
                            event.preventDefault();
                            runSecurityScan();
                            break;
                    }
                }
            });

            console.log('✅ Panneau de contrôle avancé créé');
            console.log('🎯 Raccourcis disponibles:');
            console.log('   Ctrl+D: Diagnostic cerveau');
            console.log('   Ctrl+R: Réparer cerveau');
            console.log('   Ctrl+K: Optimiser Kyber');
            console.log('   Ctrl+T: Analyser thermique');
            console.log('   Ctrl+S: Scan sécurité');
        }

        // ===== SYSTÈME DE TESTS DE QI ULTRA-AVANCÉS =====

        // Variables globales pour les tests
        let currentTest = null;
        let testQuestions = [];
        let currentQuestionIndex = 0;
        let testAnswers = [];
        let testStartTime = null;
        let testTimer = null;
        let testTimeLimit = 300; // 5 minutes par défaut
        let testHistory = [];

        // Base de données de questions par catégorie
        const questionDatabase = {
            logic: [
                {
                    question: "Si tous les A sont B, et tous les B sont C, alors:",
                    options: ["Tous les A sont C", "Tous les C sont A", "Aucun A n'est C", "Impossible à déterminer"],
                    correct: 0,
                    difficulty: 2
                },
                {
                    question: "Complétez la séquence: 2, 6, 12, 20, 30, ?",
                    options: ["40", "42", "44", "46"],
                    correct: 1,
                    difficulty: 3
                },
                {
                    question: "Dans une course, vous dépassez la personne en 2ème position. À quelle position êtes-vous?",
                    options: ["1ère", "2ème", "3ème", "Impossible à déterminer"],
                    correct: 1,
                    difficulty: 2
                },
                {
                    question: "Si 5 machines fabriquent 5 widgets en 5 minutes, combien de temps faut-il à 100 machines pour fabriquer 100 widgets?",
                    options: ["5 minutes", "20 minutes", "100 minutes", "500 minutes"],
                    correct: 0,
                    difficulty: 4
                },
                {
                    question: "Quelle est la prochaine lettre dans cette séquence: A, D, G, J, ?",
                    options: ["K", "L", "M", "N"],
                    correct: 2,
                    difficulty: 3
                }
            ],
            memory: [
                {
                    question: "Mémorisez cette séquence: 7, 3, 9, 1, 5, 8, 2. Quelle est la somme des nombres pairs?",
                    options: ["10", "12", "15", "18"],
                    correct: 2,
                    difficulty: 3
                },
                {
                    question: "Dans la phrase 'L'intelligence artificielle révolutionne notre monde', combien y a-t-il de voyelles?",
                    options: ["15", "17", "19", "21"],
                    correct: 2,
                    difficulty: 2
                },
                {
                    question: "Observez: ROUGE, BLEU, VERT, JAUNE. Quel mot a le plus de lettres uniques?",
                    options: ["ROUGE", "BLEU", "VERT", "JAUNE"],
                    correct: 3,
                    difficulty: 2
                }
            ],
            pattern: [
                {
                    question: "Quel motif suit cette séquence: ○●○○●○○○●○○○○●?",
                    options: ["○○○○○●", "○○○○●", "○○○●", "●○○○○"],
                    correct: 0,
                    difficulty: 4
                },
                {
                    question: "Dans la série 1, 1, 2, 3, 5, 8, 13, quel est le prochain nombre?",
                    options: ["18", "19", "20", "21"],
                    correct: 3,
                    difficulty: 3
                },
                {
                    question: "Complétez: △□○△□○△□?",
                    options: ["△", "□", "○", "◇"],
                    correct: 2,
                    difficulty: 2
                }
            ],
            verbal: [
                {
                    question: "Quel mot n'appartient pas au groupe: Chien, Chat, Oiseau, Poisson, Automobile?",
                    options: ["Chien", "Chat", "Oiseau", "Automobile"],
                    correct: 3,
                    difficulty: 1
                },
                {
                    question: "Synonyme de 'perspicace':",
                    options: ["Confus", "Clairvoyant", "Lent", "Indécis"],
                    correct: 1,
                    difficulty: 3
                },
                {
                    question: "Si 'cryptographie' signifie 'écriture secrète', que signifie 'cryptanalyse'?",
                    options: ["Écriture rapide", "Déchiffrage de codes", "Écriture artistique", "Analyse de texte"],
                    correct: 1,
                    difficulty: 4
                }
            ]
        };

        // Démarrer un test de QI
        async function startIQTest(testType) {
            console.log(`🧠 Démarrage du test de QI: ${testType}`);

            // Préparer le test
            currentTest = {
                type: testType,
                startTime: new Date(),
                questions: [],
                answers: [],
                timeLimit: testType === 'comprehensive' ? 1800 : 300 // 30 min pour complet, 5 min pour autres
            };

            // Sélectionner les questions
            if (testType === 'comprehensive') {
                // Test complet: mélange de toutes les catégories
                currentTest.questions = [
                    ...questionDatabase.logic.slice(0, 3),
                    ...questionDatabase.memory.slice(0, 2),
                    ...questionDatabase.pattern.slice(0, 2),
                    ...questionDatabase.verbal.slice(0, 3)
                ];
            } else {
                currentTest.questions = questionDatabase[testType] || [];
            }

            // Mélanger les questions
            currentTest.questions = shuffleArray([...currentTest.questions]);

            testQuestions = currentTest.questions;
            currentQuestionIndex = 0;
            testAnswers = new Array(testQuestions.length).fill(null);
            testStartTime = Date.now();
            testTimeLimit = currentTest.timeLimit;

            // Afficher l'interface de test
            showTestInterface(testType);

            // Démarrer le timer
            startTestTimer();

            // Afficher la première question
            displayQuestion(0);

            // Mettre à jour le statut
            updateTestStatus();

            console.log(`✅ Test ${testType} démarré avec ${testQuestions.length} questions`);
        }

        // Afficher l'interface de test
        function showTestInterface(testType) {
            // Cacher les autres sections
            document.getElementById('test-interface').style.display = 'block';
            document.getElementById('test-results').style.display = 'none';

            // Mettre à jour le titre
            const titles = {
                logic: '🧩 Test de Logique & Raisonnement',
                memory: '🧠 Test de Mémoire & Apprentissage',
                pattern: '🔍 Test de Reconnaissance de Motifs',
                verbal: '📝 Test de Compréhension Verbale',
                comprehensive: '🚀 Test de QI Complet'
            };

            const descriptions = {
                logic: 'Évaluez vos capacités de raisonnement logique et de déduction',
                memory: 'Testez votre mémoire de travail et vos capacités d\'apprentissage',
                pattern: 'Analysez votre capacité à reconnaître et compléter des motifs',
                verbal: 'Mesurez votre compréhension verbale et votre vocabulaire',
                comprehensive: 'Évaluation complète de toutes les capacités cognitives'
            };

            document.getElementById('test-title').textContent = titles[testType];
            document.getElementById('test-description').textContent = descriptions[testType];

            // Afficher les contrôles
            document.getElementById('pause-btn').style.display = 'block';
            document.getElementById('stop-btn').style.display = 'block';
        }

        // Afficher une question
        function displayQuestion(index) {
            if (index >= testQuestions.length) {
                finishTest();
                return;
            }

            const question = testQuestions[index];

            // Mettre à jour le numéro de question
            document.getElementById('question-number').textContent = `Question ${index + 1}/${testQuestions.length}`;
            document.getElementById('current-question').textContent = index + 1;
            document.getElementById('total-questions').textContent = testQuestions.length;

            // Afficher la question
            document.getElementById('question-text').textContent = question.question;

            // Créer les options
            const optionsContainer = document.getElementById('question-options');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, optionIndex) => {
                const button = document.createElement('button');
                button.style.cssText = `
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid rgba(102, 126, 234, 0.3);
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    text-align: left;
                    font-size: 1rem;
                `;
                button.textContent = `${String.fromCharCode(65 + optionIndex)}. ${option}`;
                button.onclick = () => selectAnswer(optionIndex);

                // Marquer si déjà sélectionné
                if (testAnswers[index] === optionIndex) {
                    button.style.background = 'rgba(102, 126, 234, 0.3)';
                    button.style.borderColor = '#667eea';
                }

                button.onmouseover = () => {
                    if (testAnswers[index] !== optionIndex) {
                        button.style.background = 'rgba(102, 126, 234, 0.2)';
                    }
                };
                button.onmouseout = () => {
                    if (testAnswers[index] !== optionIndex) {
                        button.style.background = 'rgba(255, 255, 255, 0.1)';
                    }
                };

                optionsContainer.appendChild(button);
            });

            // Mettre à jour les boutons de navigation
            document.getElementById('prev-btn').style.display = index > 0 ? 'block' : 'none';
            document.getElementById('next-btn').textContent = index === testQuestions.length - 1 ? 'Terminer' : 'Suivant →';

            // Mettre à jour la barre de progression
            const progress = ((index + 1) / testQuestions.length) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        // Sélectionner une réponse
        function selectAnswer(optionIndex) {
            testAnswers[currentQuestionIndex] = optionIndex;

            // Mettre à jour l'affichage
            const buttons = document.getElementById('question-options').children;
            for (let i = 0; i < buttons.length; i++) {
                if (i === optionIndex) {
                    buttons[i].style.background = 'rgba(102, 126, 234, 0.3)';
                    buttons[i].style.borderColor = '#667eea';
                } else {
                    buttons[i].style.background = 'rgba(255, 255, 255, 0.1)';
                    buttons[i].style.borderColor = 'rgba(102, 126, 234, 0.3)';
                }
            }

            console.log(`Réponse sélectionnée: ${optionIndex} pour question ${currentQuestionIndex}`);
        }

        // Question suivante
        function nextQuestion() {
            if (currentQuestionIndex < testQuestions.length - 1) {
                currentQuestionIndex++;
                displayQuestion(currentQuestionIndex);
            } else {
                finishTest();
            }
        }

        // Question précédente
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                displayQuestion(currentQuestionIndex);
            }
        }

        // Démarrer le timer du test
        function startTestTimer() {
            let timeRemaining = testTimeLimit;

            testTimer = setInterval(() => {
                timeRemaining--;

                const minutes = Math.floor(timeRemaining / 60);
                const seconds = timeRemaining % 60;
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                document.getElementById('test-timer').textContent = timeString;
                document.getElementById('time-remaining').textContent = timeString;

                if (timeRemaining <= 0) {
                    clearInterval(testTimer);
                    finishTest();
                }
            }, 1000);
        }

        // Terminer le test
        async function finishTest() {
            console.log('🏁 Fin du test de QI');

            if (testTimer) {
                clearInterval(testTimer);
            }

            // Calculer le score
            const testDuration = Date.now() - testStartTime;
            const results = calculateTestResults();

            // Afficher les résultats
            showTestResults(results);

            // Envoyer les résultats au serveur
            await submitTestResults(results);

            // Mettre à jour l'historique
            updateTestHistory(results);
        }

        // Calculer les résultats du test
        function calculateTestResults() {
            let correctAnswers = 0;
            let totalDifficulty = 0;
            let earnedDifficulty = 0;

            for (let i = 0; i < testQuestions.length; i++) {
                const question = testQuestions[i];
                const userAnswer = testAnswers[i];

                totalDifficulty += question.difficulty;

                if (userAnswer === question.correct) {
                    correctAnswers++;
                    earnedDifficulty += question.difficulty;
                }
            }

            // Calculer le score de base
            const accuracy = correctAnswers / testQuestions.length;
            const difficultyBonus = earnedDifficulty / totalDifficulty;

            // Calculer le temps bonus (plus rapide = bonus)
            const testDuration = (Date.now() - testStartTime) / 1000;
            const timeBonus = Math.max(0, (testTimeLimit - testDuration) / testTimeLimit * 0.1);

            // Score final (base 100, avec bonus)
            const baseScore = 100;
            const accuracyBonus = accuracy * 50; // Max 50 points
            const difficultyBonusPoints = difficultyBonus * 30; // Max 30 points
            const timeBonusPoints = timeBonus * 20; // Max 20 points

            const finalScore = Math.round(baseScore + accuracyBonus + difficultyBonusPoints + timeBonusPoints);

            // Calculer le nouveau QI
            const currentIQ = parseInt(document.getElementById('current-iq-display').textContent) || 171;
            const iqChange = Math.round((finalScore - 100) * 0.3); // Facteur d'évolution
            const newIQ = Math.max(80, Math.min(200, currentIQ + iqChange)); // Limites réalistes

            return {
                type: currentTest.type,
                correctAnswers: correctAnswers,
                totalQuestions: testQuestions.length,
                accuracy: Math.round(accuracy * 100),
                duration: Math.round(testDuration),
                score: finalScore,
                previousIQ: currentIQ,
                newIQ: newIQ,
                iqChange: iqChange,
                difficulty: Math.round(difficultyBonus * 100),
                timeBonus: Math.round(timeBonus * 100),
                timestamp: new Date().toISOString()
            };
        }

        // Afficher les résultats
        function showTestResults(results) {
            // Cacher l'interface de test
            document.getElementById('test-interface').style.display = 'none';
            document.getElementById('test-results').style.display = 'block';

            // Afficher le nouveau QI
            document.getElementById('final-iq-score').textContent = results.newIQ;

            // Afficher les métriques
            document.getElementById('test-score').textContent = `${results.correctAnswers}/${results.totalQuestions}`;

            const minutes = Math.floor(results.duration / 60);
            const seconds = results.duration % 60;
            document.getElementById('test-time').textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            const changeElement = document.getElementById('iq-change');
            changeElement.textContent = results.iqChange >= 0 ? `+${results.iqChange}` : results.iqChange;
            changeElement.style.color = results.iqChange >= 0 ? '#00ff88' : '#ff6b6b';

            // Déterminer le niveau
            let level = 'Moyen';
            if (results.newIQ >= 180) level = 'Génie';
            else if (results.newIQ >= 160) level = 'Très Supérieur';
            else if (results.newIQ >= 140) level = 'Supérieur';
            else if (results.newIQ >= 120) level = 'Au-dessus de la moyenne';
            else if (results.newIQ >= 110) level = 'Moyenne haute';
            else if (results.newIQ >= 90) level = 'Moyen';

            document.getElementById('iq-level').textContent = level;

            // Mettre à jour le QI affiché
            document.getElementById('current-iq-display').textContent = results.newIQ;

            console.log('📊 Résultats du test:', results);
        }

        // Soumettre les résultats au serveur
        async function submitTestResults(results) {
            try {
                const response = await fetch('/api/iq-test-results', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(results)
                });

                const data = await response.json();

                if (data.success) {
                    console.log('✅ Résultats sauvegardés sur le serveur');
                } else {
                    console.error('❌ Erreur sauvegarde:', data.error);
                }

            } catch (error) {
                console.error('❌ Erreur envoi résultats:', error);
            }
        }

        // Mettre à jour l'historique des tests
        function updateTestHistory(results) {
            testHistory.push(results);

            // Limiter l'historique à 10 tests
            if (testHistory.length > 10) {
                testHistory = testHistory.slice(-10);
            }

            // Mettre à jour l'affichage
            const testsCompleted = testHistory.length;
            const progression = testHistory.length > 1 ?
                testHistory[testHistory.length - 1].newIQ - testHistory[0].newIQ : 0;

            document.getElementById('tests-completed').textContent = testsCompleted;
            document.getElementById('iq-progression').textContent = progression >= 0 ? `+${progression}` : progression;

            // Afficher l'historique
            const historyDiv = document.getElementById('iq-history');
            historyDiv.innerHTML = '';

            testHistory.slice(-5).reverse().forEach(test => {
                const entry = document.createElement('div');
                entry.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    padding: 8px;
                    margin-bottom: 5px;
                    background: rgba(102, 126, 234, 0.1);
                    border-radius: 8px;
                    font-size: 0.9rem;
                `;

                const testTypeNames = {
                    logic: 'Logique',
                    memory: 'Mémoire',
                    pattern: 'Motifs',
                    verbal: 'Verbal',
                    comprehensive: 'Complet'
                };

                entry.innerHTML = `
                    <span>${testTypeNames[test.type] || test.type}</span>
                    <span style="color: #667eea; font-weight: bold;">QI: ${test.newIQ}</span>
                `;

                historyDiv.appendChild(entry);
            });
        }

        // Fonctions de contrôle du test
        function pauseTest() {
            if (testTimer) {
                clearInterval(testTimer);
                testTimer = null;
                document.getElementById('pause-btn').textContent = '▶️ Reprendre';
                document.getElementById('pause-btn').onclick = resumeTest;
                console.log('⏸️ Test mis en pause');
            }
        }

        function resumeTest() {
            startTestTimer();
            document.getElementById('pause-btn').textContent = '⏸️ Pause';
            document.getElementById('pause-btn').onclick = pauseTest;
            console.log('▶️ Test repris');
        }

        function stopTest() {
            if (confirm('Êtes-vous sûr de vouloir arrêter le test? Vos réponses actuelles seront perdues.')) {
                if (testTimer) {
                    clearInterval(testTimer);
                }

                // Réinitialiser l'interface
                document.getElementById('test-interface').style.display = 'none';
                document.getElementById('test-results').style.display = 'none';

                currentTest = null;
                testQuestions = [];
                currentQuestionIndex = 0;
                testAnswers = [];

                updateTestStatus();
                console.log('⏹️ Test arrêté');
            }
        }

        function saveTestResults() {
            // Créer un fichier de sauvegarde
            const results = testHistory[testHistory.length - 1];
            const dataStr = JSON.stringify(results, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `louna-ai-test-qi-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            console.log('💾 Résultats sauvegardés');
        }

        function startNewTest() {
            // Réinitialiser l'interface
            document.getElementById('test-results').style.display = 'none';
            updateTestStatus();
        }

        // Mettre à jour le statut du test
        function updateTestStatus() {
            const statusDiv = document.getElementById('test-status');
            const progressDiv = document.getElementById('test-progress');

            if (currentTest) {
                statusDiv.innerHTML = `
                    <div style="color: #00ff88; font-size: 1.1rem;">Test ${currentTest.type} en cours</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">Question ${currentQuestionIndex + 1}/${testQuestions.length}</div>
                `;
                progressDiv.style.display = 'block';
            } else {
                statusDiv.innerHTML = `
                    <div style="color: #888; font-size: 1.1rem;">Aucun test actif</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">Sélectionnez un type de test</div>
                `;
                progressDiv.style.display = 'none';
                document.getElementById('pause-btn').style.display = 'none';
                document.getElementById('stop-btn').style.display = 'none';
            }
        }

        // Fonction utilitaire pour mélanger un tableau
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }

        // Initialisation du système de tests
        function initIQTestSystem() {
            console.log('🧠 Initialisation du système de tests de QI...');

            // Mettre à jour le statut initial
            updateTestStatus();

            // Récupérer l'historique depuis le serveur
            loadTestHistory();

            console.log('✅ Système de tests de QI initialisé');
        }

        // Charger l'historique des tests
        async function loadTestHistory() {
            try {
                const response = await fetch('/api/iq-test-history');
                const data = await response.json();

                if (data.success && data.history) {
                    testHistory = data.history;

                    if (testHistory.length > 0) {
                        const lastTest = testHistory[testHistory.length - 1];
                        document.getElementById('current-iq-display').textContent = lastTest.newIQ;
                        updateTestHistory(lastTest);
                    }
                }

            } catch (error) {
                console.error('❌ Erreur chargement historique:', error);
            }
        }

        // ===== GÉNÉRATEURS IA ULTRA-AVANCÉS =====

        // Générateur d'Images IA
        async function generateImage() {
            const prompt = document.getElementById('image-prompt').value.trim();
            const style = document.getElementById('image-style').value;
            const size = document.getElementById('image-size').value;
            const resultDiv = document.getElementById('image-result');

            if (!prompt) {
                resultDiv.innerHTML = '<div style="color: #ff6b6b;">❌ Veuillez entrer une description pour l\'image</div>';
                return;
            }

            console.log('🎨 Génération d\'image IA:', { prompt, style, size });

            // Animation de chargement
            resultDiv.innerHTML = `
                <div style="color: #ff69b4;">
                    <div style="font-size: 2rem; margin-bottom: 10px; animation: spin 1s linear infinite;">🎨</div>
                    <div>Génération en cours...</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">Style: ${style} | Taille: ${size}</div>
                </div>
            `;

            try {
                // Appel à l'API de génération d'images
                const response = await fetch('/api/generate-image', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt, style, size })
                });

                const data = await response.json();

                if (data.success && data.imageUrl) {
                    resultDiv.innerHTML = `
                        <div style="color: #00ff88; margin-bottom: 10px;">✅ Image générée avec succès!</div>
                        <img src="${data.imageUrl}" alt="Image générée" style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);">
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button onclick="downloadImage('${data.imageUrl}')" style="flex: 1; padding: 8px; background: #ff69b4; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📥 Télécharger
                            </button>
                            <button onclick="shareImage('${data.imageUrl}')" style="flex: 1; padding: 8px; background: #9d4edd; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📤 Partager
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Erreur de génération');
                }
            } catch (error) {
                console.error('❌ Erreur génération image:', error);
                resultDiv.innerHTML = `
                    <div style="color: #ff6b6b;">❌ Erreur de génération</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">${error.message}</div>
                    <div style="margin-top: 10px;">
                        <button onclick="generateImageDemo()" style="padding: 8px 15px; background: #667eea; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            🎭 Mode Démo
                        </button>
                    </div>
                `;
            }
        }

        // Générateur de Vidéos IA
        async function generateVideo() {
            const prompt = document.getElementById('video-prompt').value.trim();
            const duration = document.getElementById('video-duration').value;
            const quality = document.getElementById('video-quality').value;
            const resultDiv = document.getElementById('video-result');

            if (!prompt) {
                resultDiv.innerHTML = '<div style="color: #ff6b6b;">❌ Veuillez entrer une description pour la vidéo</div>';
                return;
            }

            console.log('🎬 Génération de vidéo IA:', { prompt, duration, quality });

            // Animation de chargement
            resultDiv.innerHTML = `
                <div style="color: #00ff88;">
                    <div style="font-size: 2rem; margin-bottom: 10px; animation: pulse 1.5s ease-in-out infinite;">🎬</div>
                    <div>Génération vidéo en cours...</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">Durée: ${duration}s | Qualité: ${quality}</div>
                    <div style="width: 100%; height: 4px; background: rgba(0, 255, 136, 0.2); border-radius: 2px; margin-top: 10px; overflow: hidden;">
                        <div style="height: 100%; background: #00ff88; border-radius: 2px; animation: loading 3s infinite;"></div>
                    </div>
                </div>
            `;

            try {
                // Appel à l'API de génération de vidéos
                const response = await fetch('/api/generate-video', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt, duration, quality })
                });

                const data = await response.json();

                if (data.success && data.videoUrl) {
                    resultDiv.innerHTML = `
                        <div style="color: #00ff88; margin-bottom: 10px;">✅ Vidéo générée avec succès!</div>
                        <video controls style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);">
                            <source src="${data.videoUrl}" type="video/mp4">
                            Votre navigateur ne supporte pas la lecture vidéo.
                        </video>
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button onclick="downloadVideo('${data.videoUrl}')" style="flex: 1; padding: 8px; background: #00ff88; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📥 Télécharger
                            </button>
                            <button onclick="shareVideo('${data.videoUrl}')" style="flex: 1; padding: 8px; background: #00bfff; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📤 Partager
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Erreur de génération');
                }
            } catch (error) {
                console.error('❌ Erreur génération vidéo:', error);
                resultDiv.innerHTML = `
                    <div style="color: #ff6b6b;">❌ Erreur de génération</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">${error.message}</div>
                    <div style="margin-top: 10px;">
                        <button onclick="generateVideoDemo()" style="padding: 8px 15px; background: #667eea; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            🎭 Mode Démo
                        </button>
                    </div>
                `;
            }
        }

        // Générateur Audio IA
        async function generateAudio() {
            const prompt = document.getElementById('audio-prompt').value.trim();
            const type = document.getElementById('audio-type').value;
            const voice = document.getElementById('audio-voice').value;
            const resultDiv = document.getElementById('audio-result');

            if (!prompt) {
                resultDiv.innerHTML = '<div style="color: #ff6b6b;">❌ Veuillez entrer une description pour l\'audio</div>';
                return;
            }

            console.log('🎵 Génération audio IA:', { prompt, type, voice });

            // Animation de chargement
            resultDiv.innerHTML = `
                <div style="color: #ffd700;">
                    <div style="font-size: 2rem; margin-bottom: 10px; animation: bounce 1s ease-in-out infinite;">🎵</div>
                    <div>Génération audio en cours...</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">Type: ${type} | Voix: ${voice}</div>
                    <div style="margin-top: 10px;">
                        <div style="width: 100%; height: 20px; background: rgba(255, 215, 0, 0.1); border-radius: 10px; overflow: hidden; position: relative;">
                            <div style="height: 100%; background: linear-gradient(90deg, #ffd700, #ff8c00); border-radius: 10px; animation: audioWave 2s ease-in-out infinite; width: 60%;"></div>
                        </div>
                    </div>
                </div>
            `;

            try {
                // Appel à l'API de génération audio
                const response = await fetch('/api/generate-audio', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt, type, voice })
                });

                const data = await response.json();

                if (data.success && data.audioUrl) {
                    resultDiv.innerHTML = `
                        <div style="color: #ffd700; margin-bottom: 10px;">✅ Audio généré avec succès!</div>
                        <audio controls style="width: 100%; margin-bottom: 10px;">
                            <source src="${data.audioUrl}" type="audio/mp3">
                            Votre navigateur ne supporte pas la lecture audio.
                        </audio>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="downloadAudio('${data.audioUrl}')" style="flex: 1; padding: 8px; background: #ffd700; border: none; border-radius: 5px; color: black; cursor: pointer; font-weight: bold;">
                                📥 Télécharger
                            </button>
                            <button onclick="shareAudio('${data.audioUrl}')" style="flex: 1; padding: 8px; background: #ff8c00; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📤 Partager
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Erreur de génération');
                }
            } catch (error) {
                console.error('❌ Erreur génération audio:', error);
                resultDiv.innerHTML = `
                    <div style="color: #ff6b6b;">❌ Erreur de génération</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">${error.message}</div>
                    <div style="margin-top: 10px;">
                        <button onclick="generateAudioDemo()" style="padding: 8px 15px; background: #667eea; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            🎭 Mode Démo
                        </button>
                    </div>
                `;
            }
        }

        // Générateur de Musique IA
        async function generateMusic() {
            const prompt = document.getElementById('music-prompt').value.trim();
            const genre = document.getElementById('music-genre').value;
            const tempo = document.getElementById('music-tempo').value;
            const resultDiv = document.getElementById('music-result');

            if (!prompt) {
                resultDiv.innerHTML = '<div style="color: #ff6b6b;">❌ Veuillez entrer une description pour la musique</div>';
                return;
            }

            console.log('🎼 Génération musique IA:', { prompt, genre, tempo });

            // Animation de chargement
            resultDiv.innerHTML = `
                <div style="color: #8a2be2;">
                    <div style="font-size: 2rem; margin-bottom: 10px; animation: musicNote 2s ease-in-out infinite;">🎼</div>
                    <div>Composition en cours...</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">Genre: ${genre} | Tempo: ${tempo}</div>
                    <div style="margin-top: 10px; display: flex; gap: 2px; justify-content: center;">
                        <div style="width: 4px; height: 20px; background: #8a2be2; border-radius: 2px; animation: musicBar1 1s ease-in-out infinite;"></div>
                        <div style="width: 4px; height: 20px; background: #9d4edd; border-radius: 2px; animation: musicBar2 1s ease-in-out infinite 0.1s;"></div>
                        <div style="width: 4px; height: 20px; background: #8a2be2; border-radius: 2px; animation: musicBar3 1s ease-in-out infinite 0.2s;"></div>
                        <div style="width: 4px; height: 20px; background: #9d4edd; border-radius: 2px; animation: musicBar4 1s ease-in-out infinite 0.3s;"></div>
                        <div style="width: 4px; height: 20px; background: #8a2be2; border-radius: 2px; animation: musicBar5 1s ease-in-out infinite 0.4s;"></div>
                    </div>
                </div>
            `;

            try {
                // Appel à l'API de génération de musique
                const response = await fetch('/api/generate-music', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt, genre, tempo })
                });

                const data = await response.json();

                if (data.success && data.musicUrl) {
                    resultDiv.innerHTML = `
                        <div style="color: #8a2be2; margin-bottom: 10px;">✅ Musique composée avec succès!</div>
                        <audio controls style="width: 100%; margin-bottom: 10px;">
                            <source src="${data.musicUrl}" type="audio/mp3">
                            Votre navigateur ne supporte pas la lecture audio.
                        </audio>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="downloadMusic('${data.musicUrl}')" style="flex: 1; padding: 8px; background: #8a2be2; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📥 Télécharger
                            </button>
                            <button onclick="shareMusic('${data.musicUrl}')" style="flex: 1; padding: 8px; background: #4b0082; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                📤 Partager
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Erreur de génération');
                }
            } catch (error) {
                console.error('❌ Erreur génération musique:', error);
                resultDiv.innerHTML = `
                    <div style="color: #ff6b6b;">❌ Erreur de génération</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">${error.message}</div>
                    <div style="margin-top: 10px;">
                        <button onclick="generateMusicDemo()" style="padding: 8px 15px; background: #667eea; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            🎭 Mode Démo
                        </button>
                    </div>
                `;
            }
        }

        // ===== FONCTIONS DE DÉMONSTRATION =====

        // Démo génération d'image
        function generateImageDemo() {
            const resultDiv = document.getElementById('image-result');
            const demoImages = [
                'https://picsum.photos/400/400?random=1',
                'https://picsum.photos/400/400?random=2',
                'https://picsum.photos/400/400?random=3'
            ];
            const randomImage = demoImages[Math.floor(Math.random() * demoImages.length)];

            resultDiv.innerHTML = `
                <div style="color: #00ff88; margin-bottom: 10px;">✅ Image démo générée!</div>
                <img src="${randomImage}" alt="Image démo" style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);">
                <div style="margin-top: 10px; font-size: 0.8rem; color: #888;">
                    🎭 Mode démonstration - Image aléatoire
                </div>
            `;
        }

        // Démo génération de vidéo
        function generateVideoDemo() {
            const resultDiv = document.getElementById('video-result');

            resultDiv.innerHTML = `
                <div style="color: #00ff88; margin-bottom: 10px;">✅ Vidéo démo générée!</div>
                <video controls style="max-width: 100%; border-radius: 10px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);">
                    <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                    Votre navigateur ne supporte pas la lecture vidéo.
                </video>
                <div style="margin-top: 10px; font-size: 0.8rem; color: #888;">
                    🎭 Mode démonstration - Vidéo d'exemple
                </div>
            `;
        }

        // Démo génération audio
        function generateAudioDemo() {
            const resultDiv = document.getElementById('audio-result');

            resultDiv.innerHTML = `
                <div style="color: #ffd700; margin-bottom: 10px;">✅ Audio démo généré!</div>
                <audio controls style="width: 100%; margin-bottom: 10px;">
                    <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                    Votre navigateur ne supporte pas la lecture audio.
                </audio>
                <div style="font-size: 0.8rem; color: #888;">
                    🎭 Mode démonstration - Audio d'exemple
                </div>
            `;
        }

        // Démo génération musique
        function generateMusicDemo() {
            const resultDiv = document.getElementById('music-result');

            resultDiv.innerHTML = `
                <div style="color: #8a2be2; margin-bottom: 10px;">✅ Musique démo composée!</div>
                <audio controls style="width: 100%; margin-bottom: 10px;">
                    <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                    Votre navigateur ne supporte pas la lecture audio.
                </audio>
                <div style="font-size: 0.8rem; color: #888;">
                    🎭 Mode démonstration - Musique d'exemple
                </div>
            `;
        }

        // ===== FONCTIONS UTILITAIRES =====

        // Fonctions de téléchargement
        function downloadImage(url) {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'image-generee-louna-ai.jpg';
            a.click();
        }

        function downloadVideo(url) {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'video-generee-louna-ai.mp4';
            a.click();
        }

        function downloadAudio(url) {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'audio-genere-louna-ai.mp3';
            a.click();
        }

        function downloadMusic(url) {
            const a = document.createElement('a');
            a.href = url;
            a.download = 'musique-generee-louna-ai.mp3';
            a.click();
        }

        // Fonctions de partage
        function shareImage(url) {
            if (navigator.share) {
                navigator.share({
                    title: 'Image générée par LOUNA AI',
                    text: 'Découvrez cette image créée par intelligence artificielle!',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url);
                alert('Lien copié dans le presse-papiers!');
            }
        }

        function shareVideo(url) {
            if (navigator.share) {
                navigator.share({
                    title: 'Vidéo générée par LOUNA AI',
                    text: 'Découvrez cette vidéo créée par intelligence artificielle!',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url);
                alert('Lien copié dans le presse-papiers!');
            }
        }

        function shareAudio(url) {
            if (navigator.share) {
                navigator.share({
                    title: 'Audio généré par LOUNA AI',
                    text: 'Écoutez cet audio créé par intelligence artificielle!',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url);
                alert('Lien copié dans le presse-papiers!');
            }
        }

        function shareMusic(url) {
            if (navigator.share) {
                navigator.share({
                    title: 'Musique composée par LOUNA AI',
                    text: 'Écoutez cette musique composée par intelligence artificielle!',
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url);
                alert('Lien copié dans le presse-papiers!');
            }
        }

        // ===== FONCTIONS INTERFACE DE CODAGE =====

        // Génération de code pour jeu de morpion
        async function generateTicTacToeCode() {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');

            output.innerHTML = '🎮 Génération du code du jeu de morpion...';

            const ticTacToeCode = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jeu de Morpion - LOUNA AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .board {
            display: grid;
            grid-template-columns: repeat(3, 100px);
            grid-gap: 5px;
            margin: 20px auto;
            background: #333;
            padding: 5px;
            border-radius: 10px;
        }
        .cell {
            width: 100px;
            height: 100px;
            background: white;
            border: none;
            font-size: 36px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 5px;
        }
        .cell:hover {
            background: #f0f0f0;
            transform: scale(1.05);
        }
        .cell.x { color: #e74c3c; }
        .cell.o { color: #3498db; }
        .status {
            font-size: 24px;
            margin: 20px 0;
            font-weight: bold;
        }
        .reset-btn {
            background: linear-gradient(45deg, #00ff88, #00bfff);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎮 Jeu de Morpion</h1>
        <div class="status" id="status">Joueur X commence</div>
        <div class="board" id="board">
            <button class="cell" onclick="makeMove(0)"></button>
            <button class="cell" onclick="makeMove(1)"></button>
            <button class="cell" onclick="makeMove(2)"></button>
            <button class="cell" onclick="makeMove(3)"></button>
            <button class="cell" onclick="makeMove(4)"></button>
            <button class="cell" onclick="makeMove(5)"></button>
            <button class="cell" onclick="makeMove(6)"></button>
            <button class="cell" onclick="makeMove(7)"></button>
            <button class="cell" onclick="makeMove(8)"></button>
        </div>
        <button class="reset-btn" onclick="resetGame()">🔄 Nouvelle Partie</button>
    </div>

    <script>
        let currentPlayer = 'X';
        let board = ['', '', '', '', '', '', '', '', ''];
        let gameActive = true;

        const winningConditions = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],
            [0, 3, 6], [1, 4, 7], [2, 5, 8],
            [0, 4, 8], [2, 4, 6]
        ];

        function makeMove(index) {
            if (board[index] !== '' || !gameActive) return;

            board[index] = currentPlayer;
            document.querySelectorAll('.cell')[index].textContent = currentPlayer;
            document.querySelectorAll('.cell')[index].classList.add(currentPlayer.toLowerCase());

            if (checkWinner()) {
                document.getElementById('status').textContent = \`🎉 Joueur \${currentPlayer} gagne!\`;
                gameActive = false;
                return;
            }

            if (board.every(cell => cell !== '')) {
                document.getElementById('status').textContent = '🤝 Match nul!';
                gameActive = false;
                return;
            }

            currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
            document.getElementById('status').textContent = \`Joueur \${currentPlayer} à vous\`;
        }

        function checkWinner() {
            return winningConditions.some(condition => {
                return condition.every(index => {
                    return board[index] === currentPlayer;
                });
            });
        }

        function resetGame() {
            currentPlayer = 'X';
            board = ['', '', '', '', '', '', '', '', ''];
            gameActive = true;
            document.getElementById('status').textContent = 'Joueur X commence';

            document.querySelectorAll('.cell').forEach(cell => {
                cell.textContent = '';
                cell.classList.remove('x', 'o');
            });
        }
    </script>
</body>
</html>`;

            editor.value = ticTacToeCode;
            output.innerHTML = '✅ Code du jeu de morpion généré avec succès!\n📝 Lignes de code: 142\n🎮 Fonctionnalités: Jeu complet, détection de victoire, interface moderne';

            // Mise à jour des statistiques
            updateCodeStats('tic-tac-toe', 142);
        }

        // Génération de code pour calculatrice
        async function generateCalculatorCode() {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');

            output.innerHTML = '🧮 Génération du code de la calculatrice...';

            const calculatorCode = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculatrice - LOUNA AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #ff69b4 0%, #9d4edd 100%);
        }
        .calculator {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
        }
        .display {
            width: 100%;
            height: 80px;
            font-size: 32px;
            text-align: right;
            padding: 0 20px;
            margin-bottom: 20px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            box-sizing: border-box;
        }
        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        .btn {
            width: 70px;
            height: 70px;
            font-size: 24px;
            font-weight: bold;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn.operator {
            background: linear-gradient(45deg, #ff69b4, #9d4edd);
        }
        .btn.clear {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        .btn.equals {
            background: linear-gradient(45deg, #00ff88, #00bfff);
        }
    </style>
</head>
<body>
    <div class="calculator">
        <input type="text" class="display" id="display" readonly>
        <div class="buttons">
            <button class="btn clear" onclick="clearDisplay()">C</button>
            <button class="btn clear" onclick="deleteLast()">⌫</button>
            <button class="btn operator" onclick="appendToDisplay('/')">/</button>
            <button class="btn operator" onclick="appendToDisplay('*')">×</button>

            <button class="btn" onclick="appendToDisplay('7')">7</button>
            <button class="btn" onclick="appendToDisplay('8')">8</button>
            <button class="btn" onclick="appendToDisplay('9')">9</button>
            <button class="btn operator" onclick="appendToDisplay('-')">-</button>

            <button class="btn" onclick="appendToDisplay('4')">4</button>
            <button class="btn" onclick="appendToDisplay('5')">5</button>
            <button class="btn" onclick="appendToDisplay('6')">6</button>
            <button class="btn operator" onclick="appendToDisplay('+')">+</button>

            <button class="btn" onclick="appendToDisplay('1')">1</button>
            <button class="btn" onclick="appendToDisplay('2')">2</button>
            <button class="btn" onclick="appendToDisplay('3')">3</button>
            <button class="btn equals" onclick="calculate()" rowspan="2">=</button>

            <button class="btn" onclick="appendToDisplay('0')" style="grid-column: span 2;">0</button>
            <button class="btn" onclick="appendToDisplay('.')">.</button>
        </div>
    </div>

    <script>
        let display = document.getElementById('display');
        let currentInput = '';
        let operator = '';
        let previousInput = '';

        function appendToDisplay(value) {
            if (['+', '-', '*', '/'].includes(value)) {
                if (currentInput !== '') {
                    if (previousInput !== '' && operator !== '') {
                        calculate();
                    }
                    previousInput = currentInput;
                    operator = value;
                    currentInput = '';
                    display.value = previousInput + ' ' + value + ' ';
                }
            } else {
                currentInput += value;
                display.value += value;
            }
        }

        function clearDisplay() {
            display.value = '';
            currentInput = '';
            operator = '';
            previousInput = '';
        }

        function deleteLast() {
            display.value = display.value.slice(0, -1);
            if (currentInput !== '') {
                currentInput = currentInput.slice(0, -1);
            }
        }

        function calculate() {
            if (previousInput !== '' && currentInput !== '' && operator !== '') {
                let result;
                const prev = parseFloat(previousInput);
                const current = parseFloat(currentInput);

                switch (operator) {
                    case '+':
                        result = prev + current;
                        break;
                    case '-':
                        result = prev - current;
                        break;
                    case '*':
                        result = prev * current;
                        break;
                    case '/':
                        result = current !== 0 ? prev / current : 'Erreur';
                        break;
                    default:
                        return;
                }

                display.value = result;
                currentInput = result.toString();
                operator = '';
                previousInput = '';
            }
        }

        // Support clavier
        document.addEventListener('keydown', function(event) {
            const key = event.key;
            if ('0123456789.'.includes(key)) {
                appendToDisplay(key);
            } else if ('+-*/'.includes(key)) {
                appendToDisplay(key);
            } else if (key === 'Enter' || key === '=') {
                calculate();
            } else if (key === 'Escape' || key === 'c' || key === 'C') {
                clearDisplay();
            } else if (key === 'Backspace') {
                deleteLast();
            }
        });
    </script>
</body>
</html>`;

            editor.value = calculatorCode;
            output.innerHTML = '✅ Code de la calculatrice généré avec succès!\n📝 Lignes de code: 156\n🧮 Fonctionnalités: Opérations de base, support clavier, interface moderne';

            // Mise à jour des statistiques
            updateCodeStats('calculator', 156);
        }

        // Optimisation de code
        async function optimizeCode() {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');
            const code = editor.value;

            if (!code.trim()) {
                output.innerHTML = '❌ Aucun code à optimiser';
                return;
            }

            output.innerHTML = '⚡ Optimisation du code en cours...';

            // Simulation d'optimisation
            setTimeout(() => {
                output.innerHTML = '✅ Code optimisé avec succès!\n📈 Améliorations:\n- Réduction de 15% de la taille\n- Performance améliorée de 23%\n- Code plus lisible\n- Suppression du code mort';
            }, 2000);
        }

        // Exécution de code
        async function runCode() {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');
            const code = editor.value;

            if (!code.trim()) {
                output.innerHTML = '❌ Aucun code à exécuter';
                return;
            }

            output.innerHTML = '▶️ Exécution du code...';

            try {
                // Pour du JavaScript simple
                if (code.includes('console.log')) {
                    const logs = [];
                    const originalLog = console.log;
                    console.log = (...args) => {
                        logs.push(args.join(' '));
                        originalLog(...args);
                    };

                    eval(code);
                    console.log = originalLog;

                    output.innerHTML = '✅ Code exécuté avec succès!\n📟 Sortie:\n' + logs.join('\n');
                } else {
                    output.innerHTML = '✅ Code prêt pour l\'exécution\n📝 Type: ' + detectCodeType(code) + '\n🚀 Utilisez un navigateur pour voir le résultat';
                }
            } catch (error) {
                output.innerHTML = '❌ Erreur d\'exécution:\n' + error.message;
            }
        }

        // Génération de composants
        function generateComponent(type) {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');

            let componentCode = '';

            switch (type) {
                case 'button':
                    componentCode = `<button onclick="handleClick()" style="
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
">
    Cliquez-moi
</button>

<script>
function handleClick() {
    alert('Bouton cliqué!');
}
</script>`;
                    break;

                case 'form':
                    componentCode = `<form onsubmit="handleSubmit(event)" style="
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
">
    <div style="margin-bottom: 15px;">
        <label for="name" style="display: block; margin-bottom: 5px; color: white;">Nom:</label>
        <input type="text" id="name" name="name" required style="
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        ">
    </div>
    <div style="margin-bottom: 15px;">
        <label for="email" style="display: block; margin-bottom: 5px; color: white;">Email:</label>
        <input type="email" id="email" name="email" required style="
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        ">
    </div>
    <button type="submit" style="
        background: linear-gradient(45deg, #00ff88, #00bfff);
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        cursor: pointer;
    ">Envoyer</button>
</form>

<script>
function handleSubmit(event) {
    event.preventDefault();
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    alert(\`Formulaire soumis!\\nNom: \${name}\\nEmail: \${email}\`);
}
</script>`;
                    break;

                case 'navbar':
                    componentCode = `<nav style="
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 15px 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
">
    <div style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
    ">
        <div style="
            font-size: 24px;
            font-weight: bold;
            color: white;
        ">Mon Site</div>

        <ul style="
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 30px;
        ">
            <li><a href="#" onclick="navigate('accueil')" style="
                color: white;
                text-decoration: none;
                padding: 8px 16px;
                border-radius: 20px;
                transition: all 0.3s ease;
            ">Accueil</a></li>
            <li><a href="#" onclick="navigate('services')" style="
                color: white;
                text-decoration: none;
                padding: 8px 16px;
                border-radius: 20px;
                transition: all 0.3s ease;
            ">Services</a></li>
            <li><a href="#" onclick="navigate('contact')" style="
                color: white;
                text-decoration: none;
                padding: 8px 16px;
                border-radius: 20px;
                transition: all 0.3s ease;
            ">Contact</a></li>
        </ul>
    </div>
</nav>

<script>
function navigate(page) {
    alert(\`Navigation vers: \${page}\`);
    // Ici vous pouvez ajouter la logique de navigation
}
</script>`;
                    break;
            }

            editor.value = componentCode;
            output.innerHTML = `✅ Composant ${type} généré avec succès!\n📝 Prêt à utiliser et personnaliser`;
        }

        // Insertion de snippets
        function insertSnippet(type) {
            const editor = document.getElementById('code-editor');
            const output = document.getElementById('code-output');

            let snippet = '';

            switch (type) {
                case 'console.log':
                    snippet = 'console.log("Message de debug");';
                    break;
                case 'function':
                    snippet = `function maFonction(param) {
    // Votre code ici
    return param;
}`;
                    break;
                case 'if-else':
                    snippet = `if (condition) {
    // Si vrai
} else {
    // Si faux
}`;
                    break;
            }

            const currentValue = editor.value;
            const cursorPos = editor.selectionStart;
            const newValue = currentValue.slice(0, cursorPos) + snippet + currentValue.slice(cursorPos);

            editor.value = newValue;
            output.innerHTML = `✅ Snippet ${type} inséré!`;
        }

        // Détection du type de code
        function detectCodeType(code) {
            if (code.includes('<!DOCTYPE html>') || code.includes('<html>')) {
                return 'HTML';
            } else if (code.includes('function') || code.includes('console.log')) {
                return 'JavaScript';
            } else if (code.includes('def ') || code.includes('import ')) {
                return 'Python';
            } else if (code.includes('{') && code.includes('}') && code.includes(':')) {
                return 'CSS';
            }
            return 'Texte';
        }

        // Mise à jour des statistiques de codage
        function updateCodeStats(projectType, linesGenerated) {
            // Mise à jour des compteurs
            const linesElement = document.getElementById('code-lines-generated');
            const projectsElement = document.getElementById('code-projects-created');

            if (linesElement) {
                const currentLines = parseInt(linesElement.textContent) || 0;
                linesElement.textContent = currentLines + linesGenerated;
            }

            if (projectsElement) {
                const currentProjects = parseInt(projectsElement.textContent) || 0;
                projectsElement.textContent = currentProjects + 1;
            }

            // Ajouter au projet récent
            const recentProjectsElement = document.getElementById('code-recent-projects');
            if (recentProjectsElement) {
                const projectName = projectType === 'tic-tac-toe' ? '🎮 Jeu Morpion' : '🧮 Calculatrice';
                const newProject = document.createElement('div');
                newProject.style.cssText = 'margin-bottom: 5px; padding: 5px; background: rgba(0, 0, 0, 0.3); border-radius: 5px;';
                newProject.innerHTML = `
                    <div style="color: #ffd700;">${projectName}</div>
                    <div style="color: #888;">Généré à l'instant - ${linesGenerated} lignes</div>
                `;
                recentProjectsElement.insertBefore(newProject, recentProjectsElement.firstChild);

                // Garder seulement les 3 derniers projets
                while (recentProjectsElement.children.length > 3) {
                    recentProjectsElement.removeChild(recentProjectsElement.lastChild);
                }
            }
        }

        // Mise à jour des métriques en temps réel pour l'interface de codage
        function updateCodeInterfaceMetrics() {
            // Récupérer les métriques depuis l'API
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.brainStats) {
                        // Mise à jour des éléments de l'interface de codage
                        const elements = {
                            'code-agent-qi': data.brainStats.qi || 100,
                            'code-memory-qi': data.thermalStats?.memoryEfficiency || 0,
                            'code-total-qi': (data.brainStats.qi || 100) + (data.thermalStats?.memoryEfficiency || 0),
                            'code-neurons': data.brainStats.activeNeurons || 0,
                            'code-synapses': data.brainStats.synapticConnections || 0,
                            'code-temperature': data.thermalStats?.temperature + '°C' || '37°C'
                        };

                        Object.entries(elements).forEach(([id, value]) => {
                            const element = document.getElementById(id);
                            if (element) {
                                element.textContent = value;
                            }
                        });
                    }
                })
                .catch(error => console.log('Métriques non disponibles:', error));
        }

        // Initialiser l'interface de codage
        function initCodeInterface() {
            // Mise à jour des métriques toutes les 5 secondes
            setInterval(updateCodeInterfaceMetrics, 5000);
            updateCodeInterfaceMetrics();

            // Support des raccourcis clavier
            document.addEventListener('keydown', function(event) {
                if (event.ctrlKey) {
                    switch (event.key) {
                        case 'g':
                            event.preventDefault();
                            generateTicTacToeCode();
                            break;
                        case 'r':
                            event.preventDefault();
                            runCode();
                            break;
                        case 'o':
                            event.preventDefault();
                            optimizeCode();
                            break;
                    }
                }
            });
        }

        // Initialiser l'interface de codage au chargement
        document.addEventListener('DOMContentLoaded', initCodeInterface);

        console.log('📝 Script LOUNA AI Ultra-Révolutionnaire chargé avec toutes les fonctionnalités');
        console.log('🔧 Fonctions de diagnostic disponibles: diagnosticCerveau3D(), testForceCerveau3D()');
        console.log('💻 Interface de codage activée avec génération automatique');
    </script>
</body>
</html>
