<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Monitoring Ultra-Avancé</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 15% 85%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 85% 15%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2d1b69 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
            animation: backgroundFlow 20s ease-in-out infinite;
        }

        @keyframes backgroundFlow {
            0%, 100% { filter: brightness(1) hue-rotate(0deg); }
            25% { filter: brightness(1.1) hue-rotate(5deg); }
            50% { filter: brightness(0.9) hue-rotate(-5deg); }
            75% { filter: brightness(1.05) hue-rotate(3deg); }
        }

        .monitoring-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            grid-template-rows: auto;
            gap: 20px;
            padding: 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        .header-full {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(25px);
            border-radius: 25px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header-full::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: headerFlow 5s infinite;
        }

        @keyframes headerFlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header-title {
            font-size: 36px;
            font-weight: 900;
            background: linear-gradient(45deg, #fff 0%, #a8e6cf 20%, #667eea 40%, #ff69b4 60%, #ffd700 80%, #fff 100%);
            background-size: 400% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleFlow 4s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            margin-bottom: 10px;
        }

        @keyframes titleFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header-subtitle {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .monitoring-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .monitoring-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
            animation: cardShimmer 8s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes cardShimmer {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(100%) translateY(100%); }
        }

        .monitoring-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff69b4;
            text-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            font-size: 24px;
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.08);
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .metric-item:hover::before {
            left: 100%;
        }

        .metric-item:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-2px);
        }

        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 900;
            color: #4ade80;
            text-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
            animation: valueGlow 2s ease-in-out infinite;
        }

        @keyframes valueGlow {
            0%, 100% { filter: brightness(1); }
            50% { filter: brightness(1.2); }
        }

        .chart-container {
            height: 300px;
            position: relative;
            margin-top: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateX(5px);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.3); }
        }

        .status-active { background: #4ade80; box-shadow: 0 0 10px rgba(74, 222, 128, 0.5); }
        .status-warning { background: #fbbf24; box-shadow: 0 0 10px rgba(251, 191, 36, 0.5); }
        .status-error { background: #ef4444; box-shadow: 0 0 10px rgba(239, 68, 68, 0.5); }
        .status-processing { background: #8b5cf6; box-shadow: 0 0 10px rgba(139, 92, 246, 0.5); }

        .status-text {
            font-size: 14px;
            font-weight: 600;
        }

        .activity-log {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 15px;
        }

        .log-entry {
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .log-time {
            color: #667eea;
            font-weight: 600;
            font-size: 11px;
        }

        .log-message {
            margin-top: 5px;
            color: rgba(255, 255, 255, 0.9);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.5s ease;
            animation: progressGlow 2s ease-in-out infinite;
        }

        @keyframes progressGlow {
            0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 15px rgba(102, 126, 234, 0.8); }
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        .real-time-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #4ade80;
            font-weight: 600;
        }

        .real-time-dot {
            width: 8px;
            height: 8px;
            background: #4ade80;
            border-radius: 50%;
            animation: realTimePulse 1s infinite;
        }

        @keyframes realTimePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="monitoring-container">
        <div class="header-full">
            <h1 class="header-title">🧠 LOUNA AI - Monitoring Ultra-Avancé</h1>
            <p class="header-subtitle">Surveillance en temps réel des systèmes IA • Mémoire Thermique • DeepSeek R1 8B</p>
            <div class="real-time-indicator">
                <div class="real-time-dot"></div>
                <span>Temps Réel Actif</span>
            </div>
        </div>

        <!-- Métriques Principales -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-brain card-icon"></i>
                Métriques IA Principales
            </h3>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-label">QI Agent</div>
                    <div class="metric-value" id="agent-iq">100</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">QI Mémoire</div>
                    <div class="metric-value" id="memory-iq">0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">QI Combiné</div>
                    <div class="metric-value" id="combined-iq">100</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Neurones</div>
                    <div class="metric-value" id="neurons">240</div>
                </div>
            </div>
        </div>

        <!-- Statut des Systèmes -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-server card-icon"></i>
                Statut des Systèmes
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-indicator status-active" id="deepseek-indicator"></div>
                    <div class="status-text">DeepSeek R1 8B</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-active" id="thermal-indicator"></div>
                    <div class="status-text">Mémoire Thermique</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-active" id="electron-indicator"></div>
                    <div class="status-text">Electron IPC</div>
                </div>
                <div class="status-item">
                    <div class="status-indicator status-processing" id="learning-indicator"></div>
                    <div class="status-text">Apprentissage Continu</div>
                </div>
            </div>
        </div>

        <!-- Performances Thermiques -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-thermometer-half card-icon"></i>
                Performances Thermiques
            </h3>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-label">Température</div>
                    <div class="metric-value" id="temperature">37.0°C</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Efficacité</div>
                    <div class="metric-value" id="efficiency">95%</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Entrées</div>
                    <div class="metric-value" id="memory-entries">50</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Zones</div>
                    <div class="metric-value" id="memory-zones">6</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="thermal-progress" style="width: 95%"></div>
            </div>
        </div>

        <!-- Graphique de Performance -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-chart-line card-icon"></i>
                Performance en Temps Réel
            </h3>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>

        <!-- Activité Récente -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-history card-icon"></i>
                Activité Récente
            </h3>
            <div class="activity-log" id="activity-log">
                <!-- Les logs seront ajoutés ici -->
            </div>
        </div>

        <!-- Métriques Avancées -->
        <div class="monitoring-card">
            <h3 class="card-title">
                <i class="fas fa-cogs card-icon"></i>
                Métriques Avancées
            </h3>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-label">Réponses</div>
                    <div class="metric-value" id="response-count">0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Qualité Moy</div>
                    <div class="metric-value" id="avg-quality">0</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Uptime</div>
                    <div class="metric-value" id="uptime">0m</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">Apprentissage</div>
                    <div class="metric-value" id="learning-rate">100%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 🧠 MONITORING ULTRA-AVANCÉ
        let performanceChart;
        let startTime = Date.now();
        let performanceData = [];
        
        // 📊 INITIALISER LE GRAPHIQUE
        function initChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'QI Combiné',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Efficacité',
                        data: [],
                        borderColor: '#ff69b4',
                        backgroundColor: 'rgba(255, 105, 180, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: '#ffffff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
        }
        
        // 📊 METTRE À JOUR LES MÉTRIQUES
        async function updateMetrics() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    // Métriques principales
                    document.getElementById('agent-iq').textContent = data.stats.agentIQ || '100';
                    document.getElementById('memory-iq').textContent = data.stats.memoryIQ || '0';
                    document.getElementById('combined-iq').textContent = data.stats.combinedIQ || '100';
                    document.getElementById('neurons').textContent = data.stats.neurons || '240';
                    
                    // Métriques thermiques
                    document.getElementById('temperature').textContent = `${data.stats.temperature || 37.0}°C`;
                    document.getElementById('efficiency').textContent = `${data.stats.efficiency || 95}%`;
                    document.getElementById('memory-entries').textContent = data.stats.memoryEntries || '50';
                    document.getElementById('memory-zones').textContent = data.stats.memoryZones || '6';
                    
                    // Mettre à jour la barre de progression
                    const thermalProgress = document.getElementById('thermal-progress');
                    thermalProgress.style.width = `${data.stats.efficiency || 95}%`;
                    
                    // Ajouter au graphique
                    const now = new Date().toLocaleTimeString();
                    performanceData.push({
                        time: now,
                        combinedIQ: data.stats.combinedIQ || 100,
                        efficiency: data.stats.efficiency || 95
                    });
                    
                    // Garder seulement les 20 derniers points
                    if (performanceData.length > 20) {
                        performanceData.shift();
                    }
                    
                    updateChart();
                }
                
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
                addLogEntry('Erreur', 'Échec de mise à jour des métriques');
            }
        }
        
        // 📈 METTRE À JOUR LE GRAPHIQUE
        function updateChart() {
            if (!performanceChart) return;
            
            performanceChart.data.labels = performanceData.map(d => d.time);
            performanceChart.data.datasets[0].data = performanceData.map(d => d.combinedIQ);
            performanceChart.data.datasets[1].data = performanceData.map(d => d.efficiency);
            performanceChart.update('none');
        }
        
        // 📝 AJOUTER UNE ENTRÉE DE LOG
        function addLogEntry(type, message) {
            const logContainer = document.getElementById('activity-log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const now = new Date().toLocaleTimeString();
            logEntry.innerHTML = `
                <div class="log-time">[${now}] ${type}</div>
                <div class="log-message">${message}</div>
            `;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // Garder seulement les 50 dernières entrées
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }
        
        // ⏱️ METTRE À JOUR L'UPTIME
        function updateUptime() {
            const uptime = Math.floor((Date.now() - startTime) / 60000);
            document.getElementById('uptime').textContent = `${uptime}m`;
        }
        
        // 🔄 METTRE À JOUR LES MÉTRIQUES AVANCÉES
        async function updateAdvancedMetrics() {
            try {
                const response = await fetch('/api/deepseek/learning');
                const data = await response.json();
                
                if (data.success && data.learning) {
                    document.getElementById('response-count').textContent = data.learning.totalInteractions || '0';
                    document.getElementById('avg-quality').textContent = data.learning.averageQuality || '0';
                    document.getElementById('learning-rate').textContent = '100%';
                }
                
            } catch (error) {
                console.error('Erreur métriques avancées:', error);
            }
        }
        
        // 🎯 INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Monitoring Ultra-Avancé initialisé');
            
            initChart();
            updateMetrics();
            updateAdvancedMetrics();
            
            addLogEntry('Système', 'Monitoring Ultra-Avancé démarré');
            addLogEntry('IA', 'Systèmes IA opérationnels');
            addLogEntry('Mémoire', 'Mémoire thermique active');
            
            // Mise à jour automatique
            setInterval(updateMetrics, 3000);
            setInterval(updateAdvancedMetrics, 10000);
            setInterval(updateUptime, 60000);
            
            // Simulation d'activité
            setInterval(() => {
                const activities = [
                    'Génération de neurones thermiques',
                    'Optimisation des connexions',
                    'Apprentissage continu actif',
                    'Transfert entre zones mémoire',
                    'Analyse de qualité des réponses',
                    'Synchronisation thermique',
                    'Évolution adaptative'
                ];
                
                const activity = activities[Math.floor(Math.random() * activities.length)];
                addLogEntry('IA', activity);
            }, 8000);
        });
    </script>
</body>
</html>
