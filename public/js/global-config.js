// 🌐 Configuration Globale LOUNA AI
// Fichier de configuration centralisé pour toutes les interfaces

window.LOUNA_CONFIG = {
    // Configuration de base
    version: '2.1.0',
    name: 'LOUNA AI',
    creator: '<PERSON><PERSON><PERSON>',
    location: 'Guadeloupe',
    
    // Configuration QI
    qi: {
        base: 100,
        current: 225,
        target: 250,
        evolutionRate: 0.1,
        maxEvolution: 500
    },
    
    // Configuration serveur
    server: {
        host: 'localhost',
        port: 52796,
        protocol: 'http'
    },
    
    // Configuration mémoire thermique
    thermal: {
        baseTemperature: 37.0,
        maxTemperature: 100.0,
        zones: 6,
        maxEntries: 100000
    },
    
    // Configuration neurones
    neurons: {
        base: 216,
        generationRate: 700, // par jour
        maxNeurons: 100000
    },
    
    // Configuration tests
    tests: {
        categories: ['logique', 'memoire', 'creativite', 'analyse', 'synthese'],
        difficulties: ['facile', 'moyen', 'difficile', 'expert'],
        maxQuestions: 50
    }
};

// 🔧 Fonctions utilitaires globales
window.LOUNA_UTILS = {
    // Obtenir l'URL de base du serveur
    getServerUrl: function() {
        const config = window.LOUNA_CONFIG.server;
        return `${config.protocol}://${config.host}:${config.port}`;
    },
    
    // Formater un timestamp
    formatTime: function(timestamp = null) {
        const date = timestamp ? new Date(timestamp) : new Date();
        return date.toLocaleTimeString('fr-FR');
    },
    
    // Formater une date complète
    formatDateTime: function(timestamp = null) {
        const date = timestamp ? new Date(timestamp) : new Date();
        return date.toLocaleString('fr-FR');
    },
    
    // Générer un ID unique
    generateId: function() {
        return 'louna_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },
    
    // Sauvegarder dans localStorage
    saveToStorage: function(key, data) {
        try {
            localStorage.setItem(`louna_${key}`, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Erreur sauvegarde localStorage:', error);
            return false;
        }
    },
    
    // Charger depuis localStorage
    loadFromStorage: function(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(`louna_${key}`);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('Erreur chargement localStorage:', error);
            return defaultValue;
        }
    },
    
    // Faire une requête API
    apiRequest: async function(endpoint, options = {}) {
        const url = this.getServerUrl() + endpoint;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            return { success: response.ok, data, status: response.status };
        } catch (error) {
            console.error('Erreur API:', error);
            return { success: false, error: error.message, status: 0 };
        }
    },
    
    // Afficher une notification
    showNotification: function(message, type = 'info', duration = 3000) {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;
        
        // Couleurs selon le type
        const colors = {
            'info': 'linear-gradient(135deg, #2196F3, #1976D2)',
            'success': 'linear-gradient(135deg, #4CAF50, #388E3C)',
            'warning': 'linear-gradient(135deg, #FF9800, #F57C00)',
            'error': 'linear-gradient(135deg, #F44336, #D32F2F)'
        };
        
        notification.style.background = colors[type] || colors.info;
        notification.textContent = message;
        
        // Ajouter au DOM
        document.body.appendChild(notification);
        
        // Animation d'entrée
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);
        
        // Suppression automatique
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
};

// 📊 Gestionnaire d'état global
window.LOUNA_STATE = {
    qi: window.LOUNA_CONFIG.qi.current,
    neurons: window.LOUNA_CONFIG.neurons.base,
    temperature: window.LOUNA_CONFIG.thermal.baseTemperature,
    memoryEntries: 150,
    isConnected: false,
    lastUpdate: Date.now(),
    
    // Mettre à jour l'état
    update: function(newState) {
        Object.assign(this, newState);
        this.lastUpdate = Date.now();
        
        // Sauvegarder dans localStorage
        window.LOUNA_UTILS.saveToStorage('state', this);
        
        // Déclencher un événement personnalisé
        window.dispatchEvent(new CustomEvent('lounaStateUpdate', { detail: this }));
    },
    
    // Charger l'état depuis localStorage
    load: function() {
        const savedState = window.LOUNA_UTILS.loadFromStorage('state');
        if (savedState) {
            Object.assign(this, savedState);
        }
    }
};

// Charger l'état au démarrage
window.LOUNA_STATE.load();

// 🚀 Initialisation globale
console.log('🌐 Configuration globale LOUNA AI chargée');
console.log('📊 État actuel:', window.LOUNA_STATE);
