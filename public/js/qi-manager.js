// 🧮 Gestionnaire de QI LOUNA AI
// Système de calcul et évolution du QI en temps réel

window.QIManager = {
    // Configuration
    config: {
        baseQI: 100,
        agentQI: 100, // QI fixe de l'agent DeepSeek
        memoryQIBase: 0,
        maxQI: 500,
        evolutionFactors: {
            complexity: 0.1,
            memory: 0.3,
            learning: 0.2,
            creativity: 0.15,
            analysis: 0.25
        }
    },
    
    // État actuel
    state: {
        agentQI: 100,
        memoryQI: 0,
        combinedQI: 100,
        lastCalculation: Date.now(),
        evolutionHistory: []
    },
    
    // 🧮 Calculer le QI de la mémoire thermique
    calculateMemoryQI: function(stats) {
        let memoryQI = 0;
        
        try {
            // Facteur entrées (max 30 points)
            const entriesScore = Math.min(30, (stats.totalEntries || 0) / 5);
            
            // Facteur efficacité (max 20 points)
            const efficiencyScore = Math.min(20, (stats.efficiency || 0) / 5);
            
            // Facteur température (max 10 points)
            const tempScore = stats.temperature ? 
                Math.min(10, Math.max(0, 10 - Math.abs(stats.temperature - 37) / 2)) : 0;
            
            // Facteur zones actives (max 12 points)
            const zonesScore = Math.min(12, (stats.activeZones || 0) * 2);
            
            // Facteur neurogenèse (max 8 points)
            const neurogenesisScore = Math.min(8, (stats.neurogenesis || 0) / 100);
            
            memoryQI = Math.round(entriesScore + efficiencyScore + tempScore + zonesScore + neurogenesisScore);
            
        } catch (error) {
            console.error('Erreur calcul QI mémoire:', error);
            memoryQI = this.state.memoryQI; // Garder la valeur précédente
        }
        
        return Math.max(0, Math.min(100, memoryQI));
    },
    
    // 🧠 Calculer le QI combiné
    calculateCombinedQI: function(agentQI = null, memoryQI = null) {
        const agent = agentQI !== null ? agentQI : this.state.agentQI;
        const memory = memoryQI !== null ? memoryQI : this.state.memoryQI;
        
        // QI combiné = QI Agent (fixe) + QI Mémoire (évolutif)
        const combined = agent + memory;
        
        return Math.min(this.config.maxQI, combined);
    },
    
    // 📊 Mettre à jour les métriques QI
    updateQI: async function(forceRefresh = false) {
        try {
            // Récupérer les métriques du serveur
            const response = await window.LOUNA_UTILS.apiRequest('/api/metrics');
            
            if (response.success && response.data) {
                const data = response.data;
                
                // Calculer le QI de la mémoire
                const thermalStats = data.thermalStats || data.brainStats || {};
                const newMemoryQI = this.calculateMemoryQI(thermalStats);
                
                // Mettre à jour l'état
                this.state.agentQI = this.config.agentQI; // Toujours fixe
                this.state.memoryQI = newMemoryQI;
                this.state.combinedQI = this.calculateCombinedQI();
                this.state.lastCalculation = Date.now();
                
                // Ajouter à l'historique si changement significatif
                if (Math.abs(newMemoryQI - (this.state.evolutionHistory[0]?.memoryQI || 0)) >= 1) {
                    this.state.evolutionHistory.unshift({
                        timestamp: Date.now(),
                        agentQI: this.state.agentQI,
                        memoryQI: this.state.memoryQI,
                        combinedQI: this.state.combinedQI,
                        reason: 'Évolution mémoire thermique'
                    });
                    
                    // Limiter l'historique à 50 entrées
                    if (this.state.evolutionHistory.length > 50) {
                        this.state.evolutionHistory = this.state.evolutionHistory.slice(0, 50);
                    }
                }
                
                // Sauvegarder
                this.saveState();
                
                // Déclencher un événement
                window.dispatchEvent(new CustomEvent('qiUpdate', { 
                    detail: this.state 
                }));
                
                return this.state;
            }
        } catch (error) {
            console.error('Erreur mise à jour QI:', error);
        }
        
        return this.state;
    },
    
    // 💾 Sauvegarder l'état
    saveState: function() {
        window.LOUNA_UTILS.saveToStorage('qi_state', this.state);
    },
    
    // 📂 Charger l'état
    loadState: function() {
        const savedState = window.LOUNA_UTILS.loadFromStorage('qi_state');
        if (savedState) {
            this.state = { ...this.state, ...savedState };
        }
    },
    
    // 🎯 Simuler une évolution cognitive
    simulateEvolution: function(reason, points = null) {
        const evolutionPoints = points || Math.floor(Math.random() * 3) + 1;
        
        // Ajouter les points au QI mémoire
        this.state.memoryQI = Math.min(100, this.state.memoryQI + evolutionPoints);
        this.state.combinedQI = this.calculateCombinedQI();
        
        // Ajouter à l'historique
        this.state.evolutionHistory.unshift({
            timestamp: Date.now(),
            agentQI: this.state.agentQI,
            memoryQI: this.state.memoryQI,
            combinedQI: this.state.combinedQI,
            reason: reason,
            evolution: evolutionPoints
        });
        
        this.saveState();
        
        // Déclencher un événement
        window.dispatchEvent(new CustomEvent('qiEvolution', { 
            detail: { 
                evolution: evolutionPoints, 
                reason: reason,
                newQI: this.state.combinedQI
            }
        }));
        
        return {
            evolution: evolutionPoints,
            newQI: this.state.combinedQI,
            message: `QI évolution: +${evolutionPoints} points (${reason})`
        };
    },
    
    // 📈 Obtenir les statistiques d'évolution
    getEvolutionStats: function() {
        const history = this.state.evolutionHistory;
        if (history.length === 0) return null;
        
        const first = history[history.length - 1];
        const last = history[0];
        
        return {
            totalEvolution: last.combinedQI - first.combinedQI,
            memoryEvolution: last.memoryQI - first.memoryQI,
            evolutionCount: history.length,
            averageEvolution: history.reduce((sum, entry) => sum + (entry.evolution || 0), 0) / history.length,
            timeSpan: last.timestamp - first.timestamp
        };
    },
    
    // 🎯 Obtenir le QI actuel
    getCurrentQI: function() {
        return this.state.combinedQI;
    },
    
    // 🧠 Obtenir les détails du QI
    getQIDetails: function() {
        return {
            agent: this.state.agentQI,
            memory: this.state.memoryQI,
            combined: this.state.combinedQI,
            lastUpdate: this.state.lastCalculation
        };
    }
};

// 🚀 Initialisation
window.QIManager.loadState();

// Fonction globale pour compatibilité
window.getCurrentQI = function() {
    return window.QIManager.getCurrentQI();
};

// Mise à jour automatique toutes les 10 secondes
setInterval(() => {
    window.QIManager.updateQI();
}, 10000);

console.log('🧮 Gestionnaire de QI initialisé');
console.log('📊 QI actuel:', window.QIManager.getQIDetails());
