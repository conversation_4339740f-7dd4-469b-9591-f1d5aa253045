<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOUNA AI - Formation Accélérée</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: radial-gradient(circle at center, #0a0a0a 0%, #2a1a2e 50%, #3e1a3e 100%);
            color: #c084fc;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(192, 132, 252, 0.1);
            border-bottom: 2px solid #c084fc;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px #c084fc;
            background: linear-gradient(45deg, #c084fc, #f472b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .training-panel {
            background: rgba(192, 132, 252, 0.1);
            border: 2px solid #c084fc;
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .panel-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            color: #c084fc;
            text-shadow: 0 0 10px #c084fc;
        }

        .training-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-group {
            background: rgba(244, 114, 182, 0.1);
            border: 1px solid #f472b6;
            border-radius: 10px;
            padding: 15px;
        }

        .control-label {
            font-weight: bold;
            margin-bottom: 10px;
            color: #f472b6;
        }

        .control-input {
            width: 100%;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #c084fc;
            border-radius: 5px;
            padding: 8px;
            color: #c084fc;
            margin-bottom: 10px;
        }

        .btn {
            width: 100%;
            background: linear-gradient(45deg, #c084fc, #f472b6);
            border: none;
            color: #000;
            padding: 12px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px 0;
            transition: all 0.3s ease;
            font-size: 1em;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(192, 132, 252, 0.4);
        }

        .btn-emergency {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            animation: pulse 2s infinite;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #c084fc;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .training-log {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #c084fc;
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #c084fc;
            padding-left: 10px;
        }

        .log-success {
            border-left-color: #10b981;
            color: #10b981;
        }

        .log-warning {
            border-left-color: #f59e0b;
            color: #f59e0b;
        }

        .log-error {
            border-left-color: #ef4444;
            color: #ef4444;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #c084fc, #f472b6);
            width: 0%;
            transition: width 0.3s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @media (max-width: 768px) {
            .main-container {
                grid-template-columns: 1fr;
            }
            
            .training-controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div style="text-align: left; margin-bottom: 15px;">
            <button onclick="window.location.href='/'" class="btn" style="width: auto; padding: 10px 20px;">🏠 Retour à l'Accueil</button>
        </div>
        <h1>⚡ FORMATION ACCÉLÉRÉE LOUNA AI</h1>
        <div>Interface de formation intensive avec génération massive de neurones</div>
    </div>

    <div class="main-container">
        <!-- Panel de contrôle de formation -->
        <div class="training-panel">
            <div class="panel-title">🎯 Contrôles de Formation</div>
            
            <div class="training-controls">
                <div class="control-group">
                    <div class="control-label">Mode de Formation</div>
                    <select id="trainingMode" class="control-input">
                        <option value="normal">Normal (2 neurones/sec)</option>
                        <option value="intensive">Intensif (5 neurones/sec)</option>
                        <option value="turbo">Turbo (8 neurones/sec)</option>
                        <option value="extreme">Extrême (12 neurones/sec)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <div class="control-label">Durée (secondes)</div>
                    <input type="number" id="trainingDuration" class="control-input" value="30" min="5" max="300">
                </div>
                
                <div class="control-group">
                    <div class="control-label">Agent Cible</div>
                    <select id="targetAgent" class="control-input">
                        <option value="main">Agent Principal (LOUNA)</option>
                        <option value="deepseek">DeepSeek R1</option>
                        <option value="both">Les Deux Agents</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <div class="control-label">Type de Formation</div>
                    <select id="trainingType" class="control-input">
                        <option value="neurogenesis">Neurogenèse</option>
                        <option value="synaptic">Connexions Synaptiques</option>
                        <option value="memory">Consolidation Mémoire</option>
                        <option value="complete">Formation Complète</option>
                    </select>
                </div>
            </div>
            
            <button class="btn" onclick="startTraining()">🚀 Démarrer Formation</button>
            <button class="btn" onclick="pauseTraining()">⏸️ Pause Formation</button>
            <button class="btn" onclick="startQuestionSession()">🤖 Session Questions</button>
            <button class="btn btn-emergency" onclick="stopTraining()">🛑 Arrêt d'Urgence</button>
            
            <div class="panel-title" style="margin-top: 30px;">📊 Progression</div>
            <div class="progress-bar">
                <div class="progress-fill" id="trainingProgress"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="progressText">En attente...</span>
            </div>
        </div>

        <!-- Panel de métriques -->
        <div class="training-panel">
            <div class="panel-title">📈 Métriques de Formation</div>

            <div class="metrics-grid" id="metricsGrid">
                <!-- Les métriques seront ajoutées dynamiquement -->
            </div>

            <!-- Zone de Questions de l'Agent -->
            <div class="panel-title" style="margin-top: 20px;">🤖 Agent de Formation</div>
            <div id="questionPanel" style="background: rgba(0, 0, 0, 0.3); border: 1px solid #c084fc; border-radius: 10px; padding: 15px; margin-bottom: 20px; display: none;">
                <div id="currentQuestion" style="margin-bottom: 15px; font-weight: bold; color: #f472b6;"></div>
                <input type="text" id="answerInput" class="control-input" placeholder="Tapez votre réponse ici..." style="margin-bottom: 10px;">
                <div style="display: flex; gap: 10px;">
                    <button class="btn" onclick="submitAnswer()" style="flex: 1;">📝 Répondre</button>
                    <button class="btn" onclick="skipQuestion()" style="flex: 1; background: linear-gradient(45deg, #6b7280, #4b5563);">⏭️ Passer</button>
                </div>
                <div id="questionFeedback" style="margin-top: 10px; padding: 10px; border-radius: 5px; display: none;"></div>
            </div>

            <div class="panel-title">📝 Journal de Formation</div>
            <div class="training-log" id="trainingLog">
                <div class="log-entry">🟢 Système de formation initialisé</div>
                <div class="log-entry">🟢 Accélérateurs Kyber connectés</div>
                <div class="log-entry">🟢 Mémoire thermique prête</div>
                <div class="log-entry">🤖 Agent de formation connecté</div>
                <div class="log-entry">⚡ En attente de commandes...</div>
            </div>
        </div>
    </div>

    <script>
        let trainingActive = false;
        let trainingInterval = null;
        let trainingStartTime = null;
        let totalNeuronsGenerated = 0;
        let questionSession = false;
        let currentQuestionData = null;
        let questionCount = 0;
        let correctAnswers = 0;

        // Initialiser l'interface
        document.addEventListener('DOMContentLoaded', function() {
            updateMetrics();
            setInterval(updateMetrics, 5000); // Mise à jour toutes les 5 secondes
        });

        // Démarrer la formation
        async function startTraining() {
            if (trainingActive) {
                addLog('⚠️ Formation déjà en cours', 'warning');
                return;
            }

            const mode = document.getElementById('trainingMode').value;
            const duration = parseInt(document.getElementById('trainingDuration').value);
            const agent = document.getElementById('targetAgent').value;
            const type = document.getElementById('trainingType').value;

            addLog(`🚀 Démarrage formation ${mode} pour ${duration}s`, 'success');
            addLog(`🎯 Agent cible: ${agent}`, 'info');
            addLog(`📋 Type: ${type}`, 'info');

            try {
                const response = await fetch('/api/training/accelerated', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        mode: mode,
                        duration: duration,
                        agent: agent,
                        type: type
                    })
                });

                const data = await response.json();

                if (data.success) {
                    trainingActive = true;
                    trainingStartTime = Date.now();
                    totalNeuronsGenerated = 0;

                    addLog(`✅ Formation démarrée avec succès`, 'success');
                    addLog(`⚡ Génération: ${data.neuronsPerSecond || 'N/A'} neurones/sec`, 'info');

                    // Démarrer le suivi de progression
                    startProgressTracking(duration);

                } else {
                    addLog(`❌ Erreur: ${data.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur de connexion: ${error.message}`, 'error');
            }
        }

        // Pause formation
        function pauseTraining() {
            if (!trainingActive) {
                addLog('⚠️ Aucune formation en cours', 'warning');
                return;
            }

            trainingActive = false;
            if (trainingInterval) {
                clearInterval(trainingInterval);
            }

            addLog('⏸️ Formation mise en pause', 'warning');
            document.getElementById('progressText').textContent = 'En pause...';
        }

        // Arrêt d'urgence
        async function stopTraining() {
            if (!trainingActive) {
                addLog('⚠️ Aucune formation en cours', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/training/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                trainingActive = false;
                if (trainingInterval) {
                    clearInterval(trainingInterval);
                }

                addLog('🛑 Formation arrêtée d\'urgence', 'error');
                document.getElementById('progressText').textContent = 'Arrêtée';
                document.getElementById('trainingProgress').style.width = '0%';

            } catch (error) {
                addLog(`❌ Erreur arrêt: ${error.message}`, 'error');
            }
        }

        // Démarrer le suivi de progression
        function startProgressTracking(duration) {
            let elapsed = 0;

            trainingInterval = setInterval(() => {
                elapsed += 1;
                const progress = (elapsed / duration) * 100;

                document.getElementById('trainingProgress').style.width = progress + '%';
                document.getElementById('progressText').textContent =
                    `${elapsed}s / ${duration}s (${Math.round(progress)}%)`;

                // Simuler la génération de neurones
                const neuronsThisSecond = Math.floor(Math.random() * 8) + 2;
                totalNeuronsGenerated += neuronsThisSecond;

                if (elapsed % 5 === 0) {
                    addLog(`🧠 +${neuronsThisSecond} neurones générés (Total: ${totalNeuronsGenerated})`, 'success');
                }

                if (elapsed >= duration) {
                    clearInterval(trainingInterval);
                    trainingActive = false;
                    addLog(`🎉 Formation terminée ! ${totalNeuronsGenerated} neurones générés`, 'success');
                    document.getElementById('progressText').textContent = 'Terminée !';
                    updateMetrics();
                }
            }, 1000);
        }

        // Ajouter une entrée au journal
        function addLog(message, type = 'info') {
            const log = document.getElementById('trainingLog');
            const timestamp = new Date().toLocaleTimeString();

            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;

            log.insertBefore(entry, log.firstChild);

            // Limiter à 50 entrées
            while (log.children.length > 50) {
                log.removeChild(log.lastChild);
            }
        }

        // Mettre à jour les métriques
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    displayMetrics(data);
                }
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        // 🤖 DÉMARRER SESSION DE QUESTIONS
        async function startQuestionSession() {
            if (questionSession) {
                addLog('⚠️ Session de questions déjà en cours', 'warning');
                return;
            }

            questionSession = true;
            questionCount = 0;
            correctAnswers = 0;

            document.getElementById('questionPanel').style.display = 'block';
            addLog('🤖 Session de questions démarrée', 'success');

            // Poser la première question
            await askNextQuestion();
        }

        // 🤖 POSER LA PROCHAINE QUESTION
        async function askNextQuestion() {
            if (!questionSession) return;

            try {
                const categories = ['logique', 'memoire', 'creativite'];
                const difficulties = ['facile', 'moyen', 'difficile'];

                const category = categories[Math.floor(Math.random() * categories.length)];
                const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];

                addLog(`🎯 Génération question ${difficulty} en ${category}...`, 'info');

                const response = await fetch('/api/training/ask-question', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        difficulty: difficulty,
                        category: category,
                        agentTarget: 'main'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentQuestionData = data;
                    questionCount++;

                    document.getElementById('currentQuestion').innerHTML =
                        `<strong>Question ${questionCount} (${data.difficulty} - ${data.category}):</strong><br>${data.question}`;
                    document.getElementById('answerInput').value = '';
                    document.getElementById('answerInput').focus();
                    document.getElementById('questionFeedback').style.display = 'none';

                    addLog(`❓ Question posée: ${data.question.substring(0, 50)}...`, 'info');
                } else {
                    addLog(`❌ Erreur génération question: ${data.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur connexion: ${error.message}`, 'error');
            }
        }

        // 📝 SOUMETTRE RÉPONSE
        async function submitAnswer() {
            if (!currentQuestionData) {
                addLog('⚠️ Aucune question active', 'warning');
                return;
            }

            const answer = document.getElementById('answerInput').value.trim();
            if (!answer) {
                addLog('⚠️ Veuillez saisir une réponse', 'warning');
                return;
            }

            try {
                addLog(`📝 Évaluation de la réponse: "${answer.substring(0, 30)}..."`, 'info');

                const response = await fetch('/api/training/evaluate-answer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        questionId: currentQuestionData.questionId,
                        answer: answer,
                        category: currentQuestionData.category,
                        difficulty: currentQuestionData.difficulty
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Afficher le feedback
                    const feedbackDiv = document.getElementById('questionFeedback');
                    feedbackDiv.style.display = 'block';
                    feedbackDiv.style.background = data.score >= 70 ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)';
                    feedbackDiv.style.border = `1px solid ${data.score >= 70 ? '#10b981' : '#ef4444'}`;
                    feedbackDiv.innerHTML = `
                        <strong>Score: ${data.score}/100</strong><br>
                        ${data.feedback}<br>
                        <em>${data.improvement}</em>
                    `;

                    if (data.score >= 70) correctAnswers++;

                    addLog(`✅ Score: ${data.score}/100 - ${data.feedback}`, data.score >= 70 ? 'success' : 'warning');

                    // Prochaine question après 3 secondes
                    setTimeout(() => {
                        if (questionCount < 10) { // Limiter à 10 questions par session
                            askNextQuestion();
                        } else {
                            endQuestionSession();
                        }
                    }, 3000);

                } else {
                    addLog(`❌ Erreur évaluation: ${data.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur connexion: ${error.message}`, 'error');
            }
        }

        // ⏭️ PASSER LA QUESTION
        function skipQuestion() {
            if (!currentQuestionData) return;

            addLog(`⏭️ Question passée: ${currentQuestionData.question.substring(0, 30)}...`, 'warning');

            if (questionCount < 10) {
                askNextQuestion();
            } else {
                endQuestionSession();
            }
        }

        // 🏁 TERMINER SESSION DE QUESTIONS
        function endQuestionSession() {
            questionSession = false;
            document.getElementById('questionPanel').style.display = 'none';

            const successRate = Math.round((correctAnswers / questionCount) * 100);
            addLog(`🏁 Session terminée: ${correctAnswers}/${questionCount} bonnes réponses (${successRate}%)`, 'success');

            // Évaluation globale
            let evaluation = '';
            if (successRate >= 80) {
                evaluation = '🌟 Excellent niveau !';
            } else if (successRate >= 60) {
                evaluation = '👍 Bon niveau, continuez !';
            } else {
                evaluation = '📚 Besoin d\'amélioration';
            }

            addLog(evaluation, 'info');
            updateMetrics();
        }

        // Gestion de la touche Entrée pour les réponses
        document.addEventListener('DOMContentLoaded', function() {
            const answerInput = document.getElementById('answerInput');
            if (answerInput) {
                answerInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        submitAnswer();
                    }
                });
            }
        });

        // Afficher les métriques
        function displayMetrics(metrics) {
            const grid = document.getElementById('metricsGrid');

            // Récupérer les données QI depuis localStorage si pas dans metrics
            let agentIQ = 'Calcul...';
            let memoryIQ = 'Calcul...';
            let combinedIQ = 'Calcul...';

            if (metrics.iqAnalysis) {
                agentIQ = metrics.iqAnalysis.agentIQ || 'N/A';
                memoryIQ = metrics.iqAnalysis.memoryIQ || 'N/A';
                combinedIQ = metrics.iqAnalysis.combinedIQ || 'N/A';
            } else {
                // Essayer de récupérer depuis localStorage
                const savedIQ = localStorage.getItem('lounaIQData');
                if (savedIQ) {
                    const iqData = JSON.parse(savedIQ);
                    agentIQ = iqData.agentIQ || 'N/A';
                    memoryIQ = iqData.memoryIQ || 'N/A';
                    combinedIQ = iqData.combinedIQ || 'N/A';
                }
            }

            // Sauvegarder les nouvelles données QI
            if (metrics.iqAnalysis) {
                localStorage.setItem('lounaIQData', JSON.stringify({
                    agentIQ: metrics.iqAnalysis.agentIQ,
                    memoryIQ: metrics.iqAnalysis.memoryIQ,
                    combinedIQ: metrics.iqAnalysis.combinedIQ,
                    timestamp: Date.now()
                }));
            }

            const metricsData = [
                {
                    label: 'QI Agent Principal',
                    value: agentIQ,
                    color: '#c084fc',
                    evolution: getEvolution('agentIQ', agentIQ)
                },
                {
                    label: 'QI Mémoire Thermique',
                    value: memoryIQ,
                    color: '#f472b6',
                    evolution: getEvolution('memoryIQ', memoryIQ)
                },
                {
                    label: 'QI Combiné Total',
                    value: combinedIQ,
                    color: '#a855f7',
                    evolution: getEvolution('combinedIQ', combinedIQ)
                },
                {
                    label: 'Neurones Actifs',
                    value: (metrics.brainStats?.activeNeurons || 0).toLocaleString(),
                    color: '#8b5cf6',
                    evolution: getEvolution('neurons', metrics.brainStats?.activeNeurons)
                },
                {
                    label: 'Connexions Synaptiques',
                    value: (metrics.brainStats?.synapticConnections || 0).toLocaleString(),
                    color: '#ec4899',
                    evolution: getEvolution('synapses', metrics.brainStats?.synapticConnections)
                },
                {
                    label: 'Température Mémoire',
                    value: (metrics.memoryStats?.globalTemperature || 0).toFixed(1) + '°C',
                    color: '#f59e0b',
                    evolution: getEvolution('temperature', metrics.memoryStats?.globalTemperature)
                },
                {
                    label: 'Efficacité Système',
                    value: Math.round(metrics.memoryStats?.memoryEfficiency || 0) + '%',
                    color: '#10b981',
                    evolution: getEvolution('efficiency', metrics.memoryStats?.memoryEfficiency)
                },
                {
                    label: 'Agent Actuel',
                    value: metrics.agent || 'DeepSeek R1-0528',
                    color: '#06b6d4',
                    evolution: ''
                },
                {
                    label: 'Accélérateurs Kyber',
                    value: metrics.accelerators?.count || '30+',
                    color: '#8b5cf6',
                    evolution: ''
                },
                {
                    label: 'Formation Active',
                    value: trainingActive ? 'EN COURS' : 'ARRÊTÉE',
                    color: trainingActive ? '#10b981' : '#ef4444',
                    evolution: ''
                },
                {
                    label: 'Neurones Générés',
                    value: totalNeuronsGenerated.toLocaleString(),
                    color: '#a855f7',
                    evolution: totalNeuronsGenerated > 0 ? '↗' : ''
                },
                {
                    label: 'Mémoires Stockées',
                    value: (metrics.memoryStats?.totalMemories || 0).toLocaleString(),
                    color: '#f472b6',
                    evolution: getEvolution('memories', metrics.memoryStats?.totalMemories)
                }
            ];

            grid.innerHTML = metricsData.map(metric => `
                <div class="metric-card">
                    <div class="metric-value" style="color: ${metric.color}">
                        ${metric.value}
                        ${metric.evolution ? `<span style="margin-left: 5px; font-size: 0.8em;">${metric.evolution}</span>` : ''}
                    </div>
                    <div class="metric-label">${metric.label}</div>
                </div>
            `).join('');

            // Sauvegarder les métriques précédentes pour calculer l'évolution
            previousMetrics = { ...metrics };
        }

        // Variables pour l'évolution
        let previousMetrics = null;

        // Calculer l'évolution des métriques
        function getEvolution(key, currentValue) {
            if (!previousMetrics || currentValue === undefined || currentValue === null) return '';

            let previousValue;
            switch(key) {
                case 'agentIQ':
                    previousValue = previousMetrics.iqAnalysis?.agentIQ;
                    break;
                case 'memoryIQ':
                    previousValue = previousMetrics.iqAnalysis?.memoryIQ;
                    break;
                case 'combinedIQ':
                    previousValue = previousMetrics.iqAnalysis?.combinedIQ;
                    break;
                case 'neurons':
                    previousValue = previousMetrics.brainStats?.activeNeurons;
                    break;
                case 'synapses':
                    previousValue = previousMetrics.brainStats?.synapticConnections;
                    break;
                case 'temperature':
                    previousValue = previousMetrics.memoryStats?.globalTemperature;
                    break;
                case 'efficiency':
                    previousValue = previousMetrics.memoryStats?.memoryEfficiency;
                    break;
                case 'memories':
                    previousValue = previousMetrics.memoryStats?.totalMemories;
                    break;
                default:
                    return '';
            }

            if (previousValue === undefined || previousValue === null) return '';

            const current = parseFloat(currentValue);
            const previous = parseFloat(previousValue);

            if (current > previous) return '↗';
            if (current < previous) return '↘';
            return '→';
        }
    </script>
</body>
</html>
