<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 RÉCUPÉRATION D'URGENCE NEURONES - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 50%, #990000 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .emergency-container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 30px;
            border: 3px solid #ff0000;
            box-shadow: 0 0 30px rgba(255, 0, 0, 0.5);
        }

        .emergency-header {
            text-align: center;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .emergency-title {
            font-size: 2.5rem;
            color: #ff0000;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid #ff6666;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ff3333;
        }

        .status-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ff6666;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .emergency-btn {
            background: linear-gradient(135deg, #ff0000, #cc0000);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
        }

        .emergency-btn:hover {
            background: linear-gradient(135deg, #cc0000, #990000);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 0, 0, 0.5);
        }

        .emergency-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .emergency-btn.success {
            background: linear-gradient(135deg, #00ff00, #00cc00);
        }

        .log-panel {
            background: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 2px solid #ff3333;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 5px 0;
            padding: 8px;
            border-left: 3px solid #ff6666;
            background: rgba(255, 255, 255, 0.05);
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-timestamp {
            color: #ff9999;
            font-size: 0.8rem;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid #ff6666;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .warning-box {
            background: rgba(255, 165, 0, 0.2);
            border: 2px solid #ffa500;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff0000, #ff6666);
            width: 0%;
            transition: width 0.3s ease;
        }

        .critical-alert {
            background: rgba(255, 0, 0, 0.3);
            border: 3px solid #ff0000;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <div class="emergency-container">
        <div class="emergency-header">
            <div class="emergency-title">
                <i class="fas fa-exclamation-triangle"></i>
                RÉCUPÉRATION D'URGENCE NEURONES
            </div>
            <p>Système de récupération des neurones perdus - LOUNA AI v2.1.0</p>
            <div style="margin-top: 15px;">
                <a href="/" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
                <a href="/qi-test-simple.html" class="nav-btn"><i class="fas fa-brain"></i> Diagnostic</a>
                <a href="/mcp-test-interface.html" class="nav-btn"><i class="fas fa-cogs"></i> Tests MCP</a>
            </div>
        </div>

        <div class="critical-alert">
            <h3><i class="fas fa-skull-crossbones"></i> ALERTE CRITIQUE</h3>
            <p>Des neurones ont été perdus ! Utilisez cette interface pour récupérer immédiatement vos neurones sauvegardés.</p>
        </div>

        <div class="warning-box">
            <h4><i class="fas fa-info-circle"></i> Information Importante</h4>
            <p>Cette interface permet de récupérer les neurones qui n'ont pas été correctement sauvegardés. 
            Le système va scanner toutes les zones mémoire et régénérer les neurones manquants.</p>
        </div>

        <div class="status-panel">
            <h3><i class="fas fa-chart-bar"></i> État Actuel du Système</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="neurons-saved">0</div>
                    <div class="status-label">Neurones Sauvegardés</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="neurons-generated">0</div>
                    <div class="status-label">Neurones Générés</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="neurons-zone">0</div>
                    <div class="status-label">Zone Neuronale</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="neurons-emergency">0</div>
                    <div class="status-label">Sauvegardes Urgence</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="temperature">37.0°C</div>
                    <div class="status-label">Température</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="memory-entries">0</div>
                    <div class="status-label">Entrées Mémoire</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="recovery-progress"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="progress-text">Prêt pour la récupération</span>
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <button class="emergency-btn" onclick="checkNeuronStatus()">
                <i class="fas fa-search"></i> Vérifier Statut Neurones
            </button>
            
            <button class="emergency-btn" onclick="recoverLostNeurons()">
                <i class="fas fa-medkit"></i> RÉCUPÉRER NEURONES PERDUS
            </button>
        </div>

        <div class="log-panel">
            <h4><i class="fas fa-list"></i> Journal de Récupération</h4>
            <div id="recovery-log">
                <div class="log-entry">
                    <span class="log-timestamp">[INIT]</span> Interface de récupération d'urgence prête
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecovering = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 Interface de récupération d\'urgence initialisée');
            checkNeuronStatus();
        });

        // Vérifier le statut des neurones
        async function checkNeuronStatus() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Vérification...';
            
            try {
                const response = await fetch('/api/neurons/status');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusDisplay(data);
                    addLogEntry('SUCCESS', 'Statut des neurones vérifié', data.neuronStats);
                } else {
                    addLogEntry('ERROR', 'Erreur vérification statut', data.error);
                }
            } catch (error) {
                addLogEntry('ERROR', 'Erreur connexion API', error.message);
            }
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-search"></i> Vérifier Statut Neurones';
        }

        // Récupérer les neurones perdus
        async function recoverLostNeurons() {
            if (isRecovering) return;
            
            const btn = event.target;
            isRecovering = true;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> RÉCUPÉRATION EN COURS...';
            
            updateProgress(0, 'Démarrage de la récupération...');
            addLogEntry('START', 'DÉMARRAGE RÉCUPÉRATION D\'URGENCE DES NEURONES');
            
            try {
                updateProgress(25, 'Connexion au système de mémoire thermique...');
                
                const response = await fetch('/api/recover-neurons', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                updateProgress(50, 'Traitement de la récupération...');
                
                const data = await response.json();
                
                updateProgress(75, 'Finalisation...');
                
                if (data.success) {
                    updateProgress(100, 'Récupération terminée avec succès !');
                    addLogEntry('SUCCESS', 'RÉCUPÉRATION RÉUSSIE', data.result);
                    
                    btn.className = 'emergency-btn success';
                    btn.innerHTML = '<i class="fas fa-check"></i> RÉCUPÉRATION RÉUSSIE';
                    
                    // Mettre à jour le statut
                    setTimeout(() => {
                        checkNeuronStatus();
                    }, 2000);
                    
                } else {
                    updateProgress(0, 'Erreur lors de la récupération');
                    addLogEntry('ERROR', 'ÉCHEC RÉCUPÉRATION', data.error);
                    btn.innerHTML = '<i class="fas fa-times"></i> ÉCHEC - Réessayer';
                }
                
            } catch (error) {
                updateProgress(0, 'Erreur de connexion');
                addLogEntry('ERROR', 'ERREUR CRITIQUE', error.message);
                btn.innerHTML = '<i class="fas fa-times"></i> ERREUR - Réessayer';
            }
            
            isRecovering = false;
            btn.disabled = false;
        }

        // Mettre à jour l'affichage du statut
        function updateStatusDisplay(data) {
            const neuronStats = data.neuronStats || {};
            const thermalStats = data.thermalStats || {};
            
            document.getElementById('neurons-saved').textContent = neuronStats.totalSaved || 0;
            document.getElementById('neurons-generated').textContent = neuronStats.totalGenerated || 0;
            document.getElementById('neurons-zone').textContent = neuronStats.inNeuronZone || 0;
            document.getElementById('neurons-emergency').textContent = neuronStats.emergencyBackups || 0;
            document.getElementById('temperature').textContent = (thermalStats.temperature || 37.0).toFixed(1) + '°C';
            document.getElementById('memory-entries').textContent = thermalStats.totalEntries || 0;
        }

        // Mettre à jour la barre de progression
        function updateProgress(percentage, text) {
            document.getElementById('recovery-progress').style.width = percentage + '%';
            document.getElementById('progress-text').textContent = text;
        }

        // Ajouter une entrée au journal
        function addLogEntry(type, message, details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('recovery-log');
            
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            let content = `<span class="log-timestamp">[${timestamp}] [${type}]</span> ${message}`;
            if (details) {
                content += `<br><pre style="margin-top: 5px; font-size: 0.8rem;">${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            entry.innerHTML = content;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>
</html>
