<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Morpion LOUNA AI - Démonstration Live</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .subtitle {
            margin-bottom: 30px;
            opacity: 0.8;
            font-size: 1.1em;
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(3, 100px);
            grid-template-rows: repeat(3, 100px);
            gap: 5px;
            margin: 20px auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 15px;
        }

        .cell {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            font-size: 2em;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cell:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .cell:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .cell.x {
            color: #ff6b6b;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .cell.o {
            color: #4ecdc4;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .game-info {
            margin: 20px 0;
            font-size: 1.2em;
        }

        .current-player {
            font-weight: bold;
            color: #ffd93d;
        }

        .reset-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .winner-message {
            font-size: 1.5em;
            font-weight: bold;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #ffd93d;
        }

        .stats {
            margin-top: 20px;
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd93d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Morpion LOUNA AI</h1>
        <p class="subtitle">Démonstration des capacités de développement en temps réel</p>
        
        <div class="game-info">
            <span>Joueur actuel : <span class="current-player" id="currentPlayer">X</span></span>
        </div>

        <div class="game-board" id="gameBoard">
            <button class="cell" data-index="0"></button>
            <button class="cell" data-index="1"></button>
            <button class="cell" data-index="2"></button>
            <button class="cell" data-index="3"></button>
            <button class="cell" data-index="4"></button>
            <button class="cell" data-index="5"></button>
            <button class="cell" data-index="6"></button>
            <button class="cell" data-index="7"></button>
            <button class="cell" data-index="8"></button>
        </div>

        <div id="winnerMessage" class="winner-message" style="display: none;"></div>

        <button class="reset-btn" onclick="resetGame()">🔄 Nouvelle Partie</button>

        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="xWins">0</div>
                <div>Victoires X</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="draws">0</div>
                <div>Égalités</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="oWins">0</div>
                <div>Victoires O</div>
            </div>
        </div>
    </div>

    <script>
        // État du jeu
        let currentPlayer = 'X';
        let gameBoard = ['', '', '', '', '', '', '', '', ''];
        let gameActive = true;
        let stats = { xWins: 0, oWins: 0, draws: 0 };

        // Combinaisons gagnantes
        const winningCombinations = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // Lignes
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // Colonnes
            [0, 4, 8], [2, 4, 6] // Diagonales
        ];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.addEventListener('click', handleCellClick);
            });
            updateDisplay();
        });

        // Gestion du clic sur une case
        function handleCellClick(event) {
            const index = event.target.getAttribute('data-index');
            
            if (gameBoard[index] !== '' || !gameActive) {
                return;
            }

            // Placer le symbole
            gameBoard[index] = currentPlayer;
            event.target.textContent = currentPlayer;
            event.target.classList.add(currentPlayer.toLowerCase());
            event.target.disabled = true;

            // Vérifier la victoire
            if (checkWinner()) {
                gameActive = false;
                showWinner(currentPlayer);
                updateStats(currentPlayer);
            } else if (gameBoard.every(cell => cell !== '')) {
                gameActive = false;
                showWinner('draw');
                updateStats('draw');
            } else {
                // Changer de joueur
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                updateCurrentPlayer();
            }
        }

        // Vérifier s'il y a un gagnant
        function checkWinner() {
            return winningCombinations.some(combination => {
                const [a, b, c] = combination;
                return gameBoard[a] && 
                       gameBoard[a] === gameBoard[b] && 
                       gameBoard[a] === gameBoard[c];
            });
        }

        // Afficher le gagnant
        function showWinner(winner) {
            const winnerMessage = document.getElementById('winnerMessage');
            if (winner === 'draw') {
                winnerMessage.textContent = '🤝 Égalité !';
            } else {
                winnerMessage.textContent = `🎉 Joueur ${winner} gagne !`;
            }
            winnerMessage.style.display = 'block';
        }

        // Mettre à jour l'affichage du joueur actuel
        function updateCurrentPlayer() {
            document.getElementById('currentPlayer').textContent = currentPlayer;
        }

        // Mettre à jour les statistiques
        function updateStats(winner) {
            if (winner === 'X') {
                stats.xWins++;
            } else if (winner === 'O') {
                stats.oWins++;
            } else {
                stats.draws++;
            }
            updateDisplay();
        }

        // Mettre à jour l'affichage des statistiques
        function updateDisplay() {
            document.getElementById('xWins').textContent = stats.xWins;
            document.getElementById('oWins').textContent = stats.oWins;
            document.getElementById('draws').textContent = stats.draws;
        }

        // Réinitialiser le jeu
        function resetGame() {
            currentPlayer = 'X';
            gameBoard = ['', '', '', '', '', '', '', '', ''];
            gameActive = true;
            
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.textContent = '';
                cell.disabled = false;
                cell.classList.remove('x', 'o');
            });
            
            document.getElementById('winnerMessage').style.display = 'none';
            updateCurrentPlayer();
        }

        // Animation d'entrée
        window.addEventListener('load', function() {
            document.querySelector('.container').style.animation = 'fadeInUp 0.8s ease-out';
        });
    </script>

    <style>
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</body>
</html>
