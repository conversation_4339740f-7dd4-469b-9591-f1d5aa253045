<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Morpion - Test LOUNA AI</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .game-board {
            display: grid;
            grid-template-columns: repeat(3, 100px);
            grid-template-rows: repeat(3, 100px);
            gap: 5px;
            margin: 20px auto;
            background: #333;
            padding: 5px;
            border-radius: 10px;
        }
        
        .cell {
            background: white;
            border: none;
            font-size: 2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 5px;
            color: #333;
        }
        
        .cell:hover {
            background: #f0f0f0;
            transform: scale(1.05);
        }
        
        .cell.x {
            color: #e74c3c;
        }
        
        .cell.o {
            color: #3498db;
        }
        
        .status {
            font-size: 1.5em;
            margin: 20px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .controls {
            margin-top: 20px;
        }
        
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .stats {
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            text-align: center;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .ai-thinking {
            color: #f39c12;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Morpion - Test LOUNA AI</h1>
        
        <div class="status" id="status">
            🎯 Votre tour (X) - Cliquez sur une case !
        </div>
        
        <div class="game-board" id="gameBoard">
            <button class="cell" data-index="0"></button>
            <button class="cell" data-index="1"></button>
            <button class="cell" data-index="2"></button>
            <button class="cell" data-index="3"></button>
            <button class="cell" data-index="4"></button>
            <button class="cell" data-index="5"></button>
            <button class="cell" data-index="6"></button>
            <button class="cell" data-index="7"></button>
            <button class="cell" data-index="8"></button>
        </div>
        
        <div class="controls">
            <button onclick="resetGame()">🔄 Nouvelle Partie</button>
            <button onclick="toggleDifficulty()">⚙️ Difficulté: <span id="difficulty">Normale</span></button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <span class="stat-number" id="playerWins">0</span>
                <div>Victoires Joueur</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="aiWins">0</span>
                <div>Victoires IA</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="draws">0</span>
                <div>Égalités</div>
            </div>
        </div>
    </div>

    <script>
        // 🎮 VARIABLES DU JEU
        let board = ['', '', '', '', '', '', '', '', ''];
        let currentPlayer = 'X';
        let gameActive = true;
        let aiDifficulty = 'normal'; // easy, normal, hard
        let stats = {
            playerWins: 0,
            aiWins: 0,
            draws: 0
        };

        // 🎯 COMBINAISONS GAGNANTES
        const winningConditions = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // Lignes
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // Colonnes
            [0, 4, 8], [2, 4, 6] // Diagonales
        ];

        // 🚀 INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.addEventListener('click', handleCellClick);
            });
            updateStatus('🎯 Votre tour (X) - Cliquez sur une case !');
        });

        // 🎯 GESTION DES CLICS
        function handleCellClick(event) {
            const clickedCell = event.target;
            const clickedCellIndex = parseInt(clickedCell.getAttribute('data-index'));

            if (board[clickedCellIndex] !== '' || !gameActive || currentPlayer !== 'X') {
                return;
            }

            makeMove(clickedCellIndex, 'X');
            
            if (gameActive && currentPlayer === 'O') {
                setTimeout(() => {
                    aiMove();
                }, 500);
            }
        }

        // 🎮 FAIRE UN MOUVEMENT
        function makeMove(index, player) {
            board[index] = player;
            document.querySelector(`[data-index="${index}"]`).textContent = player;
            document.querySelector(`[data-index="${index}"]`).classList.add(player.toLowerCase());
            
            if (checkWin()) {
                gameActive = false;
                if (player === 'X') {
                    stats.playerWins++;
                    updateStatus('🎉 Vous avez gagné ! Félicitations !');
                } else {
                    stats.aiWins++;
                    updateStatus('🤖 L\'IA a gagné ! Bien joué à elle !');
                }
                updateStats();
            } else if (board.every(cell => cell !== '')) {
                gameActive = false;
                stats.draws++;
                updateStatus('🤝 Égalité ! Partie serrée !');
                updateStats();
            } else {
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                if (currentPlayer === 'X') {
                    updateStatus('🎯 Votre tour (X) - À vous de jouer !');
                } else {
                    updateStatus('🤖 <span class="ai-thinking">L\'IA réfléchit...</span>');
                }
            }
        }

        // 🤖 MOUVEMENT DE L'IA
        function aiMove() {
            if (!gameActive) return;

            let move;
            
            switch(aiDifficulty) {
                case 'easy':
                    move = getRandomMove();
                    break;
                case 'normal':
                    move = getNormalMove();
                    break;
                case 'hard':
                    move = getHardMove();
                    break;
            }

            if (move !== -1) {
                makeMove(move, 'O');
            }
        }

        // 🎲 IA FACILE - MOUVEMENT ALÉATOIRE
        function getRandomMove() {
            const availableMoves = board.map((cell, index) => cell === '' ? index : null).filter(val => val !== null);
            return availableMoves.length > 0 ? availableMoves[Math.floor(Math.random() * availableMoves.length)] : -1;
        }

        // 🧠 IA NORMALE - STRATÉGIE BASIQUE
        function getNormalMove() {
            // 1. Gagner si possible
            for (let i = 0; i < 9; i++) {
                if (board[i] === '') {
                    board[i] = 'O';
                    if (checkWin()) {
                        board[i] = '';
                        return i;
                    }
                    board[i] = '';
                }
            }

            // 2. Bloquer le joueur
            for (let i = 0; i < 9; i++) {
                if (board[i] === '') {
                    board[i] = 'X';
                    if (checkWin()) {
                        board[i] = '';
                        return i;
                    }
                    board[i] = '';
                }
            }

            // 3. Prendre le centre si libre
            if (board[4] === '') return 4;

            // 4. Mouvement aléatoire
            return getRandomMove();
        }

        // 🧠 IA DIFFICILE - MINIMAX
        function getHardMove() {
            let bestScore = -Infinity;
            let bestMove = -1;

            for (let i = 0; i < 9; i++) {
                if (board[i] === '') {
                    board[i] = 'O';
                    let score = minimax(board, 0, false);
                    board[i] = '';
                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = i;
                    }
                }
            }

            return bestMove;
        }

        // 🧮 ALGORITHME MINIMAX
        function minimax(board, depth, isMaximizing) {
            if (checkWinForPlayer('O')) return 1;
            if (checkWinForPlayer('X')) return -1;
            if (board.every(cell => cell !== '')) return 0;

            if (isMaximizing) {
                let bestScore = -Infinity;
                for (let i = 0; i < 9; i++) {
                    if (board[i] === '') {
                        board[i] = 'O';
                        let score = minimax(board, depth + 1, false);
                        board[i] = '';
                        bestScore = Math.max(score, bestScore);
                    }
                }
                return bestScore;
            } else {
                let bestScore = Infinity;
                for (let i = 0; i < 9; i++) {
                    if (board[i] === '') {
                        board[i] = 'X';
                        let score = minimax(board, depth + 1, true);
                        board[i] = '';
                        bestScore = Math.min(score, bestScore);
                    }
                }
                return bestScore;
            }
        }

        // 🏆 VÉRIFIER LA VICTOIRE
        function checkWin() {
            return winningConditions.some(condition => {
                return condition.every(index => {
                    return board[index] === currentPlayer;
                });
            });
        }

        function checkWinForPlayer(player) {
            return winningConditions.some(condition => {
                return condition.every(index => {
                    return board[index] === player;
                });
            });
        }

        // 🔄 NOUVELLE PARTIE
        function resetGame() {
            board = ['', '', '', '', '', '', '', '', ''];
            currentPlayer = 'X';
            gameActive = true;
            
            document.querySelectorAll('.cell').forEach(cell => {
                cell.textContent = '';
                cell.classList.remove('x', 'o');
            });
            
            updateStatus('🎯 Votre tour (X) - Cliquez sur une case !');
        }

        // ⚙️ CHANGER LA DIFFICULTÉ
        function toggleDifficulty() {
            const difficulties = ['easy', 'normal', 'hard'];
            const names = ['Facile', 'Normale', 'Difficile'];
            const currentIndex = difficulties.indexOf(aiDifficulty);
            const nextIndex = (currentIndex + 1) % difficulties.length;
            
            aiDifficulty = difficulties[nextIndex];
            document.getElementById('difficulty').textContent = names[nextIndex];
        }

        // 📊 METTRE À JOUR L'AFFICHAGE
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }

        function updateStats() {
            document.getElementById('playerWins').textContent = stats.playerWins;
            document.getElementById('aiWins').textContent = stats.aiWins;
            document.getElementById('draws').textContent = stats.draws;
        }
    </script>
</body>
</html>
